{"format": "graph-model", "generatedBy": "https://github.com/610265158/mobilenetv3_centernet", "convertedBy": "https://github.com/vladmandic", "signature": {"inputs": {"tower_0/images": {"name": "tower_0/images", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "512"}, {"size": "512"}, {"size": "3"}]}}}, "outputs": {"tower_0/wh": {"name": "tower_0/wh", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "128"}, {"size": "128"}, {"size": "4"}]}}, "tower_0/keypoints": {"name": "tower_0/keypoints", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "128"}, {"size": "128"}, {"size": "80"}]}}, "tower_0/detections": {"name": "tower_0/detections", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "100"}, {"size": "6"}]}}}}, "modelTopology": {"node": [{"name": "tower_0/mul_3/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/strided_slice_9/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/strided_slice_9/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/strided_slice_9/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/mul_4/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/strided_slice_10/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/strided_slice_10/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/strided_slice_10/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "tower_0/ExpandDims/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/mul_5/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/strided_slice_11/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "tower_0/strided_slice_11/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "tower_0/strided_slice_11/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "tower_0/ExpandDims_1/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/mul_6/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/BatchGather/concat", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "tower_0/strided_slice_6", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "tower_0/BatchGather/Reshape_1/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "tower_0/BatchGather/GatherV2/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/BatchGather/concat_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "tower_0/strided_slice_12/stack", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/strided_slice_12/stack_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/strided_slice_12/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "tower_0/ExpandDims_3/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/Reshape/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/TopKV2/k", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/strided_slice_7", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "tower_0/ExpandDims_2/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/concat_1/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "CenternetHead/centernet_cls_output/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/centernet_cls_output/pointwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "80"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/centernet_cls_output/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "80"}]}}}}}, {"name": "CenternetHead/c2_reverse/3x3/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "18"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/c3_upsample/branch_x_upsample_resize/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "192"}, {"size": "1"}]}}}}}, {"name": "CenternetHead/c3_reverse/3x3/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/c4_upsample/branch_x_upsample_resize/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/c4_reverse/3x3/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "22"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/c5_upsample/branch_x_upsample_resize/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "480"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/extern1/hard_swish/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/mul/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "MobilenetV3/expanded_conv_14/squeeze_excite/Conv/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "720"}, {"size": "184"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_14/squeeze_excite/Conv/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "184"}]}}}}}, {"name": "MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "184"}, {"size": "720"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "720"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/mul/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "MobilenetV3/expanded_conv_13/squeeze_excite/Conv/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "720"}, {"size": "184"}]}}}}}, {"name": "MobilenetV3/expanded_conv_13/squeeze_excite/Conv/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "184"}]}}}}}, {"name": "MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "184"}, {"size": "720"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "720"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/mul/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "MobilenetV3/expanded_conv_12/squeeze_excite/Conv/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "528"}, {"size": "136"}]}}}}}, {"name": "MobilenetV3/expanded_conv_12/squeeze_excite/Conv/biases", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "136"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "136"}, {"size": "528"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "528"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/mul/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "MobilenetV3/expanded_conv_11/squeeze_excite/Conv/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "528"}, {"size": "136"}]}}}}}, {"name": "MobilenetV3/expanded_conv_11/squeeze_excite/Conv/biases", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "136"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "136"}, {"size": "528"}]}}}}}, {"name": "MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "528"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/mul/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "MobilenetV3/expanded_conv_10/squeeze_excite/Conv/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_10/squeeze_excite/Conv/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "384"}]}}}}}, {"name": "MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/biases", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/mul_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/mul/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "MobilenetV3/expanded_conv_5/squeeze_excite/Conv/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_5/squeeze_excite/Conv/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/biases", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/mul/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "MobilenetV3/expanded_conv_4/squeeze_excite/Conv/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_4/squeeze_excite/Conv/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/mul/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "MobilenetV3/expanded_conv_3/squeeze_excite/Conv/weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "72"}, {"size": "24"}]}}}}}, {"name": "MobilenetV3/expanded_conv_3/squeeze_excite/Conv/biases", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/biases", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/Conv/hard_swish/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/images", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "1"}, {"size": "512"}, {"size": "512"}, {"size": "3"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/Conv/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/extern1/hard_swish/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/c5_upsample/branch_y_upsample_resize/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "480"}, {"size": "1"}]}}}}}, {"name": "tower_0/CenternetHead/c5_upsample/up_sampling2d/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/CenternetHead/concat/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "CenternetHead/c4_upsample/branch_y_upsample_resize/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c4_upsample/up_sampling2d_1/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/CenternetHead/concat_1/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "CenternetHead/c3_upsample/branch_y_upsample_resize/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "192"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c3_upsample/up_sampling2d_2/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/CenternetHead/concat_2/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "tower_0/CenternetHead/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "CenternetHead/conv1x1_se_b/weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/centernet_wh_output/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/centernet_wh_output/pointwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "4"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "CenternetHead/centernet_wh_output/biases", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/mul/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "tower_0/MobilenetV3/Conv/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/separable_conv2d_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "96"}]}}}}}, {"name": "tower_0/MobilenetV3/Conv/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv/project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "64"}]}}}}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/separable_conv2d_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "72"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "72"}, {"size": "24"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "72"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}}}, {"name": "tower_0/CenternetHead/c2_reverse/1x1/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "72"}, {"size": "18"}]}}}}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/separable_conv2d_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "64"}]}}}}}, {"name": "tower_0/CenternetHead/c2_reverse/1x1/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "18"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "72"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/separable_conv2d_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/separable_conv2d_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c2_reverse/3x3/separable_conv2d_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "18"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c2_reverse/3x3/separable_conv2d_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "72"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/separable_conv2d_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "96"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "32"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/conv1x1_se_a/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "32"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "tower_0/CenternetHead/c3_reverse/1x1/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}}}, {"name": "tower_0/CenternetHead/conv1x1_se_a/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c3_reverse/1x1/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "96"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c3_reverse/3x3/separable_conv2d_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}}}, {"name": "tower_0/CenternetHead/c3_reverse/3x3/separable_conv2d_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "192"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "160"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "160"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "160"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "160"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "160"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "144"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "144"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "144"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "144"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "144"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "144"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "384"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "88"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "88"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "88"}, {"size": "528"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "528"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "528"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "528"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "528"}, {"size": "88"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "88"}]}}}}}, {"name": "tower_0/CenternetHead/c4_reverse/1x1/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "88"}, {"size": "22"}]}}}}}, {"name": "tower_0/CenternetHead/c4_reverse/1x1/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "22"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "88"}, {"size": "528"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "528"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c4_reverse/3x3/separable_conv2d_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "22"}, {"size": "128"}]}}}}}, {"name": "tower_0/CenternetHead/c4_reverse/3x3/separable_conv2d_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "528"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "528"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "528"}, {"size": "120"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "120"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "120"}, {"size": "720"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "720"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "720"}, {"size": "1"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "720"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "720"}, {"size": "120"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "120"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "120"}, {"size": "720"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "720"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "720"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "720"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "720"}, {"size": "120"}]}}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "120"}]}}}}}, {"name": "tower_0/extern1/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "120"}, {"size": "480"}]}}}}}, {"name": "tower_0/extern1/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "480"}]}}}}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/separable_conv2d_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "480"}, {"size": "128"}]}}}}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/separable_conv2d_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/separable_conv2d_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "480"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/separable_conv2d_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/separable_conv2d_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/separable_conv2d_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/Conv/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/images", "tower_0/MobilenetV3/Conv/Conv2D_weights", "tower_0/MobilenetV3/Conv/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/Conv/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/Conv/hard_swish/mul_1/y", "tower_0/MobilenetV3/Conv/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/Conv/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/Conv/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/Conv/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/Conv/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/Conv/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/Conv/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/Conv/hard_swish/mul", "tower_0/MobilenetV3/Conv/hard_swish/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/Conv/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv/depthwise/depthwise_bn_offset"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}}}, {"name": "tower_0/MobilenetV3/expanded_conv/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/Conv/hard_swish/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/expand/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv/add", "tower_0/MobilenetV3/expanded_conv_1/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_1/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_1/expand/Relu", "tower_0/MobilenetV3/expanded_conv_1/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_1/depthwise/depthwise_bn_offset"], "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_1/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_1/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_1/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/expand/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_1/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_2/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_2/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_2/expand/Relu", "tower_0/MobilenetV3/expanded_conv_2/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_2/depthwise/depthwise_bn_offset"], "attr": {"data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_2/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_2/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_2/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_2/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_1/project/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/expand/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_2/add", "tower_0/MobilenetV3/expanded_conv_3/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_3/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "tower_0/CenternetHead/c2_reverse/1x1/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_3/expand/Relu", "tower_0/CenternetHead/c2_reverse/1x1/Conv2D_weights", "tower_0/CenternetHead/c2_reverse/1x1/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_3/expand/Relu", "tower_0/MobilenetV3/expanded_conv_3/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_3/depthwise/depthwise_bn_offset"], "attr": {"num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "tower_0/CenternetHead/c2_reverse/3x3/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/CenternetHead/c2_reverse/1x1/Relu", "CenternetHead/c2_reverse/3x3/depthwise_weights"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "tower_0/CenternetHead/c2_reverse/3x3/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/c2_reverse/3x3/separable_conv2d/depthwise", "tower_0/CenternetHead/c2_reverse/3x3/separable_conv2d_weights", "tower_0/CenternetHead/c2_reverse/3x3/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Mean", "op": "Mean", "input": ["tower_0/MobilenetV3/expanded_conv_3/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Mean/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Mean", "MobilenetV3/expanded_conv_3/squeeze_excite/Conv/weights", "MobilenetV3/expanded_conv_3/squeeze_excite/Conv/biases"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv/Relu", "MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/weights", "MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/biases"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/BiasAdd", "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/mul/y", "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/mul", "tower_0/MobilenetV3/expanded_conv_3/depthwise/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/mul", "tower_0/MobilenetV3/expanded_conv_3/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_3/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/expand/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_3/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_4/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_4/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_4/expand/Relu", "tower_0/MobilenetV3/expanded_conv_4/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_4/depthwise/depthwise_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Mean", "op": "Mean", "input": ["tower_0/MobilenetV3/expanded_conv_4/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Mean/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Mean", "MobilenetV3/expanded_conv_4/squeeze_excite/Conv/weights", "MobilenetV3/expanded_conv_4/squeeze_excite/Conv/biases"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv/Relu", "MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/weights", "MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/biases"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/BiasAdd", "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/mul/y", "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/mul", "tower_0/MobilenetV3/expanded_conv_4/depthwise/depthwise"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/mul", "tower_0/MobilenetV3/expanded_conv_4/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_4/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_4/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_3/project/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/expand/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_4/add", "tower_0/MobilenetV3/expanded_conv_5/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_5/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "tower_0/CenternetHead/c3_reverse/1x1/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_5/expand/Relu", "tower_0/CenternetHead/c3_reverse/1x1/Conv2D_weights", "tower_0/CenternetHead/c3_reverse/1x1/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_5/expand/Relu", "tower_0/MobilenetV3/expanded_conv_5/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_5/depthwise/depthwise_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "tower_0/CenternetHead/c3_reverse/3x3/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/CenternetHead/c3_reverse/1x1/Relu", "CenternetHead/c3_reverse/3x3/depthwise_weights"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/CenternetHead/c3_reverse/3x3/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/c3_reverse/3x3/separable_conv2d/depthwise", "tower_0/CenternetHead/c3_reverse/3x3/separable_conv2d_weights", "tower_0/CenternetHead/c3_reverse/3x3/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Mean", "op": "Mean", "input": ["tower_0/MobilenetV3/expanded_conv_5/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Mean", "MobilenetV3/expanded_conv_5/squeeze_excite/Conv/weights", "MobilenetV3/expanded_conv_5/squeeze_excite/Conv/biases"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv/Relu", "MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/weights", "MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/biases"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/BiasAdd", "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/mul/y", "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/mul", "tower_0/MobilenetV3/expanded_conv_5/depthwise/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/mul", "tower_0/MobilenetV3/expanded_conv_5/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_5/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_5/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_4/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_5/add", "tower_0/MobilenetV3/expanded_conv_6/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_6/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_6/expand/BatchNorm/FusedBatchNorm"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_6/expand/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_6/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_6/depthwise/depthwise_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_6/depthwise/depthwise"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_6/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_6/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_6/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_6/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_7/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_7/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_7/expand/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_7/expand/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_7/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_7/depthwise/depthwise_bn_offset"], "attr": {"data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_7/depthwise/depthwise"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_7/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_7/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_7/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_7/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_6/project/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_7/add", "tower_0/MobilenetV3/expanded_conv_8/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_8/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_8/expand/BatchNorm/FusedBatchNorm"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_8/expand/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_8/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_8/depthwise/depthwise_bn_offset"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_8/depthwise/depthwise"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_8/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_8/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_8/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_8/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_7/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_8/add", "tower_0/MobilenetV3/expanded_conv_9/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_9/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_9/expand/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_9/expand/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_9/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_9/depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_9/depthwise/depthwise"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_9/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_9/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_9/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_9/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_8/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_9/add", "tower_0/MobilenetV3/expanded_conv_10/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_10/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_10/expand/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_10/expand/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_10/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_10/depthwise/depthwise_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_10/depthwise/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_10/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Mean", "op": "Mean", "input": ["tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Mean/reduction_indices"], "attr": {"keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Mean", "MobilenetV3/expanded_conv_10/squeeze_excite/Conv/weights", "MobilenetV3/expanded_conv_10/squeeze_excite/Conv/biases"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv/Relu", "MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/weights", "MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/biases"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/BiasAdd", "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/mul/y", "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/mul", "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/mul", "tower_0/MobilenetV3/expanded_conv_10/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_10/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_10/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_11/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_11/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_11/expand/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_11/expand/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_11/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_11/depthwise/depthwise_bn_offset"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_11/depthwise/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_11/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Mean", "op": "Mean", "input": ["tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Mean", "MobilenetV3/expanded_conv_11/squeeze_excite/Conv/weights", "MobilenetV3/expanded_conv_11/squeeze_excite/Conv/biases"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv/Relu", "MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/weights", "MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/biases"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/BiasAdd", "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/mul/y", "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/mul", "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/mul", "tower_0/MobilenetV3/expanded_conv_11/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_11/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_11/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_10/project/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c4_reverse/1x1/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_11/add", "tower_0/CenternetHead/c4_reverse/1x1/Conv2D_weights", "tower_0/CenternetHead/c4_reverse/1x1/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_11/add", "tower_0/MobilenetV3/expanded_conv_12/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_12/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/CenternetHead/c4_reverse/3x3/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/CenternetHead/c4_reverse/1x1/Relu", "CenternetHead/c4_reverse/3x3/depthwise_weights"], "attr": {"data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_12/expand/BatchNorm/FusedBatchNorm"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_12/expand/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c4_reverse/3x3/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/c4_reverse/3x3/separable_conv2d/depthwise", "tower_0/CenternetHead/c4_reverse/3x3/separable_conv2d_weights", "tower_0/CenternetHead/c4_reverse/3x3/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_12/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_12/depthwise/depthwise_bn_offset"], "attr": {"num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_12/depthwise/depthwise"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_12/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Mean", "op": "Mean", "input": ["tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Mean/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Mean", "MobilenetV3/expanded_conv_12/squeeze_excite/Conv/weights", "MobilenetV3/expanded_conv_12/squeeze_excite/Conv/biases"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv/Relu", "MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/weights", "MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/biases"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/BiasAdd", "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/mul/y", "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/mul", "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/mul", "tower_0/MobilenetV3/expanded_conv_12/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_12/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_12/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_13/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_13/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_13/expand/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_13/expand/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_13/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_13/depthwise/depthwise_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_13/depthwise/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_13/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Mean", "op": "Mean", "input": ["tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Mean/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Mean", "MobilenetV3/expanded_conv_13/squeeze_excite/Conv/weights", "MobilenetV3/expanded_conv_13/squeeze_excite/Conv/biases"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv/Relu", "MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/weights", "MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/biases"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/BiasAdd", "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/mul/y", "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/mul", "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/mul_1"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/mul", "tower_0/MobilenetV3/expanded_conv_13/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_13/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_13/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_12/project/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_13/add", "tower_0/MobilenetV3/expanded_conv_14/expand/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_14/expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_14/expand/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_14/expand/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_14/depthwise/depthwise_weights", "tower_0/MobilenetV3/expanded_conv_14/depthwise/depthwise_bn_offset"], "attr": {"padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/mul_1/y", "tower_0/MobilenetV3/expanded_conv_14/depthwise/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_14/depthwise/depthwise", "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/mul", "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Mean", "op": "Mean", "input": ["tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/mul_1", "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Mean/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv/Relu", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Mean", "MobilenetV3/expanded_conv_14/squeeze_excite/Conv/weights", "MobilenetV3/expanded_conv_14/squeeze_excite/Conv/biases"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv/Relu", "MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/weights", "MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/biases"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/BiasAdd", "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/Relu6", "op": "Relu6", "input": ["tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/mul/y", "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/mul", "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/mul_1"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/project/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/mul", "tower_0/MobilenetV3/expanded_conv_14/project/Conv2D_weights", "tower_0/MobilenetV3/expanded_conv_14/project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/add", "op": "Add", "input": ["tower_0/MobilenetV3/expanded_conv_14/project/BatchNorm/FusedBatchNorm", "tower_0/MobilenetV3/expanded_conv_13/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/extern1/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/MobilenetV3/expanded_conv_14/add", "tower_0/extern1/Conv2D_weights", "tower_0/extern1/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "tower_0/extern1/hard_swish/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/extern1/hard_swish/mul_1/y", "tower_0/extern1/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/extern1/hard_swish/add", "op": "Add", "input": ["tower_0/extern1/BatchNorm/FusedBatchNorm", "tower_0/extern1/hard_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/extern1/hard_swish/Relu6", "op": "Relu6", "input": ["tower_0/extern1/hard_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/extern1/hard_swish/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/extern1/hard_swish/mul", "tower_0/extern1/hard_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/extern1/hard_swish/mul_1", "CenternetHead/c5_upsample/branch_x_upsample_resize/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/extern1/hard_swish/mul_1", "CenternetHead/c5_upsample/branch_y_upsample_resize/depthwise_weights"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/separable_conv2d/depthwise", "tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/separable_conv2d_weights", "tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/separable_conv2d/depthwise", "tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/separable_conv2d_weights", "tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/CenternetHead/c5_upsample/add", "op": "Add", "input": ["tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/BatchNorm/FusedBatchNorm", "tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c5_upsample/up_sampling2d/ResizeBilinear", "op": "ResizeBilinear", "input": ["tower_0/CenternetHead/c5_upsample/add", "tower_0/CenternetHead/c5_upsample/up_sampling2d/mul"], "attr": {"align_corners": {"b": false}, "T": {"type": "DT_FLOAT"}, "half_pixel_centers": {"b": false}}}, {"name": "tower_0/CenternetHead/concat", "op": "ConcatV2", "input": ["tower_0/CenternetHead/c4_reverse/3x3/BatchNorm/FusedBatchNorm", "tower_0/CenternetHead/c5_upsample/up_sampling2d/ResizeBilinear", "tower_0/CenternetHead/concat/axis"], "attr": {"N": {"i": "2"}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/Relu", "op": "<PERSON><PERSON>", "input": ["tower_0/CenternetHead/concat"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/CenternetHead/Relu", "CenternetHead/c4_upsample/branch_x_upsample_resize/depthwise_weights"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/CenternetHead/Relu", "CenternetHead/c4_upsample/branch_y_upsample_resize/depthwise_weights"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/separable_conv2d/depthwise", "tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/separable_conv2d_weights", "tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/separable_conv2d/depthwise", "tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/separable_conv2d_weights", "tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}}}, {"name": "tower_0/CenternetHead/c4_upsample/add", "op": "Add", "input": ["tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/BatchNorm/FusedBatchNorm", "tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c4_upsample/up_sampling2d_1/ResizeBilinear", "op": "ResizeBilinear", "input": ["tower_0/CenternetHead/c4_upsample/add", "tower_0/CenternetHead/c4_upsample/up_sampling2d_1/mul"], "attr": {"align_corners": {"b": false}, "half_pixel_centers": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/concat_1", "op": "ConcatV2", "input": ["tower_0/CenternetHead/c3_reverse/3x3/BatchNorm/FusedBatchNorm", "tower_0/CenternetHead/c4_upsample/up_sampling2d_1/ResizeBilinear", "tower_0/CenternetHead/concat_1/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "N": {"i": "2"}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/Relu_1", "op": "<PERSON><PERSON>", "input": ["tower_0/CenternetHead/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/CenternetHead/Relu_1", "CenternetHead/c3_upsample/branch_x_upsample_resize/depthwise_weights"], "attr": {"data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/CenternetHead/Relu_1", "CenternetHead/c3_upsample/branch_y_upsample_resize/depthwise_weights"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/separable_conv2d/depthwise", "tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/separable_conv2d_weights", "tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/BatchNorm/FusedBatchNorm", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/separable_conv2d/depthwise", "tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/separable_conv2d_weights", "tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}}}, {"name": "tower_0/CenternetHead/c3_upsample/add", "op": "Add", "input": ["tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/BatchNorm/FusedBatchNorm", "tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/BatchNorm/FusedBatchNorm"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/c3_upsample/up_sampling2d_2/ResizeBilinear", "op": "ResizeBilinear", "input": ["tower_0/CenternetHead/c3_upsample/add", "tower_0/CenternetHead/c3_upsample/up_sampling2d_2/mul"], "attr": {"align_corners": {"b": false}, "T": {"type": "DT_FLOAT"}, "half_pixel_centers": {"b": false}}}, {"name": "tower_0/CenternetHead/concat_2", "op": "ConcatV2", "input": ["tower_0/CenternetHead/c2_reverse/3x3/BatchNorm/FusedBatchNorm", "tower_0/CenternetHead/c3_upsample/up_sampling2d_2/ResizeBilinear", "tower_0/CenternetHead/concat_2/axis"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "tower_0/CenternetHead/Relu_2", "op": "<PERSON><PERSON>", "input": ["tower_0/CenternetHead/concat_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/Mean", "op": "Mean", "input": ["tower_0/CenternetHead/Relu_2", "tower_0/CenternetHead/Mean/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}}}, {"name": "tower_0/CenternetHead/conv1x1_se_a/Relu", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/Mean", "tower_0/CenternetHead/conv1x1_se_a/Conv2D_weights", "tower_0/CenternetHead/conv1x1_se_a/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/conv1x1_se_b/Conv2D", "op": "Conv2D", "input": ["tower_0/CenternetHead/conv1x1_se_a/Relu", "CenternetHead/conv1x1_se_b/weights"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "tower_0/CenternetHead/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["tower_0/CenternetHead/conv1x1_se_b/Conv2D"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/CenternetHead/Relu_2", "tower_0/CenternetHead/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/CenternetHead/centernet_cls_output/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/CenternetHead/mul", "CenternetHead/centernet_cls_output/depthwise_weights"], "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "tower_0/CenternetHead/centernet_wh_output/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["tower_0/CenternetHead/mul", "CenternetHead/centernet_wh_output/depthwise_weights"], "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "tower_0/CenternetHead/centernet_cls_output/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/centernet_cls_output/separable_conv2d/depthwise", "CenternetHead/centernet_cls_output/pointwise_weights", "CenternetHead/centernet_cls_output/biases"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/CenternetHead/centernet_wh_output/BiasAdd", "op": "_FusedConv2D", "input": ["tower_0/CenternetHead/centernet_wh_output/separable_conv2d/depthwise", "CenternetHead/centernet_wh_output/pointwise_weights", "CenternetHead/centernet_wh_output/biases"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "tower_0/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["tower_0/CenternetHead/centernet_cls_output/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/mul", "op": "<PERSON><PERSON>", "input": ["tower_0/CenternetHead/centernet_wh_output/BiasAdd", "tower_0/mul/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/keypoints", "op": "Identity", "input": ["tower_0/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/wh", "op": "Identity", "input": ["tower_0/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/max_pooling2d/MaxPool", "op": "MaxPool", "input": ["tower_0/keypoints"], "attr": {"explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "ksize": {"list": {"i": ["1", "3", "3", "1"]}}}}, {"name": "tower_0/BatchGather/Reshape_2", "op": "Reshape", "input": ["tower_0/wh", "tower_0/BatchGather/concat"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/Equal", "op": "Equal", "input": ["tower_0/keypoints", "tower_0/max_pooling2d/MaxPool"], "attr": {"incompatible_shape_error": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/Cast", "op": "Cast", "input": ["tower_0/Equal"], "attr": {"SrcT": {"type": "DT_BOOL"}, "DstT": {"type": "DT_FLOAT"}, "Truncate": {"b": false}}}, {"name": "tower_0/mul_1", "op": "<PERSON><PERSON>", "input": ["tower_0/keypoints", "tower_0/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/Reshape", "op": "Reshape", "input": ["tower_0/mul_1", "tower_0/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "tower_0/TopKV2", "op": "TopKV2", "input": ["tower_0/Reshape", "tower_0/TopKV2/k"], "attr": {"sorted": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/ExpandDims_3", "op": "ExpandDims", "input": ["tower_0/TopKV2", "tower_0/ExpandDims_3/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/floordiv_1", "op": "FloorDiv", "input": ["tower_0/TopKV2:1", "tower_0/strided_slice_7"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "tower_0/floordiv", "op": "FloorDiv", "input": ["tower_0/TopKV2:1", "tower_0/strided_slice_7"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "tower_0/mod", "op": "FloorMod", "input": ["tower_0/TopKV2:1", "tower_0/strided_slice_7"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "tower_0/floordiv_2", "op": "FloorDiv", "input": ["tower_0/floordiv_1", "tower_0/strided_slice_6"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "tower_0/mod_1", "op": "FloorMod", "input": ["tower_0/floordiv", "tower_0/strided_slice_6"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "tower_0/ExpandDims_2", "op": "ExpandDims", "input": ["tower_0/mod", "tower_0/ExpandDims_2/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "tower_0/ExpandDims_1", "op": "ExpandDims", "input": ["tower_0/floordiv_2", "tower_0/ExpandDims_1/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "tower_0/mul_2", "op": "<PERSON><PERSON>", "input": ["tower_0/floordiv_2", "tower_0/strided_slice_6"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "tower_0/ExpandDims", "op": "ExpandDims", "input": ["tower_0/mod_1", "tower_0/ExpandDims/dim"], "attr": {"T": {"type": "DT_INT32"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "tower_0/Cast_3", "op": "Cast", "input": ["tower_0/ExpandDims_2"], "attr": {"Truncate": {"b": false}, "DstT": {"type": "DT_FLOAT"}, "SrcT": {"type": "DT_INT32"}}}, {"name": "tower_0/Cast_2", "op": "Cast", "input": ["tower_0/ExpandDims_1"], "attr": {"SrcT": {"type": "DT_INT32"}, "Truncate": {"b": false}, "DstT": {"type": "DT_FLOAT"}}}, {"name": "tower_0/BatchGather/add", "op": "Add", "input": ["tower_0/mul_2", "tower_0/mod_1"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "tower_0/Cast_1", "op": "Cast", "input": ["tower_0/ExpandDims"], "attr": {"DstT": {"type": "DT_FLOAT"}, "SrcT": {"type": "DT_INT32"}, "Truncate": {"b": false}}}, {"name": "tower_0/mul_4", "op": "<PERSON><PERSON>", "input": ["tower_0/Cast_2", "tower_0/mul_4/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/mul_6", "op": "<PERSON><PERSON>", "input": ["tower_0/Cast_2", "tower_0/mul_6/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/BatchGather/Reshape_1", "op": "Reshape", "input": ["tower_0/BatchGather/add", "tower_0/BatchGather/Reshape_1/shape"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "tower_0/mul_3", "op": "<PERSON><PERSON>", "input": ["tower_0/Cast_1", "tower_0/mul_3/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/mul_5", "op": "<PERSON><PERSON>", "input": ["tower_0/Cast_1", "tower_0/mul_5/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/BatchGather/GatherV2", "op": "GatherV2", "input": ["tower_0/BatchGather/Reshape_2", "tower_0/BatchGather/Reshape_1", "tower_0/BatchGather/GatherV2/axis"], "attr": {"Taxis": {"type": "DT_INT32"}, "batch_dims": {"i": "0"}, "Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_FLOAT"}}}, {"name": "tower_0/BatchGather/Reshape_3", "op": "Reshape", "input": ["tower_0/BatchGather/GatherV2", "tower_0/BatchGather/concat_1"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/strided_slice_11", "op": "StridedSlice", "input": ["tower_0/BatchGather/Reshape_3", "tower_0/strided_slice_11/stack", "tower_0/strided_slice_11/stack_1", "tower_0/strided_slice_11/stack_2"], "attr": {"begin_mask": {"i": "3"}, "shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "end_mask": {"i": "3"}, "T": {"type": "DT_FLOAT"}, "Index": {"type": "DT_INT32"}, "ellipsis_mask": {"i": "0"}}}, {"name": "tower_0/strided_slice_12", "op": "StridedSlice", "input": ["tower_0/BatchGather/Reshape_3", "tower_0/strided_slice_12/stack", "tower_0/strided_slice_12/stack_1", "tower_0/strided_slice_12/stack_2"], "attr": {"end_mask": {"i": "3"}, "Index": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "begin_mask": {"i": "3"}, "ellipsis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}}}, {"name": "tower_0/strided_slice_9", "op": "StridedSlice", "input": ["tower_0/BatchGather/Reshape_3", "tower_0/strided_slice_9/stack", "tower_0/strided_slice_9/stack_1", "tower_0/strided_slice_9/stack_2"], "attr": {"ellipsis_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "Index": {"type": "DT_INT32"}, "end_mask": {"i": "3"}, "shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "3"}}}, {"name": "tower_0/strided_slice_10", "op": "StridedSlice", "input": ["tower_0/BatchGather/Reshape_3", "tower_0/strided_slice_10/stack", "tower_0/strided_slice_10/stack_1", "tower_0/strided_slice_10/stack_2"], "attr": {"end_mask": {"i": "3"}, "new_axis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "begin_mask": {"i": "3"}, "ellipsis_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "Index": {"type": "DT_INT32"}}}, {"name": "tower_0/add_1", "op": "Add", "input": ["tower_0/mul_5", "tower_0/strided_slice_11"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/add_2", "op": "Add", "input": ["tower_0/mul_6", "tower_0/strided_slice_12"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/sub", "op": "Sub", "input": ["tower_0/mul_3", "tower_0/strided_slice_9"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/sub_1", "op": "Sub", "input": ["tower_0/mul_4", "tower_0/strided_slice_10"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/concat_1", "op": "ConcatV2", "input": ["tower_0/sub", "tower_0/sub_1", "tower_0/add_1", "tower_0/add_2", "tower_0/ExpandDims_3", "tower_0/Cast_3", "tower_0/concat_1/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "N": {"i": "6"}, "T": {"type": "DT_FLOAT"}}}, {"name": "tower_0/detections", "op": "Identity", "input": ["tower_0/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {}}, "weightsManifest": [{"paths": ["centernet.bin"], "weights": [{"name": "tower_0/mul_3/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/strided_slice_9/stack", "shape": [3], "dtype": "int32"}, {"name": "tower_0/strided_slice_9/stack_1", "shape": [3], "dtype": "int32"}, {"name": "tower_0/strided_slice_9/stack_2", "shape": [3], "dtype": "int32"}, {"name": "tower_0/mul_4/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/strided_slice_10/stack", "shape": [3], "dtype": "int32"}, {"name": "tower_0/strided_slice_10/stack_1", "shape": [3], "dtype": "int32"}, {"name": "tower_0/strided_slice_10/stack_2", "shape": [3], "dtype": "int32"}, {"name": "tower_0/ExpandDims/dim", "shape": [], "dtype": "int32"}, {"name": "tower_0/mul_5/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/strided_slice_11/stack", "shape": [3], "dtype": "int32"}, {"name": "tower_0/strided_slice_11/stack_1", "shape": [3], "dtype": "int32"}, {"name": "tower_0/strided_slice_11/stack_2", "shape": [3], "dtype": "int32"}, {"name": "tower_0/ExpandDims_1/dim", "shape": [], "dtype": "int32"}, {"name": "tower_0/mul_6/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/BatchGather/concat", "shape": [2], "dtype": "int32"}, {"name": "tower_0/strided_slice_6", "shape": [], "dtype": "int32"}, {"name": "tower_0/BatchGather/Reshape_1/shape", "shape": [1], "dtype": "int32"}, {"name": "tower_0/BatchGather/GatherV2/axis", "shape": [], "dtype": "int32"}, {"name": "tower_0/BatchGather/concat_1", "shape": [3], "dtype": "int32"}, {"name": "tower_0/strided_slice_12/stack", "shape": [3], "dtype": "int32"}, {"name": "tower_0/strided_slice_12/stack_1", "shape": [3], "dtype": "int32"}, {"name": "tower_0/strided_slice_12/stack_2", "shape": [3], "dtype": "int32"}, {"name": "tower_0/ExpandDims_3/dim", "shape": [], "dtype": "int32"}, {"name": "tower_0/Reshape/shape", "shape": [2], "dtype": "int32"}, {"name": "tower_0/TopKV2/k", "shape": [], "dtype": "int32"}, {"name": "tower_0/strided_slice_7", "shape": [], "dtype": "int32"}, {"name": "tower_0/ExpandDims_2/dim", "shape": [], "dtype": "int32"}, {"name": "tower_0/concat_1/axis", "shape": [], "dtype": "int32"}, {"name": "CenternetHead/centernet_cls_output/depthwise_weights", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/centernet_cls_output/pointwise_weights", "shape": [1, 1, 128, 80], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/centernet_cls_output/biases", "shape": [80], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/c2_reverse/3x3/depthwise_weights", "shape": [5, 5, 18, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/c3_upsample/branch_x_upsample_resize/depthwise_weights", "shape": [3, 3, 192, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/c3_reverse/3x3/depthwise_weights", "shape": [5, 5, 24, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/c4_upsample/branch_x_upsample_resize/depthwise_weights", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/c4_reverse/3x3/depthwise_weights", "shape": [5, 5, 22, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/c5_upsample/branch_x_upsample_resize/depthwise_weights", "shape": [3, 3, 480, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/extern1/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/mul/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "MobilenetV3/expanded_conv_14/squeeze_excite/Conv/weights", "shape": [1, 1, 720, 184], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_14/squeeze_excite/Conv/biases", "shape": [184], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/weights", "shape": [1, 1, 184, 720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/biases", "shape": [720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/squeeze_excite/Conv_1/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/mul/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "MobilenetV3/expanded_conv_13/squeeze_excite/Conv/weights", "shape": [1, 1, 720, 184], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_13/squeeze_excite/Conv/biases", "shape": [184], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/weights", "shape": [1, 1, 184, 720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/biases", "shape": [720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/squeeze_excite/Conv_1/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/mul/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "MobilenetV3/expanded_conv_12/squeeze_excite/Conv/weights", "shape": [1, 1, 528, 136], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_12/squeeze_excite/Conv/biases", "shape": [136], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/weights", "shape": [1, 1, 136, 528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/biases", "shape": [528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/squeeze_excite/Conv_1/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/mul/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "MobilenetV3/expanded_conv_11/squeeze_excite/Conv/weights", "shape": [1, 1, 528, 136], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_11/squeeze_excite/Conv/biases", "shape": [136], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/weights", "shape": [1, 1, 136, 528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/biases", "shape": [528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/squeeze_excite/Conv_1/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/mul/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "MobilenetV3/expanded_conv_10/squeeze_excite/Conv/weights", "shape": [1, 1, 384, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_10/squeeze_excite/Conv/biases", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/weights", "shape": [1, 1, 96, 384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/biases", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/squeeze_excite/Conv_1/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/mul/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "MobilenetV3/expanded_conv_5/squeeze_excite/Conv/weights", "shape": [1, 1, 96, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_5/squeeze_excite/Conv/biases", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/weights", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/biases", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/squeeze_excite/Conv_1/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/mul/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "MobilenetV3/expanded_conv_4/squeeze_excite/Conv/weights", "shape": [1, 1, 96, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_4/squeeze_excite/Conv/biases", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/weights", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/biases", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/squeeze_excite/Conv_1/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/mul/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "MobilenetV3/expanded_conv_3/squeeze_excite/Conv/weights", "shape": [1, 1, 72, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_3/squeeze_excite/Conv/biases", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/weights", "shape": [1, 1, 24, 72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/biases", "shape": [72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/squeeze_excite/Conv_1/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/Conv/hard_swish/mul_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/Conv/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/extern1/hard_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/c5_upsample/branch_y_upsample_resize/depthwise_weights", "shape": [5, 5, 480, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c5_upsample/up_sampling2d/mul", "shape": [2], "dtype": "int32"}, {"name": "tower_0/CenternetHead/concat/axis", "shape": [], "dtype": "int32"}, {"name": "CenternetHead/c4_upsample/branch_y_upsample_resize/depthwise_weights", "shape": [5, 5, 256, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c4_upsample/up_sampling2d_1/mul", "shape": [2], "dtype": "int32"}, {"name": "tower_0/CenternetHead/concat_1/axis", "shape": [], "dtype": "int32"}, {"name": "CenternetHead/c3_upsample/branch_y_upsample_resize/depthwise_weights", "shape": [5, 5, 192, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c3_upsample/up_sampling2d_2/mul", "shape": [2], "dtype": "int32"}, {"name": "tower_0/CenternetHead/concat_2/axis", "shape": [], "dtype": "int32"}, {"name": "tower_0/CenternetHead/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "CenternetHead/conv1x1_se_b/weights", "shape": [1, 1, 32, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/centernet_wh_output/depthwise_weights", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/centernet_wh_output/pointwise_weights", "shape": [1, 1, 128, 4], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "CenternetHead/centernet_wh_output/biases", "shape": [4], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/mul/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/Conv/Conv2D_weights", "shape": [3, 3, 3, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/separable_conv2d_weights", "shape": [1, 1, 256, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/Conv/Conv2D_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv/depthwise/depthwise_weights", "shape": [3, 3, 16, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv/depthwise/depthwise_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv/project/Conv2D_weights", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv/project/Conv2D_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/expand/Conv2D_weights", "shape": [1, 1, 16, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_y_upsample_resize/separable_conv2d_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/expand/Conv2D_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/depthwise/depthwise_weights", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/depthwise/depthwise_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/project/Conv2D_weights", "shape": [1, 1, 64, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_1/project/Conv2D_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/expand/Conv2D_weights", "shape": [1, 1, 24, 72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/expand/Conv2D_bn_offset", "shape": [72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/depthwise/depthwise_weights", "shape": [3, 3, 72, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/depthwise/depthwise_bn_offset", "shape": [72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/project/Conv2D_weights", "shape": [1, 1, 72, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_2/project/Conv2D_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/expand/Conv2D_weights", "shape": [1, 1, 24, 72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/expand/Conv2D_bn_offset", "shape": [72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c2_reverse/1x1/Conv2D_weights", "shape": [1, 1, 72, 18], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/separable_conv2d_weights", "shape": [1, 1, 192, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c2_reverse/1x1/Conv2D_bn_offset", "shape": [18], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/depthwise/depthwise_weights", "shape": [5, 5, 72, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/depthwise/depthwise_bn_offset", "shape": [72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_x_upsample_resize/separable_conv2d_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/separable_conv2d_weights", "shape": [1, 1, 192, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c2_reverse/3x3/separable_conv2d_weights", "shape": [1, 1, 18, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c2_reverse/3x3/separable_conv2d_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/project/Conv2D_weights", "shape": [1, 1, 72, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c3_upsample/branch_y_upsample_resize/separable_conv2d_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_3/project/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/expand/Conv2D_weights", "shape": [1, 1, 32, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/expand/Conv2D_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/depthwise/depthwise_weights", "shape": [5, 5, 96, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/depthwise/depthwise_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/project/Conv2D_weights", "shape": [1, 1, 96, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_4/project/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/expand/Conv2D_weights", "shape": [1, 1, 32, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/conv1x1_se_a/Conv2D_weights", "shape": [1, 1, 128, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/expand/Conv2D_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c3_reverse/1x1/Conv2D_weights", "shape": [1, 1, 96, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/conv1x1_se_a/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c3_reverse/1x1/Conv2D_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/depthwise/depthwise_weights", "shape": [5, 5, 96, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/depthwise/depthwise_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c3_reverse/3x3/separable_conv2d_weights", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c3_reverse/3x3/separable_conv2d_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/project/Conv2D_weights", "shape": [1, 1, 96, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_5/project/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/Conv2D_weights", "shape": [1, 1, 32, 192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/expand/Conv2D_bn_offset", "shape": [192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/depthwise_weights", "shape": [3, 3, 192, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/depthwise/depthwise_bn_offset", "shape": [192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/project/Conv2D_weights", "shape": [1, 1, 192, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_6/project/Conv2D_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/Conv2D_weights", "shape": [1, 1, 64, 160], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/expand/Conv2D_bn_offset", "shape": [160], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/depthwise_weights", "shape": [3, 3, 160, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/depthwise/depthwise_bn_offset", "shape": [160], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/project/Conv2D_weights", "shape": [1, 1, 160, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_7/project/Conv2D_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/Conv2D_weights", "shape": [1, 1, 64, 144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/expand/Conv2D_bn_offset", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/depthwise_weights", "shape": [3, 3, 144, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/depthwise/depthwise_bn_offset", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/project/Conv2D_weights", "shape": [1, 1, 144, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_8/project/Conv2D_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/Conv2D_weights", "shape": [1, 1, 64, 144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/expand/Conv2D_bn_offset", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/depthwise_weights", "shape": [3, 3, 144, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/depthwise/depthwise_bn_offset", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/project/Conv2D_weights", "shape": [1, 1, 144, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_9/project/Conv2D_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/Conv2D_weights", "shape": [1, 1, 64, 384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/expand/Conv2D_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/depthwise/depthwise_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/project/Conv2D_weights", "shape": [1, 1, 384, 88], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_10/project/Conv2D_bn_offset", "shape": [88], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/Conv2D_weights", "shape": [1, 1, 88, 528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/expand/Conv2D_bn_offset", "shape": [528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/depthwise_weights", "shape": [3, 3, 528, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/depthwise/depthwise_bn_offset", "shape": [528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/project/Conv2D_weights", "shape": [1, 1, 528, 88], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_11/project/Conv2D_bn_offset", "shape": [88], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c4_reverse/1x1/Conv2D_weights", "shape": [1, 1, 88, 22], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c4_reverse/1x1/Conv2D_bn_offset", "shape": [22], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/Conv2D_weights", "shape": [1, 1, 88, 528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/expand/Conv2D_bn_offset", "shape": [528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c4_reverse/3x3/separable_conv2d_weights", "shape": [1, 1, 22, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c4_reverse/3x3/separable_conv2d_bn_offset", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/depthwise_weights", "shape": [5, 5, 528, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/depthwise/depthwise_bn_offset", "shape": [528], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/project/Conv2D_weights", "shape": [1, 1, 528, 120], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_12/project/Conv2D_bn_offset", "shape": [120], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/Conv2D_weights", "shape": [1, 1, 120, 720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/expand/Conv2D_bn_offset", "shape": [720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/depthwise_weights", "shape": [5, 5, 720, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/depthwise/depthwise_bn_offset", "shape": [720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/project/Conv2D_weights", "shape": [1, 1, 720, 120], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_13/project/Conv2D_bn_offset", "shape": [120], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/Conv2D_weights", "shape": [1, 1, 120, 720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/expand/Conv2D_bn_offset", "shape": [720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/depthwise_weights", "shape": [5, 5, 720, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/depthwise/depthwise_bn_offset", "shape": [720], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/project/Conv2D_weights", "shape": [1, 1, 720, 120], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/MobilenetV3/expanded_conv_14/project/Conv2D_bn_offset", "shape": [120], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/extern1/Conv2D_weights", "shape": [1, 1, 120, 480], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/extern1/Conv2D_bn_offset", "shape": [480], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/separable_conv2d_weights", "shape": [1, 1, 480, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_x_upsample_resize/separable_conv2d_bn_offset", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/separable_conv2d_weights", "shape": [1, 1, 480, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c5_upsample/branch_y_upsample_resize/separable_conv2d_bn_offset", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/separable_conv2d_weights", "shape": [1, 1, 256, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "tower_0/CenternetHead/c4_upsample/branch_x_upsample_resize/separable_conv2d_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}]}]}