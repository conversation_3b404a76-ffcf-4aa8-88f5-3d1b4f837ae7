{"version": 3, "sources": ["index.ts", "indexdb.ts"], "sourcesContent": ["/**\n * Human demo for browsers\n * @default Human Library\n * @summary <https://github.com/vladmandic/human>\n * <AUTHOR>\n * @copyright <https://github.com/vladmandic>\n * @license MIT\n */\n\nimport * as H from '../../dist/human.esm.js'; // equivalent of @vladmandic/Human\nimport * as indexDb from './indexdb'; // methods to deal with indexdb\n\nconst humanConfig = { // user configuration for human, used to fine-tune behavior\n  cacheSensitivity: 0.01,\n  modelBasePath: '../../models',\n  filter: { enabled: true, equalization: true }, // lets run with histogram equilizer\n  debug: true,\n  face: {\n    enabled: true,\n    detector: { rotation: true, return: true, mask: false }, // return tensor is used to get detected face image\n    description: { enabled: true }, // default model for face descriptor extraction is faceres\n    // mobilefacenet: { enabled: true, modelPath: 'https://vladmandic.github.io/human-models/models/mobilefacenet.json' }, // alternative model\n    // insightface: { enabled: true, modelPath: 'https://vladmandic.github.io/insightface/models/insightface-mobilenet-swish.json' }, // alternative model\n    iris: { enabled: true }, // needed to determine gaze direction\n    emotion: { enabled: false }, // not needed\n    antispoof: { enabled: true }, // enable optional antispoof module\n    liveness: { enabled: true }, // enable optional liveness module\n  },\n  body: { enabled: false },\n  hand: { enabled: false },\n  object: { enabled: false },\n  gesture: { enabled: true }, // parses face and iris gestures\n};\n\n// const matchOptions = { order: 2, multiplier: 1000, min: 0.0, max: 1.0 }; // for embedding model\nconst matchOptions = { order: 2, multiplier: 25, min: 0.2, max: 0.8 }; // for faceres model\n\nconst options = {\n  minConfidence: 0.6, // overal face confidence for box, face, gender, real, live\n  minSize: 224, // min input to face descriptor model before degradation\n  maxTime: 30000, // max time before giving up\n  blinkMin: 10, // minimum duration of a valid blink\n  blinkMax: 800, // maximum duration of a valid blink\n  threshold: 0.5, // minimum similarity\n  distanceMin: 0.4, // closest that face is allowed to be to the cammera in cm\n  distanceMax: 1.0, // farthest that face is allowed to be to the cammera in cm\n  mask: humanConfig.face.detector.mask,\n  rotation: humanConfig.face.detector.rotation,\n  ...matchOptions,\n};\n\nconst ok: Record<string, { status: boolean | undefined, val: number }> = { // must meet all rules\n  faceCount: { status: false, val: 0 },\n  faceConfidence: { status: false, val: 0 },\n  facingCenter: { status: false, val: 0 },\n  lookingCenter: { status: false, val: 0 },\n  blinkDetected: { status: false, val: 0 },\n  faceSize: { status: false, val: 0 },\n  antispoofCheck: { status: false, val: 0 },\n  livenessCheck: { status: false, val: 0 },\n  distance: { status: false, val: 0 },\n  age: { status: false, val: 0 },\n  gender: { status: false, val: 0 },\n  timeout: { status: true, val: 0 },\n  descriptor: { status: false, val: 0 },\n  elapsedMs: { status: undefined, val: 0 }, // total time while waiting for valid face\n  detectFPS: { status: undefined, val: 0 }, // mark detection fps performance\n  drawFPS: { status: undefined, val: 0 }, // mark redraw fps performance\n};\n\nconst allOk = () => ok.faceCount.status\n  && ok.faceSize.status\n  && ok.blinkDetected.status\n  && ok.facingCenter.status\n  && ok.lookingCenter.status\n  && ok.faceConfidence.status\n  && ok.antispoofCheck.status\n  && ok.livenessCheck.status\n  && ok.distance.status\n  && ok.descriptor.status\n  && ok.age.status\n  && ok.gender.status;\n\nconst current: { face: H.FaceResult | null, record: indexDb.FaceRecord | null } = { face: null, record: null }; // current face record and matched database record\n\nconst blink = { // internal timers for blink start/end/duration\n  start: 0,\n  end: 0,\n  time: 0,\n};\n\n// let db: Array<{ name: string, source: string, embedding: number[] }> = []; // holds loaded face descriptor database\nconst human = new H.Human(humanConfig); // create instance of human with overrides from user configuration\n\nhuman.env.perfadd = false; // is performance data showing instant or total values\nhuman.draw.options.font = 'small-caps 18px \"Lato\"'; // set font used to draw labels when using draw methods\nhuman.draw.options.lineHeight = 20;\n\nconst dom = { // grab instances of dom objects so we dont have to look them up later\n  video: document.getElementById('video') as HTMLVideoElement,\n  canvas: document.getElementById('canvas') as HTMLCanvasElement,\n  log: document.getElementById('log') as HTMLPreElement,\n  fps: document.getElementById('fps') as HTMLPreElement,\n  match: document.getElementById('match') as HTMLDivElement,\n  name: document.getElementById('name') as HTMLInputElement,\n  save: document.getElementById('save') as HTMLSpanElement,\n  delete: document.getElementById('delete') as HTMLSpanElement,\n  retry: document.getElementById('retry') as HTMLDivElement,\n  source: document.getElementById('source') as HTMLCanvasElement,\n  ok: document.getElementById('ok') as HTMLDivElement,\n};\nconst timestamp = { detect: 0, draw: 0 }; // holds information used to calculate performance and possible memory leaks\nlet startTime = 0;\n\nconst log = (...msg) => { // helper method to output messages\n  dom.log.innerText += msg.join(' ') + '\\n';\n  console.log(...msg); // eslint-disable-line no-console\n};\n\nasync function webCam() { // initialize webcam\n  // @ts-ignore resizeMode is not yet defined in tslib\n  const cameraOptions: MediaStreamConstraints = { audio: false, video: { facingMode: 'user', resizeMode: 'none', width: { ideal: document.body.clientWidth } } };\n  const stream: MediaStream = await navigator.mediaDevices.getUserMedia(cameraOptions);\n  const ready = new Promise((resolve) => { dom.video.onloadeddata = () => resolve(true); });\n  dom.video.srcObject = stream;\n  void dom.video.play();\n  await ready;\n  dom.canvas.width = dom.video.videoWidth;\n  dom.canvas.height = dom.video.videoHeight;\n  dom.canvas.style.width = '50%';\n  dom.canvas.style.height = '50%';\n  if (human.env.initial) log('video:', dom.video.videoWidth, dom.video.videoHeight, '|', stream.getVideoTracks()[0].label);\n  dom.canvas.onclick = () => { // pause when clicked on screen and resume on next click\n    if (dom.video.paused) void dom.video.play();\n    else dom.video.pause();\n  };\n}\n\nasync function detectionLoop() { // main detection loop\n  if (!dom.video.paused) {\n    if (current.face?.tensor) human.tf.dispose(current.face.tensor); // dispose previous tensor\n    await human.detect(dom.video); // actual detection; were not capturing output in a local variable as it can also be reached via human.result\n    const now = human.now();\n    ok.detectFPS.val = Math.round(10000 / (now - timestamp.detect)) / 10;\n    timestamp.detect = now;\n    requestAnimationFrame(detectionLoop); // start new frame immediately\n  }\n}\n\nfunction drawValidationTests() {\n  let y = 32;\n  for (const [key, val] of Object.entries(ok)) {\n    let el = document.getElementById(`ok-${key}`);\n    if (!el) {\n      el = document.createElement('div');\n      el.id = `ok-${key}`;\n      el.innerText = key;\n      el.className = 'ok';\n      el.style.top = `${y}px`;\n      dom.ok.appendChild(el);\n    }\n    if (typeof val.status === 'boolean') el.style.backgroundColor = val.status ? 'lightgreen' : 'lightcoral';\n    const status = val.status ? 'ok' : 'fail';\n    el.innerText = `${key}: ${val.val === 0 ? status : val.val}`;\n    y += 28;\n  }\n}\n\nasync function validationLoop(): Promise<H.FaceResult> { // main screen refresh loop\n  const interpolated = human.next(human.result); // smoothen result using last-known results\n  human.draw.canvas(dom.video, dom.canvas); // draw canvas to screen\n  await human.draw.all(dom.canvas, interpolated); // draw labels, boxes, lines, etc.\n  const now = human.now();\n  ok.drawFPS.val = Math.round(10000 / (now - timestamp.draw)) / 10;\n  timestamp.draw = now;\n  ok.faceCount.val = human.result.face.length;\n  ok.faceCount.status = ok.faceCount.val === 1; // must be exactly detected face\n  if (ok.faceCount.status) { // skip the rest if no face\n    const gestures: string[] = Object.values(human.result.gesture).map((gesture: H.GestureResult) => gesture.gesture); // flatten all gestures\n    if (gestures.includes('blink left eye') || gestures.includes('blink right eye')) blink.start = human.now(); // blink starts when eyes get closed\n    if (blink.start > 0 && !gestures.includes('blink left eye') && !gestures.includes('blink right eye')) blink.end = human.now(); // if blink started how long until eyes are back open\n    ok.blinkDetected.status = ok.blinkDetected.status || (Math.abs(blink.end - blink.start) > options.blinkMin && Math.abs(blink.end - blink.start) < options.blinkMax);\n    if (ok.blinkDetected.status && blink.time === 0) blink.time = Math.trunc(blink.end - blink.start);\n    ok.facingCenter.status = gestures.includes('facing center');\n    ok.lookingCenter.status = gestures.includes('looking center'); // must face camera and look at camera\n    ok.faceConfidence.val = human.result.face[0].faceScore || human.result.face[0].boxScore || 0;\n    ok.faceConfidence.status = ok.faceConfidence.val >= options.minConfidence;\n    ok.antispoofCheck.val = human.result.face[0].real || 0;\n    ok.antispoofCheck.status = ok.antispoofCheck.val >= options.minConfidence;\n    ok.livenessCheck.val = human.result.face[0].live || 0;\n    ok.livenessCheck.status = ok.livenessCheck.val >= options.minConfidence;\n    ok.faceSize.val = Math.min(human.result.face[0].box[2], human.result.face[0].box[3]);\n    ok.faceSize.status = ok.faceSize.val >= options.minSize;\n    ok.distance.val = human.result.face[0].distance || 0;\n    ok.distance.status = (ok.distance.val >= options.distanceMin) && (ok.distance.val <= options.distanceMax);\n    ok.descriptor.val = human.result.face[0].embedding?.length || 0;\n    ok.descriptor.status = ok.descriptor.val > 0;\n    ok.age.val = human.result.face[0].age || 0;\n    ok.age.status = ok.age.val > 0;\n    ok.gender.val = human.result.face[0].genderScore || 0;\n    ok.gender.status = ok.gender.val >= options.minConfidence;\n  }\n  // run again\n  ok.timeout.status = ok.elapsedMs.val <= options.maxTime;\n  drawValidationTests();\n  if (allOk() || !ok.timeout.status) { // all criteria met\n    dom.video.pause();\n    return human.result.face[0];\n  }\n  ok.elapsedMs.val = Math.trunc(human.now() - startTime);\n  return new Promise((resolve) => {\n    setTimeout(async () => {\n      await validationLoop(); // run validation loop until conditions are met\n      resolve(human.result.face[0]); // recursive promise resolve\n    }, 30); // use to slow down refresh from max refresh rate to target of 30 fps\n  });\n}\n\nasync function saveRecords() {\n  if (dom.name.value.length > 0) {\n    const image = dom.canvas.getContext('2d')?.getImageData(0, 0, dom.canvas.width, dom.canvas.height) as ImageData;\n    const rec = { id: 0, name: dom.name.value, descriptor: current.face?.embedding as number[], image };\n    await indexDb.save(rec);\n    log('saved face record:', rec.name, 'descriptor length:', current.face?.embedding?.length);\n    log('known face records:', await indexDb.count());\n  } else {\n    log('invalid name');\n  }\n}\n\nasync function deleteRecord() {\n  if (current.record && current.record.id > 0) {\n    await indexDb.remove(current.record);\n  }\n}\n\nasync function detectFace() {\n  dom.canvas.style.height = '';\n  dom.canvas.getContext('2d')?.clearRect(0, 0, options.minSize, options.minSize);\n  if (!current?.face?.tensor || !current?.face?.embedding) return false;\n  console.log('face record:', current.face); // eslint-disable-line no-console\n  log(`detected face: ${current.face.gender} ${current.face.age || 0}y distance ${100 * (current.face.distance || 0)}cm/${Math.round(100 * (current.face.distance || 0) / 2.54)}in`);\n  await human.draw.tensor(current.face.tensor, dom.canvas);\n  if (await indexDb.count() === 0) {\n    log('face database is empty: nothing to compare face with');\n    document.body.style.background = 'black';\n    dom.delete.style.display = 'none';\n    return false;\n  }\n  const db = await indexDb.load();\n  const descriptors = db.map((rec) => rec.descriptor).filter((desc) => desc.length > 0);\n  const res = human.match.find(current.face.embedding, descriptors, matchOptions);\n  current.record = db[res.index] || null;\n  if (current.record) {\n    log(`best match: ${current.record.name} | id: ${current.record.id} | similarity: ${Math.round(1000 * res.similarity) / 10}%`);\n    dom.name.value = current.record.name;\n    dom.source.style.display = '';\n    dom.source.getContext('2d')?.putImageData(current.record.image, 0, 0);\n  }\n  document.body.style.background = res.similarity > options.threshold ? 'darkgreen' : 'maroon';\n  return res.similarity > options.threshold;\n}\n\nasync function main() { // main entry point\n  ok.faceCount.status = false;\n  ok.faceConfidence.status = false;\n  ok.facingCenter.status = false;\n  ok.blinkDetected.status = false;\n  ok.faceSize.status = false;\n  ok.antispoofCheck.status = false;\n  ok.livenessCheck.status = false;\n  ok.age.status = false;\n  ok.gender.status = false;\n  ok.elapsedMs.val = 0;\n  dom.match.style.display = 'none';\n  dom.retry.style.display = 'none';\n  dom.source.style.display = 'none';\n  dom.canvas.style.height = '50%';\n  document.body.style.background = 'black';\n  await webCam();\n  await detectionLoop(); // start detection loop\n  startTime = human.now();\n  current.face = await validationLoop(); // start validation loop\n  dom.canvas.width = current.face?.tensor?.shape[1] || options.minSize;\n  dom.canvas.height = current.face?.tensor?.shape[0] || options.minSize;\n  dom.source.width = dom.canvas.width;\n  dom.source.height = dom.canvas.height;\n  dom.canvas.style.width = '';\n  dom.match.style.display = 'flex';\n  dom.save.style.display = 'flex';\n  dom.delete.style.display = 'flex';\n  dom.retry.style.display = 'block';\n  if (!allOk()) { // is all criteria met?\n    log('did not find valid face');\n    return false;\n  }\n  return detectFace();\n}\n\nasync function init() {\n  log('human version:', human.version, '| tfjs version:', human.tf.version['tfjs-core']);\n  log('options:', JSON.stringify(options).replace(/{|}|\"|\\[|\\]/g, '').replace(/,/g, ' '));\n  log('initializing webcam...');\n  await webCam(); // start webcam\n  log('loading human models...');\n  await human.load(); // preload all models\n  log('initializing human...');\n  log('face embedding model:', humanConfig.face.description.enabled ? 'faceres' : '', humanConfig.face['mobilefacenet']?.enabled ? 'mobilefacenet' : '', humanConfig.face['insightface']?.enabled ? 'insightface' : '');\n  log('loading face database...');\n  log('known face records:', await indexDb.count());\n  dom.retry.addEventListener('click', main);\n  dom.save.addEventListener('click', saveRecords);\n  dom.delete.addEventListener('click', deleteRecord);\n  await human.warmup(); // warmup function to initialize backend for future faster detection\n  await main();\n}\n\nwindow.onload = init;\n", "let db: IDBDatabase; // instance of indexdb\n\nconst database = 'human';\nconst table = 'person';\n\nexport interface FaceRecord { id: number, name: string, descriptor: number[], image: ImageData }\n\nconst log = (...msg) => console.log('indexdb', ...msg); // eslint-disable-line no-console\n\nexport async function open() {\n  if (db) return true;\n  return new Promise((resolve) => {\n    const request: IDBOpenDBRequest = indexedDB.open(database, 1);\n    request.onerror = (evt) => log('error:', evt);\n    request.onupgradeneeded = (evt: IDBVersionChangeEvent) => { // create if doesnt exist\n      log('create:', evt.target);\n      db = (evt.target as IDBOpenDBRequest).result;\n      db.createObjectStore(table, { keyPath: 'id', autoIncrement: true });\n    };\n    request.onsuccess = (evt) => { // open\n      db = (evt.target as IDBOpenDBRequest).result;\n      log('open:', db);\n      resolve(true);\n    };\n  });\n}\n\nexport async function load(): Promise<FaceRecord[]> {\n  const faceDB: FaceRecord[] = [];\n  if (!db) await open(); // open or create if not already done\n  return new Promise((resolve) => {\n    const cursor: IDBRequest = db.transaction([table], 'readwrite').objectStore(table).openCursor(null, 'next');\n    cursor.onerror = (evt) => log('load error:', evt);\n    cursor.onsuccess = (evt) => {\n      if ((evt.target as IDBRequest).result) {\n        faceDB.push((evt.target as IDBRequest).result.value);\n        (evt.target as IDBRequest).result.continue();\n      } else {\n        resolve(faceDB);\n      }\n    };\n  });\n}\n\nexport async function count(): Promise<number> {\n  if (!db) await open(); // open or create if not already done\n  return new Promise((resolve) => {\n    const store: IDBRequest = db.transaction([table], 'readwrite').objectStore(table).count();\n    store.onerror = (evt) => log('count error:', evt);\n    store.onsuccess = () => resolve(store.result);\n  });\n}\n\nexport async function save(faceRecord: FaceRecord) {\n  if (!db) await open(); // open or create if not already done\n  const newRecord = { name: faceRecord.name, descriptor: faceRecord.descriptor, image: faceRecord.image }; // omit id as its autoincrement\n  db.transaction([table], 'readwrite').objectStore(table).put(newRecord);\n  log('save:', newRecord);\n}\n\nexport async function remove(faceRecord: FaceRecord) {\n  if (!db) await open(); // open or create if not already done\n  db.transaction([table], 'readwrite').objectStore(table).delete(faceRecord.id); // delete based on id\n  log('delete:', faceRecord);\n}\n"], "mappings": ";;;;;;AASA,UAAYA,MAAO,0BCTnB,IAAIC,EAEEC,EAAW,QACXC,EAAQ,SAIRC,EAAM,IAAIC,IAAQ,QAAQ,IAAI,UAAW,GAAGA,CAAG,EAErD,eAAsBC,GAAO,CAC3B,OAAIL,EAAW,GACR,IAAI,QAASM,GAAY,CAC9B,IAAMC,EAA4B,UAAU,KAAKN,EAAU,CAAC,EAC5DM,EAAQ,QAAWC,GAAQL,EAAI,SAAUK,CAAG,EAC5CD,EAAQ,gBAAmBC,GAA+B,CACxDL,EAAI,UAAWK,EAAI,MAAM,EACzBR,EAAMQ,EAAI,OAA4B,OACtCR,EAAG,kBAAkBE,EAAO,CAAE,QAAS,KAAM,cAAe,EAAK,CAAC,CACpE,EACAK,EAAQ,UAAaC,GAAQ,CAC3BR,EAAMQ,EAAI,OAA4B,OACtCL,EAAI,QAASH,CAAE,EACfM,EAAQ,EAAI,CACd,CACF,CAAC,CACH,CAEA,eAAsBG,GAA8B,CAClD,IAAMC,EAAuB,CAAC,EAC9B,OAAKV,GAAI,MAAMK,EAAK,EACb,IAAI,QAASC,GAAY,CAC9B,IAAMK,EAAqBX,EAAG,YAAY,CAACE,CAAK,EAAG,WAAW,EAAE,YAAYA,CAAK,EAAE,WAAW,KAAM,MAAM,EAC1GS,EAAO,QAAWH,GAAQL,EAAI,cAAeK,CAAG,EAChDG,EAAO,UAAaH,GAAQ,CACrBA,EAAI,OAAsB,QAC7BE,EAAO,KAAMF,EAAI,OAAsB,OAAO,KAAK,EAClDA,EAAI,OAAsB,OAAO,SAAS,GAE3CF,EAAQI,CAAM,CAElB,CACF,CAAC,CACH,CAEA,eAAsBE,GAAyB,CAC7C,OAAKZ,GAAI,MAAMK,EAAK,EACb,IAAI,QAASC,GAAY,CAC9B,IAAMO,EAAoBb,EAAG,YAAY,CAACE,CAAK,EAAG,WAAW,EAAE,YAAYA,CAAK,EAAE,MAAM,EACxFW,EAAM,QAAWL,GAAQL,EAAI,eAAgBK,CAAG,EAChDK,EAAM,UAAY,IAAMP,EAAQO,EAAM,MAAM,CAC9C,CAAC,CACH,CAEA,eAAsBC,EAAKC,EAAwB,CAC5Cf,GAAI,MAAMK,EAAK,EACpB,IAAMW,EAAY,CAAE,KAAMD,EAAW,KAAM,WAAYA,EAAW,WAAY,MAAOA,EAAW,KAAM,EACtGf,EAAG,YAAY,CAACE,CAAK,EAAG,WAAW,EAAE,YAAYA,CAAK,EAAE,IAAIc,CAAS,EACrEb,EAAI,QAASa,CAAS,CACxB,CAEA,eAAsBC,EAAOF,EAAwB,CAC9Cf,GAAI,MAAMK,EAAK,EACpBL,EAAG,YAAY,CAACE,CAAK,EAAG,WAAW,EAAE,YAAYA,CAAK,EAAE,OAAOa,EAAW,EAAE,EAC5EZ,EAAI,UAAWY,CAAU,CAC3B,CDpDA,IAAMG,EAAc,CAClB,iBAAkB,IAClB,cAAe,eACf,OAAQ,CAAE,QAAS,GAAM,aAAc,EAAK,EAC5C,MAAO,GACP,KAAM,CACJ,QAAS,GACT,SAAU,CAAE,SAAU,GAAM,OAAQ,GAAM,KAAM,EAAM,EACtD,YAAa,CAAE,QAAS,EAAK,EAG7B,KAAM,CAAE,QAAS,EAAK,EACtB,QAAS,CAAE,QAAS,EAAM,EAC1B,UAAW,CAAE,QAAS,EAAK,EAC3B,SAAU,CAAE,QAAS,EAAK,CAC5B,EACA,KAAM,CAAE,QAAS,EAAM,EACvB,KAAM,CAAE,QAAS,EAAM,EACvB,OAAQ,CAAE,QAAS,EAAM,EACzB,QAAS,CAAE,QAAS,EAAK,CAC3B,EAGMC,EAAe,CAAE,MAAO,EAAG,WAAY,GAAI,IAAK,GAAK,IAAK,EAAI,EAE9DC,EAAU,CACd,cAAe,GACf,QAAS,IACT,QAAS,IACT,SAAU,GACV,SAAU,IACV,UAAW,GACX,YAAa,GACb,YAAa,EACb,KAAMF,EAAY,KAAK,SAAS,KAChC,SAAUA,EAAY,KAAK,SAAS,SACpC,GAAGC,CACL,EAEME,EAAmE,CACvE,UAAW,CAAE,OAAQ,GAAO,IAAK,CAAE,EACnC,eAAgB,CAAE,OAAQ,GAAO,IAAK,CAAE,EACxC,aAAc,CAAE,OAAQ,GAAO,IAAK,CAAE,EACtC,cAAe,CAAE,OAAQ,GAAO,IAAK,CAAE,EACvC,cAAe,CAAE,OAAQ,GAAO,IAAK,CAAE,EACvC,SAAU,CAAE,OAAQ,GAAO,IAAK,CAAE,EAClC,eAAgB,CAAE,OAAQ,GAAO,IAAK,CAAE,EACxC,cAAe,CAAE,OAAQ,GAAO,IAAK,CAAE,EACvC,SAAU,CAAE,OAAQ,GAAO,IAAK,CAAE,EAClC,IAAK,CAAE,OAAQ,GAAO,IAAK,CAAE,EAC7B,OAAQ,CAAE,OAAQ,GAAO,IAAK,CAAE,EAChC,QAAS,CAAE,OAAQ,GAAM,IAAK,CAAE,EAChC,WAAY,CAAE,OAAQ,GAAO,IAAK,CAAE,EACpC,UAAW,CAAE,OAAQ,OAAW,IAAK,CAAE,EACvC,UAAW,CAAE,OAAQ,OAAW,IAAK,CAAE,EACvC,QAAS,CAAE,OAAQ,OAAW,IAAK,CAAE,CACvC,EAEMC,EAAQ,IAAMD,EAAG,UAAU,QAC5BA,EAAG,SAAS,QACZA,EAAG,cAAc,QACjBA,EAAG,aAAa,QAChBA,EAAG,cAAc,QACjBA,EAAG,eAAe,QAClBA,EAAG,eAAe,QAClBA,EAAG,cAAc,QACjBA,EAAG,SAAS,QACZA,EAAG,WAAW,QACdA,EAAG,IAAI,QACPA,EAAG,OAAO,OAETE,EAA4E,CAAE,KAAM,KAAM,OAAQ,IAAK,EAEvGC,EAAQ,CACZ,MAAO,EACP,IAAK,EACL,KAAM,CACR,EAGMC,EAAQ,IAAM,QAAMP,CAAW,EAErCO,EAAM,IAAI,QAAU,GACpBA,EAAM,KAAK,QAAQ,KAAO,yBAC1BA,EAAM,KAAK,QAAQ,WAAa,GAEhC,IAAMC,EAAM,CACV,MAAO,SAAS,eAAe,OAAO,EACtC,OAAQ,SAAS,eAAe,QAAQ,EACxC,IAAK,SAAS,eAAe,KAAK,EAClC,IAAK,SAAS,eAAe,KAAK,EAClC,MAAO,SAAS,eAAe,OAAO,EACtC,KAAM,SAAS,eAAe,MAAM,EACpC,KAAM,SAAS,eAAe,MAAM,EACpC,OAAQ,SAAS,eAAe,QAAQ,EACxC,MAAO,SAAS,eAAe,OAAO,EACtC,OAAQ,SAAS,eAAe,QAAQ,EACxC,GAAI,SAAS,eAAe,IAAI,CAClC,EACMC,EAAY,CAAE,OAAQ,EAAG,KAAM,CAAE,EACnCC,EAAY,EAEVC,EAAM,IAAIC,IAAQ,CACtBJ,EAAI,IAAI,WAAaI,EAAI,KAAK,GAAG,EAAI;AAAA,EACrC,QAAQ,IAAI,GAAGA,CAAG,CACpB,EAEA,eAAeC,GAAS,CAEtB,IAAMC,EAAwC,CAAE,MAAO,GAAO,MAAO,CAAE,WAAY,OAAQ,WAAY,OAAQ,MAAO,CAAE,MAAO,SAAS,KAAK,WAAY,CAAE,CAAE,EACvJC,EAAsB,MAAM,UAAU,aAAa,aAAaD,CAAa,EAC7EE,EAAQ,IAAI,QAASC,GAAY,CAAET,EAAI,MAAM,aAAe,IAAMS,EAAQ,EAAI,CAAG,CAAC,EACxFT,EAAI,MAAM,UAAYO,EACjBP,EAAI,MAAM,KAAK,EACpB,MAAMQ,EACNR,EAAI,OAAO,MAAQA,EAAI,MAAM,WAC7BA,EAAI,OAAO,OAASA,EAAI,MAAM,YAC9BA,EAAI,OAAO,MAAM,MAAQ,MACzBA,EAAI,OAAO,MAAM,OAAS,MACtBD,EAAM,IAAI,SAASI,EAAI,SAAUH,EAAI,MAAM,WAAYA,EAAI,MAAM,YAAa,IAAKO,EAAO,eAAe,EAAE,CAAC,EAAE,KAAK,EACvHP,EAAI,OAAO,QAAU,IAAM,CACrBA,EAAI,MAAM,OAAaA,EAAI,MAAM,KAAK,EACrCA,EAAI,MAAM,MAAM,CACvB,CACF,CAEA,eAAeU,GAAgB,CA1I/B,IAAAC,EA2IE,GAAI,CAACX,EAAI,MAAM,OAAQ,EACjBW,EAAAd,EAAQ,OAAR,MAAAc,EAAc,QAAQZ,EAAM,GAAG,QAAQF,EAAQ,KAAK,MAAM,EAC9D,MAAME,EAAM,OAAOC,EAAI,KAAK,EAC5B,IAAMY,EAAMb,EAAM,IAAI,EACtBJ,EAAG,UAAU,IAAM,KAAK,MAAM,KAASiB,EAAMX,EAAU,OAAO,EAAI,GAClEA,EAAU,OAASW,EACnB,sBAAsBF,CAAa,CACrC,CACF,CAEA,SAASG,GAAsB,CAC7B,IAAIC,EAAI,GACR,OAAW,CAACC,EAAKC,CAAG,IAAK,OAAO,QAAQrB,CAAE,EAAG,CAC3C,IAAIsB,EAAK,SAAS,eAAe,MAAMF,CAAG,EAAE,EACvCE,IACHA,EAAK,SAAS,cAAc,KAAK,EACjCA,EAAG,GAAK,MAAMF,CAAG,GACjBE,EAAG,UAAYF,EACfE,EAAG,UAAY,KACfA,EAAG,MAAM,IAAM,GAAGH,CAAC,KACnBd,EAAI,GAAG,YAAYiB,CAAE,GAEnB,OAAOD,EAAI,QAAW,YAAWC,EAAG,MAAM,gBAAkBD,EAAI,OAAS,aAAe,cAC5F,IAAME,EAASF,EAAI,OAAS,KAAO,OACnCC,EAAG,UAAY,GAAGF,CAAG,KAAKC,EAAI,MAAQ,EAAIE,EAASF,EAAI,GAAG,GAC1DF,GAAK,EACP,CACF,CAEA,eAAeK,GAAwC,CAxKvD,IAAAR,EAyKE,IAAMS,EAAerB,EAAM,KAAKA,EAAM,MAAM,EAC5CA,EAAM,KAAK,OAAOC,EAAI,MAAOA,EAAI,MAAM,EACvC,MAAMD,EAAM,KAAK,IAAIC,EAAI,OAAQoB,CAAY,EAC7C,IAAMR,EAAMb,EAAM,IAAI,EAKtB,GAJAJ,EAAG,QAAQ,IAAM,KAAK,MAAM,KAASiB,EAAMX,EAAU,KAAK,EAAI,GAC9DA,EAAU,KAAOW,EACjBjB,EAAG,UAAU,IAAMI,EAAM,OAAO,KAAK,OACrCJ,EAAG,UAAU,OAASA,EAAG,UAAU,MAAQ,EACvCA,EAAG,UAAU,OAAQ,CACvB,IAAM0B,EAAqB,OAAO,OAAOtB,EAAM,OAAO,OAAO,EAAE,IAAKuB,GAA6BA,EAAQ,OAAO,GAC5GD,EAAS,SAAS,gBAAgB,GAAKA,EAAS,SAAS,iBAAiB,KAAGvB,EAAM,MAAQC,EAAM,IAAI,GACrGD,EAAM,MAAQ,GAAK,CAACuB,EAAS,SAAS,gBAAgB,GAAK,CAACA,EAAS,SAAS,iBAAiB,IAAGvB,EAAM,IAAMC,EAAM,IAAI,GAC5HJ,EAAG,cAAc,OAASA,EAAG,cAAc,QAAW,KAAK,IAAIG,EAAM,IAAMA,EAAM,KAAK,EAAIJ,EAAQ,UAAY,KAAK,IAAII,EAAM,IAAMA,EAAM,KAAK,EAAIJ,EAAQ,SACtJC,EAAG,cAAc,QAAUG,EAAM,OAAS,IAAGA,EAAM,KAAO,KAAK,MAAMA,EAAM,IAAMA,EAAM,KAAK,GAChGH,EAAG,aAAa,OAAS0B,EAAS,SAAS,eAAe,EAC1D1B,EAAG,cAAc,OAAS0B,EAAS,SAAS,gBAAgB,EAC5D1B,EAAG,eAAe,IAAMI,EAAM,OAAO,KAAK,CAAC,EAAE,WAAaA,EAAM,OAAO,KAAK,CAAC,EAAE,UAAY,EAC3FJ,EAAG,eAAe,OAASA,EAAG,eAAe,KAAOD,EAAQ,cAC5DC,EAAG,eAAe,IAAMI,EAAM,OAAO,KAAK,CAAC,EAAE,MAAQ,EACrDJ,EAAG,eAAe,OAASA,EAAG,eAAe,KAAOD,EAAQ,cAC5DC,EAAG,cAAc,IAAMI,EAAM,OAAO,KAAK,CAAC,EAAE,MAAQ,EACpDJ,EAAG,cAAc,OAASA,EAAG,cAAc,KAAOD,EAAQ,cAC1DC,EAAG,SAAS,IAAM,KAAK,IAAII,EAAM,OAAO,KAAK,CAAC,EAAE,IAAI,CAAC,EAAGA,EAAM,OAAO,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,EACnFJ,EAAG,SAAS,OAASA,EAAG,SAAS,KAAOD,EAAQ,QAChDC,EAAG,SAAS,IAAMI,EAAM,OAAO,KAAK,CAAC,EAAE,UAAY,EACnDJ,EAAG,SAAS,OAAUA,EAAG,SAAS,KAAOD,EAAQ,aAAiBC,EAAG,SAAS,KAAOD,EAAQ,YAC7FC,EAAG,WAAW,MAAMgB,EAAAZ,EAAM,OAAO,KAAK,CAAC,EAAE,YAArB,YAAAY,EAAgC,SAAU,EAC9DhB,EAAG,WAAW,OAASA,EAAG,WAAW,IAAM,EAC3CA,EAAG,IAAI,IAAMI,EAAM,OAAO,KAAK,CAAC,EAAE,KAAO,EACzCJ,EAAG,IAAI,OAASA,EAAG,IAAI,IAAM,EAC7BA,EAAG,OAAO,IAAMI,EAAM,OAAO,KAAK,CAAC,EAAE,aAAe,EACpDJ,EAAG,OAAO,OAASA,EAAG,OAAO,KAAOD,EAAQ,aAC9C,CAIA,OAFAC,EAAG,QAAQ,OAASA,EAAG,UAAU,KAAOD,EAAQ,QAChDmB,EAAoB,EAChBjB,EAAM,GAAK,CAACD,EAAG,QAAQ,QACzBK,EAAI,MAAM,MAAM,EACTD,EAAM,OAAO,KAAK,CAAC,IAE5BJ,EAAG,UAAU,IAAM,KAAK,MAAMI,EAAM,IAAI,EAAIG,CAAS,EAC9C,IAAI,QAASO,GAAY,CAC9B,WAAW,SAAY,CACrB,MAAMU,EAAe,EACrBV,EAAQV,EAAM,OAAO,KAAK,CAAC,CAAC,CAC9B,EAAG,EAAE,CACP,CAAC,EACH,CAEA,eAAewB,GAAc,CA1N7B,IAAAZ,EAAAa,EAAAC,EAAAC,EA2NE,GAAI1B,EAAI,KAAK,MAAM,OAAS,EAAG,CAC7B,IAAM2B,GAAQhB,EAAAX,EAAI,OAAO,WAAW,IAAI,IAA1B,YAAAW,EAA6B,aAAa,EAAG,EAAGX,EAAI,OAAO,MAAOA,EAAI,OAAO,QACrF4B,EAAM,CAAE,GAAI,EAAG,KAAM5B,EAAI,KAAK,MAAO,YAAYwB,EAAA3B,EAAQ,OAAR,YAAA2B,EAAc,UAAuB,MAAAG,CAAM,EAClG,MAAcE,EAAKD,CAAG,EACtBzB,EAAI,qBAAsByB,EAAI,KAAM,sBAAsBF,GAAAD,EAAA5B,EAAQ,OAAR,YAAA4B,EAAc,YAAd,YAAAC,EAAyB,MAAM,EACzFvB,EAAI,sBAAuB,MAAc2B,EAAM,CAAC,CAClD,MACE3B,EAAI,cAAc,CAEtB,CAEA,eAAe4B,GAAe,CACxBlC,EAAQ,QAAUA,EAAQ,OAAO,GAAK,GACxC,MAAcmC,EAAOnC,EAAQ,MAAM,CAEvC,CAEA,eAAeoC,GAAa,CA5O5B,IAAAtB,EAAAa,EAAAC,EAAAC,EA+OE,GAFA1B,EAAI,OAAO,MAAM,OAAS,IAC1BW,EAAAX,EAAI,OAAO,WAAW,IAAI,IAA1B,MAAAW,EAA6B,UAAU,EAAG,EAAGjB,EAAQ,QAASA,EAAQ,SAClE,GAAC8B,EAAA3B,GAAA,YAAAA,EAAS,OAAT,MAAA2B,EAAe,SAAU,GAACC,EAAA5B,GAAA,YAAAA,EAAS,OAAT,MAAA4B,EAAe,WAAW,MAAO,GAIhE,GAHA,QAAQ,IAAI,eAAgB5B,EAAQ,IAAI,EACxCM,EAAI,kBAAkBN,EAAQ,KAAK,MAAM,IAAIA,EAAQ,KAAK,KAAO,CAAC,cAAc,KAAOA,EAAQ,KAAK,UAAY,EAAE,MAAM,KAAK,MAAM,KAAOA,EAAQ,KAAK,UAAY,GAAK,IAAI,CAAC,IAAI,EACjL,MAAME,EAAM,KAAK,OAAOF,EAAQ,KAAK,OAAQG,EAAI,MAAM,EACnD,MAAc8B,EAAM,IAAM,EAC5B,OAAA3B,EAAI,sDAAsD,EAC1D,SAAS,KAAK,MAAM,WAAa,QACjCH,EAAI,OAAO,MAAM,QAAU,OACpB,GAET,IAAMkC,EAAK,MAAcC,EAAK,EACxBC,EAAcF,EAAG,IAAKN,GAAQA,EAAI,UAAU,EAAE,OAAQS,GAASA,EAAK,OAAS,CAAC,EAC9EC,EAAMvC,EAAM,MAAM,KAAKF,EAAQ,KAAK,UAAWuC,EAAa3C,CAAY,EAC9E,OAAAI,EAAQ,OAASqC,EAAGI,EAAI,KAAK,GAAK,KAC9BzC,EAAQ,SACVM,EAAI,eAAeN,EAAQ,OAAO,IAAI,UAAUA,EAAQ,OAAO,EAAE,kBAAkB,KAAK,MAAM,IAAOyC,EAAI,UAAU,EAAI,EAAE,GAAG,EAC5HtC,EAAI,KAAK,MAAQH,EAAQ,OAAO,KAChCG,EAAI,OAAO,MAAM,QAAU,IAC3B0B,EAAA1B,EAAI,OAAO,WAAW,IAAI,IAA1B,MAAA0B,EAA6B,aAAa7B,EAAQ,OAAO,MAAO,EAAG,IAErE,SAAS,KAAK,MAAM,WAAayC,EAAI,WAAa5C,EAAQ,UAAY,YAAc,SAC7E4C,EAAI,WAAa5C,EAAQ,SAClC,CAEA,eAAe6C,GAAO,CAvQtB,IAAA5B,EAAAa,EAAAC,EAAAC,EAoSE,OA5BA/B,EAAG,UAAU,OAAS,GACtBA,EAAG,eAAe,OAAS,GAC3BA,EAAG,aAAa,OAAS,GACzBA,EAAG,cAAc,OAAS,GAC1BA,EAAG,SAAS,OAAS,GACrBA,EAAG,eAAe,OAAS,GAC3BA,EAAG,cAAc,OAAS,GAC1BA,EAAG,IAAI,OAAS,GAChBA,EAAG,OAAO,OAAS,GACnBA,EAAG,UAAU,IAAM,EACnBK,EAAI,MAAM,MAAM,QAAU,OAC1BA,EAAI,MAAM,MAAM,QAAU,OAC1BA,EAAI,OAAO,MAAM,QAAU,OAC3BA,EAAI,OAAO,MAAM,OAAS,MAC1B,SAAS,KAAK,MAAM,WAAa,QACjC,MAAMK,EAAO,EACb,MAAMK,EAAc,EACpBR,EAAYH,EAAM,IAAI,EACtBF,EAAQ,KAAO,MAAMsB,EAAe,EACpCnB,EAAI,OAAO,QAAQwB,GAAAb,EAAAd,EAAQ,OAAR,YAAAc,EAAc,SAAd,YAAAa,EAAsB,MAAM,KAAM9B,EAAQ,QAC7DM,EAAI,OAAO,SAAS0B,GAAAD,EAAA5B,EAAQ,OAAR,YAAA4B,EAAc,SAAd,YAAAC,EAAsB,MAAM,KAAMhC,EAAQ,QAC9DM,EAAI,OAAO,MAAQA,EAAI,OAAO,MAC9BA,EAAI,OAAO,OAASA,EAAI,OAAO,OAC/BA,EAAI,OAAO,MAAM,MAAQ,GACzBA,EAAI,MAAM,MAAM,QAAU,OAC1BA,EAAI,KAAK,MAAM,QAAU,OACzBA,EAAI,OAAO,MAAM,QAAU,OAC3BA,EAAI,MAAM,MAAM,QAAU,QACrBJ,EAAM,EAIJqC,EAAW,GAHhB9B,EAAI,yBAAyB,EACtB,GAGX,CAEA,eAAeqC,GAAO,CA3StB,IAAA7B,EAAAa,EA4SErB,EAAI,iBAAkBJ,EAAM,QAAS,kBAAmBA,EAAM,GAAG,QAAQ,WAAW,CAAC,EACrFI,EAAI,WAAY,KAAK,UAAUT,CAAO,EAAE,QAAQ,eAAgB,EAAE,EAAE,QAAQ,KAAM,GAAG,CAAC,EACtFS,EAAI,wBAAwB,EAC5B,MAAME,EAAO,EACbF,EAAI,yBAAyB,EAC7B,MAAMJ,EAAM,KAAK,EACjBI,EAAI,uBAAuB,EAC3BA,EAAI,wBAAyBX,EAAY,KAAK,YAAY,QAAU,UAAY,IAAImB,EAAAnB,EAAY,KAAK,gBAAjB,MAAAmB,EAAmC,QAAU,gBAAkB,IAAIa,EAAAhC,EAAY,KAAK,cAAjB,MAAAgC,EAAiC,QAAU,cAAgB,EAAE,EACpNrB,EAAI,0BAA0B,EAC9BA,EAAI,sBAAuB,MAAc2B,EAAM,CAAC,EAChD9B,EAAI,MAAM,iBAAiB,QAASuC,CAAI,EACxCvC,EAAI,KAAK,iBAAiB,QAASuB,CAAW,EAC9CvB,EAAI,OAAO,iBAAiB,QAAS+B,CAAY,EACjD,MAAMhC,EAAM,OAAO,EACnB,MAAMwC,EAAK,CACb,CAEA,OAAO,OAASC", "names": ["H", "db", "database", "table", "log", "msg", "open", "resolve", "request", "evt", "load", "faceDB", "cursor", "count", "store", "save", "faceRecord", "newRecord", "remove", "humanConfig", "matchOptions", "options", "ok", "allOk", "current", "blink", "human", "dom", "timestamp", "startTime", "log", "msg", "webCam", "cameraOptions", "stream", "ready", "resolve", "detectionLoop", "_a", "now", "drawValidationTests", "y", "key", "val", "el", "status", "validationLoop", "interpolated", "gestures", "gesture", "saveRecords", "_b", "_c", "_d", "image", "rec", "save", "count", "deleteRecord", "remove", "detectFace", "db", "load", "descriptors", "desc", "res", "main", "init"]}