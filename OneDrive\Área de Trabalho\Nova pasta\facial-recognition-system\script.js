// Importar Human
import Human from './node_modules/@vladmandic/human/dist/human.esm.js';

// Variáveis globais
let human;
let video;
let canvas;
let ctx;
let stream;
let isDetecting = false;
let animationId;
let fpsCounter = 0;
let lastTime = 0;
let capturedPhotos = [];

// Configuração do Human
const humanConfig = {
    async: true,
    warmup: 'none',
    filter: { enabled: true },
    backend: 'webgl',
    face: {
        enabled: true,
        detector: 'mediapipe',
        mesh: true,
        iris: true,
        emotion: true,
        age: true,
        gender: true,
        minConfidence: 0.5,
        maxDetected: 5
    },
    body: { enabled: false },
    hand: { enabled: false },
    gesture: { enabled: false }
};

// Elementos DOM
const elements = {
    video: null,
    canvas: null,
    startBtn: null,
    stopBtn: null,
    captureBtn: null,
    toggleDetection: null,
    confidenceSlider: null,
    confidenceValue: null,
    maxFaces: null,
    maxFacesValue: null,
    showAge: null,
    showGender: null,
    showEmotion: null,
    showMesh: null,
    systemStatus: null,
    fpsCounter: null,
    faceCount: null,
    processTime: null,
    resultsContainer: null,
    photosContainer: null,
    logsContainer: null,
    clearLogs: null,
    loadingOverlay: null
};

// Inicialização
document.addEventListener('DOMContentLoaded', async () => {
    await initializeElements();
    await initializeHuman();
    setupEventListeners();
    hideLoadingOverlay();
    addLog('Sistema pronto para uso', 'info');
});

// Inicializar elementos DOM
async function initializeElements() {
    elements.video = document.getElementById('video');
    elements.canvas = document.getElementById('canvas');
    elements.startBtn = document.getElementById('startBtn');
    elements.stopBtn = document.getElementById('stopBtn');
    elements.captureBtn = document.getElementById('captureBtn');
    elements.toggleDetection = document.getElementById('toggleDetection');
    elements.confidenceSlider = document.getElementById('confidenceSlider');
    elements.confidenceValue = document.getElementById('confidenceValue');
    elements.maxFaces = document.getElementById('maxFaces');
    elements.maxFacesValue = document.getElementById('maxFacesValue');
    elements.showAge = document.getElementById('showAge');
    elements.showGender = document.getElementById('showGender');
    elements.showEmotion = document.getElementById('showEmotion');
    elements.showMesh = document.getElementById('showMesh');
    elements.systemStatus = document.getElementById('systemStatus');
    elements.fpsCounter = document.getElementById('fpsCounter');
    elements.faceCount = document.getElementById('faceCount');
    elements.processTime = document.getElementById('processTime');
    elements.resultsContainer = document.getElementById('resultsContainer');
    elements.photosContainer = document.getElementById('photosContainer');
    elements.logsContainer = document.getElementById('logsContainer');
    elements.clearLogs = document.getElementById('clearLogs');
    elements.loadingOverlay = document.getElementById('loadingOverlay');

    video = elements.video;
    canvas = elements.canvas;
    ctx = canvas.getContext('2d');
}

// Inicializar Human
async function initializeHuman() {
    try {
        addLog('Inicializando Human AI...', 'info');
        human = new Human(humanConfig);
        await human.load();
        await human.warmup();
        addLog('Human AI carregado com sucesso', 'info');
        updateSystemStatus('Pronto');
    } catch (error) {
        addLog(`Erro ao carregar Human AI: ${error.message}`, 'error');
        updateSystemStatus('Erro');
    }
}

// Configurar event listeners
function setupEventListeners() {
    elements.startBtn.addEventListener('click', startCamera);
    elements.stopBtn.addEventListener('click', stopCamera);
    elements.captureBtn.addEventListener('click', capturePhoto);
    elements.toggleDetection.addEventListener('click', toggleDetection);
    elements.clearLogs.addEventListener('click', clearLogs);

    // Sliders
    elements.confidenceSlider.addEventListener('input', (e) => {
        const value = parseFloat(e.target.value);
        elements.confidenceValue.textContent = value;
        humanConfig.face.minConfidence = value;
        addLog(`Confiança mínima alterada para: ${value}`, 'info');
    });

    elements.maxFaces.addEventListener('input', (e) => {
        const value = parseInt(e.target.value);
        elements.maxFacesValue.textContent = value;
        humanConfig.face.maxDetected = value;
        addLog(`Máximo de faces alterado para: ${value}`, 'info');
    });

    // Checkboxes
    [elements.showAge, elements.showGender, elements.showEmotion, elements.showMesh].forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            addLog(`${checkbox.parentElement.textContent.trim()}: ${checkbox.checked ? 'Ativado' : 'Desativado'}`, 'info');
        });
    });
}

// Iniciar câmera
async function startCamera() {
    try {
        addLog('Iniciando câmera...', 'info');
        
        stream = await navigator.mediaDevices.getUserMedia({
            video: { 
                width: { ideal: 640 }, 
                height: { ideal: 480 },
                facingMode: 'user'
            }
        });

        video.srcObject = stream;
        
        video.addEventListener('loadedmetadata', () => {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            elements.startBtn.disabled = true;
            elements.stopBtn.disabled = false;
            elements.captureBtn.disabled = false;
            elements.toggleDetection.disabled = false;
            
            updateSystemStatus('Câmera Ativa');
            addLog('Câmera iniciada com sucesso', 'info');
            
            startDetection();
        });

    } catch (error) {
        addLog(`Erro ao acessar câmera: ${error.message}`, 'error');
        updateSystemStatus('Erro na Câmera');
    }
}

// Parar câmera
function stopCamera() {
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
    }
    
    if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
    }
    
    isDetecting = false;
    
    elements.startBtn.disabled = false;
    elements.stopBtn.disabled = true;
    elements.captureBtn.disabled = true;
    elements.toggleDetection.disabled = true;
    
    updateSystemStatus('Desconectado');
    updateFaceCount(0);
    clearCanvas();
    clearResults();
    
    addLog('Câmera parada', 'info');
}

// Iniciar detecção
function startDetection() {
    isDetecting = true;
    detectFaces();
}

// Alternar detecção
function toggleDetection() {
    isDetecting = !isDetecting;
    
    if (isDetecting) {
        elements.toggleDetection.innerHTML = '<i class="fas fa-pause"></i> Pausar Detecção';
        detectFaces();
        addLog('Detecção retomada', 'info');
    } else {
        elements.toggleDetection.innerHTML = '<i class="fas fa-play"></i> Retomar Detecção';
        if (animationId) {
            cancelAnimationFrame(animationId);
            animationId = null;
        }
        addLog('Detecção pausada', 'info');
    }
}

// Detectar faces
async function detectFaces() {
    if (!isDetecting || !video || video.paused || video.ended) return;

    const startTime = performance.now();
    
    try {
        const result = await human.detect(video);
        const endTime = performance.now();
        const processTime = Math.round(endTime - startTime);
        
        // Atualizar estatísticas
        updateProcessTime(processTime);
        updateFPS();
        updateFaceCount(result.face.length);
        
        // Limpar canvas
        clearCanvas();
        
        // Desenhar resultados
        if (result.face.length > 0) {
            drawFaces(result.face);
            displayResults(result.face);
        } else {
            clearResults();
        }
        
    } catch (error) {
        addLog(`Erro na detecção: ${error.message}`, 'error');
    }
    
    animationId = requestAnimationFrame(detectFaces);
}

// Desenhar faces detectadas
function drawFaces(faces) {
    faces.forEach((face, index) => {
        // Desenhar bounding box
        const box = face.box;
        ctx.strokeStyle = '#4CAF50';
        ctx.lineWidth = 2;
        ctx.strokeRect(box[0], box[1], box[2], box[3]);
        
        // Desenhar malha facial se habilitado
        if (elements.showMesh.checked && face.mesh) {
            ctx.fillStyle = '#4CAF50';
            face.mesh.forEach(point => {
                ctx.beginPath();
                ctx.arc(point[0], point[1], 1, 0, 2 * Math.PI);
                ctx.fill();
            });
        }
        
        // Desenhar informações
        let y = box[1] - 10;
        ctx.fillStyle = '#4CAF50';
        ctx.font = '14px Inter';
        
        if (elements.showAge.checked && face.age) {
            ctx.fillText(`Idade: ${Math.round(face.age)}`, box[0], y);
            y -= 20;
        }
        
        if (elements.showGender.checked && face.gender) {
            const gender = face.gender === 'male' ? 'Masculino' : 'Feminino';
            ctx.fillText(`Gênero: ${gender}`, box[0], y);
            y -= 20;
        }
        
        if (elements.showEmotion.checked && face.emotion && face.emotion.length > 0) {
            const emotion = face.emotion[0];
            const emotionMap = {
                'happy': 'Feliz',
                'sad': 'Triste',
                'angry': 'Raiva',
                'fear': 'Medo',
                'surprise': 'Surpresa',
                'disgust': 'Nojo',
                'neutral': 'Neutro'
            };
            const emotionText = emotionMap[emotion.emotion] || emotion.emotion;
            ctx.fillText(`Emoção: ${emotionText} (${Math.round(emotion.score * 100)}%)`, box[0], y);
        }
    });
}

// Exibir resultados
function displayResults(faces) {
    if (faces.length === 0) {
        elements.resultsContainer.innerHTML = '<p class="no-results">Nenhuma face detectada</p>';
        return;
    }
    
    let html = '';
    faces.forEach((face, index) => {
        html += `
            <div class="face-result">
                <h4>Face ${index + 1}</h4>
                <div class="face-info">
                    <span>Confiança: <strong>${Math.round(face.score * 100)}%</strong></span>
                    <span>Posição: <strong>${Math.round(face.box[0])}, ${Math.round(face.box[1])}</strong></span>
                    ${face.age ? `<span>Idade: <strong>${Math.round(face.age)} anos</strong></span>` : ''}
                    ${face.gender ? `<span>Gênero: <strong>${face.gender === 'male' ? 'Masculino' : 'Feminino'}</strong></span>` : ''}
                    ${face.emotion && face.emotion.length > 0 ? `<span>Emoção: <strong>${getEmotionText(face.emotion[0].emotion)} (${Math.round(face.emotion[0].score * 100)}%)</strong></span>` : ''}
                </div>
            </div>
        `;
    });
    
    elements.resultsContainer.innerHTML = html;
}

// Capturar foto
function capturePhoto() {
    if (!video || video.paused || video.ended) return;
    
    const captureCanvas = document.createElement('canvas');
    const captureCtx = captureCanvas.getContext('2d');
    
    captureCanvas.width = video.videoWidth;
    captureCanvas.height = video.videoHeight;
    
    // Desenhar vídeo
    captureCtx.drawImage(video, 0, 0);
    
    // Desenhar detecções por cima
    captureCtx.drawImage(canvas, 0, 0);
    
    const dataURL = captureCanvas.toDataURL('image/jpeg', 0.9);
    const timestamp = new Date().toLocaleString('pt-BR');
    
    capturedPhotos.push({
        dataURL,
        timestamp
    });
    
    displayCapturedPhotos();
    addLog(`Foto capturada: ${timestamp}`, 'info');
}

// Exibir fotos capturadas
function displayCapturedPhotos() {
    if (capturedPhotos.length === 0) {
        elements.photosContainer.innerHTML = '<p class="no-photos">Nenhuma foto capturada</p>';
        return;
    }
    
    let html = '<div class="photo-grid">';
    capturedPhotos.forEach((photo, index) => {
        html += `
            <div class="captured-photo">
                <img src="${photo.dataURL}" alt="Foto ${index + 1}">
                <div class="photo-timestamp">${photo.timestamp}</div>
            </div>
        `;
    });
    html += '</div>';
    
    elements.photosContainer.innerHTML = html;
}

// Funções utilitárias
function clearCanvas() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
}

function clearResults() {
    elements.resultsContainer.innerHTML = '<p class="no-results">Nenhuma face detectada</p>';
}

function updateSystemStatus(status) {
    elements.systemStatus.textContent = status;
    elements.systemStatus.className = `status-value ${status.toLowerCase().replace(' ', '-')}`;
}

function updateFPS() {
    const now = performance.now();
    if (lastTime) {
        const fps = Math.round(1000 / (now - lastTime));
        elements.fpsCounter.textContent = fps;
    }
    lastTime = now;
}

function updateFaceCount(count) {
    elements.faceCount.textContent = count;
}

function updateProcessTime(time) {
    elements.processTime.textContent = `${time}ms`;
}

function getEmotionText(emotion) {
    const emotionMap = {
        'happy': 'Feliz',
        'sad': 'Triste',
        'angry': 'Raiva',
        'fear': 'Medo',
        'surprise': 'Surpresa',
        'disgust': 'Nojo',
        'neutral': 'Neutro'
    };
    return emotionMap[emotion] || emotion;
}

function addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString('pt-BR');
    const logEntry = document.createElement('p');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `[${timestamp}] ${message}`;
    
    elements.logsContainer.appendChild(logEntry);
    elements.logsContainer.scrollTop = elements.logsContainer.scrollHeight;
}

function clearLogs() {
    elements.logsContainer.innerHTML = '<p class="log-entry">Logs limpos</p>';
}

function hideLoadingOverlay() {
    elements.loadingOverlay.style.display = 'none';
}
