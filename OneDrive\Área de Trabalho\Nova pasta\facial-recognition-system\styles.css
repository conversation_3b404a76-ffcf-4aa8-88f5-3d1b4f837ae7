/* Reset e configurações globais */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Layout principal */
.main-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    margin-bottom: 30px;
}

/* Seção de vídeo */
.video-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.video-container {
    position: relative;
    width: 100%;
    max-width: 640px;
    margin: 0 auto 20px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

#video {
    width: 100%;
    height: auto;
    display: block;
}

#canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Controles */
.controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 25px;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-secondary {
    background: #f44336;
    color: white;
}

.btn-success {
    background: #2196F3;
    color: white;
}

.btn-info {
    background: #FF9800;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #666;
    border: 2px solid #ddd;
}

/* Configurações */
.settings {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.settings h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1rem;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
}

.checkbox-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

/* Seção de informações */
.info-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.status-panel,
.detection-results,
.captured-photos {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.status-panel h3,
.detection-results h3,
.captured-photos h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.status-item:last-child {
    border-bottom: none;
}

.label {
    font-weight: 500;
    color: #666;
}

.status-value {
    font-weight: 600;
    color: #333;
}

/* Resultados da detecção */
.face-result {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #4CAF50;
}

.face-result h4 {
    margin-bottom: 10px;
    color: #333;
}

.face-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 14px;
}

.face-info span {
    display: flex;
    justify-content: space-between;
}

/* Fotos capturadas */
.photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
}

.captured-photo {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.captured-photo img {
    width: 100%;
    height: auto;
    display: block;
}

.photo-timestamp {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px;
    font-size: 10px;
    text-align: center;
}

/* Seção de logs */
.logs-section {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.logs-section h3 {
    margin-bottom: 15px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

#logsContainer {
    background: #1e1e1e;
    color: #00ff00;
    padding: 15px;
    border-radius: 8px;
    height: 200px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    margin-bottom: 15px;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.log-entry.error {
    color: #ff4444;
}

.log-entry.warning {
    color: #ffaa00;
}

.log-entry.info {
    color: #00aaff;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #333;
    border-top: 5px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mensagens vazias */
.no-results,
.no-photos {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
}

/* Responsividade */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
    }
    
    .info-section {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        display: grid;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
    
    .checkbox-group {
        grid-template-columns: 1fr;
    }
}
