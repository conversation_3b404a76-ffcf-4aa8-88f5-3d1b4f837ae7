# @vladmandic/human  

  Version: **3.3.5**  
  Description: **Human: AI-powered 3D Face Detection & Rotation Tracking, Face Description & Recognition, Body Pose Tracking, 3D Hand & Finger Tracking, Iris Analysis, Age & Gender & Emotion Prediction, Gesture Recognition**  
  
  Author: **<PERSON> <<EMAIL>>**  
  License: **MIT**  
  Repository: **<https://github.com/vladmandic/human>**  
  
## Changelog
  
### **3.3.5** 2025/02/05 <EMAIL>


### **origin/main** 2024/10/24 <EMAIL>

- add human.draw.tensor method

### **3.3.4** 2024/10/24 <EMAIL>


### **3.3.3** 2024/10/14 <EMAIL>

- add loaded property to model stats and mark models not loaded correctly.
- release build

### **3.3.2** 2024/09/11 <EMAIL>

- full rebuild

### **3.3.1** 2024/09/11 <EMAIL>

- add config.face.detector.square option
- human 3.3 alpha test run
- human 3.3 alpha with new build environment
- release rebuild
- fix flazeface tensor scale and update build platform

### **3.2.2** 2024/04/17 <EMAIL>


### **release: 3.2.1** 2024/02/15 <EMAIL>


### **3.2.1** 2024/02/15 <EMAIL>


### **3.2.0** 2023/12/06 <EMAIL>

- set browser false when navigator object is empty
- https://github.com/vladmandic/human/issues/402

### **release: 3.1.2** 2023/09/18 <EMAIL>

- full rebuild

### **3.1.2** 2023/09/18 <EMAIL>

- major toolkit upgrade
- full rebuild
- major toolkit upgrade

### **3.1.1** 2023/08/05 <EMAIL>

- fixes plus tfjs upgrade for new release

### **3.0.7** 2023/06/12 <EMAIL>

- full rebuild
- fix memory leak in histogramequalization
- initial work on tracker

### **3.0.6** 2023/03/21 <EMAIL>

- add optional crop to multiple models
- fix movenet-multipose
- add electron detection
- fix gender-ssrnet-imdb
- add movenet-multipose workaround
- rebuild and publish
- add face.detector.minsize configurable setting
- add affectnet

### **3.0.5** 2023/02/02 <EMAIL>

- add gear-e models
- detect react-native
- redo blazeface annotations

### **3.0.4** 2023/01/29 <EMAIL>

- make naviator calls safe
- fix facedetector-only configs

### **3.0.3** 2023/01/07 <EMAIL>

- full rebuild

### **3.0.2** 2023/01/06 <EMAIL>

- default face.rotation disabled

### **release: 3.0.1** 2022/11/22 <EMAIL>


### **3.0.1** 2022/11/22 <EMAIL>

- support dynamic loads
- polish demos
- add facedetect demo and fix model async load
- enforce markdown linting
- cleanup git history
- default empty result
- refactor draw and models namespaces
- refactor distance
- add basic anthropometry
- added webcam id specification
- include external typedefs
- prepare external typedefs
- rebuild all
- include project files for types
- architectural improvements
- refresh dependencies
- add named exports
- add draw label templates
- reduce dev dependencies
- tensor rank strong typechecks
- rebuild dependencies

### **2.11.1** 2022/10/09 <EMAIL>

- add rvm segmentation model
- add human.webcam methods
- create funding.yml
- fix rotation interpolation

### **2.10.3** 2022/09/21 <EMAIL>

- add human.video method

### **2.10.2** 2022/09/11 <EMAIL>

- add node.js esm compatibility (#292)
- release

### **2.10.1** 2022/09/07 <EMAIL>

- release candidate
- add config flags
- test update
- release preview
- optimize startup sequence
- reorder backend init code
- test embedding
- embedding test
- add browser iife tests
- minor bug fixes and increased test coverage
- extend release tests
- add model load exception handling
- add softwarekernels config option
- expand type safety
- full eslint rule rewrite

### **2.9.4** 2022/08/20 <EMAIL>

- add browser test
- add tensorflow library detection
- fix wasm detection
- enumerate additional models
- release refresh

### **2.9.3** 2022/08/10 <EMAIL>

- rehault testing framework
- release refresh
- add insightface

### **2.9.2** 2022/08/08 <EMAIL>

- release rebuild

### **2.9.1** 2022/07/25 <EMAIL>

- full rebuild
- release cleanup
- tflite experiments
- add load monitor test
- beta for upcoming major release
- swtich to release version of tfjs
- placeholder for face contours
- improve face compare in main demo
- add webview support
- fix(gear): ensure gear.modelpath is used for loadmodel()
- npm default install should be prod only
- fix npm v7 compatibility
- add getmodelstats method
- rebuild
- release build

### **2.8.1** 2022/06/08 <EMAIL>

- webgpu and wasm optimizations
- add faceboxes prototype
- full rebuild

### **2.7.4** 2022/05/24 <EMAIL>


### **2.7.3** 2022/05/24 <EMAIL>

- add face.mesh.keepinvalid config flag
- initial work for new facemesh model

### **2.7.2** 2022/05/12 <EMAIL>

- fix demo when used with video files
- major release

### **2.7.1** 2022/05/09 <EMAIL>

- support 4k input
- add attention draw methods
- fix coloring function
- enable precompile as part of warmup
- prepare release beta
- change default face crop
- beta release 2.7
- refactor draw methods
- implement face attention model
- add electronjs demo
- rebuild

### **2.6.5** 2022/04/01 <EMAIL>

- bundle offscreencanvas types
- prototype precompile pass
- fix changelog generation
- fix indexdb config check

### **2.6.4** 2022/02/27 <EMAIL>

- fix types typo
- refresh
- add config option wasmplatformfetch

### **2.6.3** 2022/02/10 <EMAIL>

- rebuild

### **2.6.2** 2022/02/07 <EMAIL>

- release rebuild

### **2.6.1** 2022/01/20 <EMAIL>

- implement model caching using indexdb
- prototype global fetch handler
- fix face box and hand tracking when in front of face

### **2.5.8** 2022/01/14 <EMAIL>

- fix samples
- fix(src): typo
- change on how face box is calculated

### **2.5.7** 2021/12/27 <EMAIL>

- fix posenet
- release refresh

### **2.5.6** 2021/12/15 <EMAIL>

- strong type for string enums
- rebuild
- fix node detection in electron environment

### **2.5.5** 2021/12/01 <EMAIL>

- added human-motion
- add offscreencanvas typedefs
- release preview
- fix face box scaling on detection
- cleanup

### **2.5.4** 2021/11/22 <EMAIL>

- prototype blazepose detector
- minor fixes
- add body 3d interpolation
- edit blazepose keypoints
- new build process

### **2.5.3** 2021/11/18 <EMAIL>

- create typedef rollup
- optimize centernet
- cache frequent tf constants
- add extra face rotation prior to mesh
- release 2.5.2
- improve error handling

### **2.5.2** 2021/11/14 <EMAIL>

- fix mobilefacenet module
- fix gear and ssrnet modules
- fix for face crop when mesh is disabled
- implement optional face masking
- add similarity score range normalization
- add faceid demo
- documentation overhaul
- auto tensor shape and channels handling
- disable use of path2d in node
- add liveness module and facerecognition demo
- initial version of facerecognition demo
- rebuild
- add type defs when working with relative path imports
- disable humangl backend if webgl 1.0 is detected
- add additional hand gestures

### **2.5.1** 2021/11/08 <EMAIL>

- new human.compare api
- added links to release notes
- new frame change detection algorithm
- add histogram equalization
- implement wasm missing ops
- performance and memory optimizations
- fix react compatibility issues
- improve box rescaling for all modules
- improve precision using wasm backend
- refactor predict with execute
- patch tfjs type defs
- start 2.5 major version
- build and docs cleanup
- fix firefox bug

### **2.4.3** 2021/10/28 <EMAIL>

- additional human.performance counters

### **2.4.2** 2021/10/27 <EMAIL>

- add ts demo
- switch from es2018 to es2020 for main build
- switch to custom tfjs for demos
- release 2.4

### **2.4.1** 2021/10/25 <EMAIL>

- refactoring plus jsdoc comments
- increase face similarity match resolution
- time based caching
- turn on minification
- initial work on skiptime
- added generic types
- enhanced typing exports
- add optional autodetected custom wasm path

### **2.3.6** 2021/10/21 <EMAIL>

- fix for human.draw labels and typedefs
- refactor human.env to a class type
- add human.custom.esm using custom tfjs build

### **2.3.5** 2021/10/19 <EMAIL>

- removed direct usage of performance.now

### **2.3.4** 2021/10/19 <EMAIL>

- minor blazepose optimizations
- compress samples
- remove posenet from default package
- enhanced movenet postprocessing
- use transferrable buffer for worker messages
- add optional anti-spoofing module
- add node-match advanced example using worker thread pool
- package updates
- optimize image preprocessing
- set webgpu optimized flags
- major precision improvements to movenet and handtrack
- image processing fixes
- redesign body and hand caching and interpolation
- demo default config cleanup
- improve gaze and face angle visualizations in draw
- release 2.3.1

### **2.3.1** 2021/10/06 <EMAIL>

- workaround for chrome offscreencanvas bug
- fix backend conflict in webworker
- add blazepose v2 and add annotations to body results
- fix backend order initialization
- added docker notes
- breaking change: new similarity and match methods
- tweaked default values
- enable handtrack as default model
- redesign face processing
- refactoring
- define app specific types
- implement box caching for movenet
- autodetect number of bodies and hands
- upload new samples
- new samples gallery and major code folder restructure
- new release

### **2.2.3** 2021/09/24 <EMAIL>

- optimize model loading
- support segmentation for nodejs
- redo segmentation and handtracking
- prototype handtracking
- automated browser tests
- support for dynamic backend switching
- initial automated browser tests
- enhanced automated test coverage
- more automated tests
- added configuration validation
- prevent validation failed on some model combinations
- webgl exception handling

### **2.2.2** 2021/09/17 <EMAIL>

- experimental webgl status monitoring
- major release

### **2.2.1** 2021/09/16 <EMAIL>

- add vr model demo
- all tests passing
- redefine draw helpers interface
- add simple webcam and webrtc demo
- added visual results browser to demo
- reorganize tfjs bundle
- experimental custom tfjs bundle - disabled
- add platform and backend capabilities detection
- enhanced automated tests
- enable canvas patching for nodejs
- full ts strict typechecks
- fix multiple memory leaks
- modularize human class and add model validation
- add dynamic kernel op detection
- added human.env diagnostic class
- minor typos
- release candidate
- parametrize face config
- mark all config items as optional
- redefine config and result interfaces
- fix usge of string enums
- start using partial definitions
- implement event emitters
- fix iife loader
- simplify dependencies
- change build process
- add benchmark info
- simplify canvas handling in nodejs
- full rebuild

### **2.1.5** 2021/08/31 <EMAIL>

- added demo node-canvas
- dynamically generate default wasm path
- implement finger poses in hand detection and gestures
- implemented movenet-multipose model

### **2.1.4** 2021/08/19 <EMAIL>

- add static type definitions to main class
- fix interpolation overflow
- rebuild full
- improve face box caching
- strict type checks
- add webgu checks
- experimental webgpu support
- add experimental webgu demo
- add backend initialization checks
- complete async work
- list detect cameras
- switch to async data reads

### **2.1.3** 2021/08/12 <EMAIL>

- fix centernet & update blazeface
- minor update
- replace movenet with lightning-v4
- enable webgl uniform support for faster warmup

### **2.1.2** 2021/07/29 <EMAIL>

- fix unregistered ops in tfjs
- fix typo
- rebuild new release

### **2.1.1** 2021/07/29 <EMAIL>

- add note on manually disping tensor
- modularize model loading

### **2.0.3** 2021/06/18 <EMAIL>

- fix demo paths
- added multithreaded demo

### **2.0.2** 2021/06/14 <EMAIL>

- reorganize demos
- fix centernet box width & height
- add body segmentation sample
- add release notes
- release 2.0

### **2.0.1** 2021/06/08 <EMAIL>

- add video drag&drop capability
- modularize build platform
- custom build tfjs from sources
- modularize build platform
- enable body segmentation and background replacement in demo
- minor git corruption
- unified build
- enable body segmentation and background replacement
- work on body segmentation
- added experimental body segmentation module
- add meet and selfie models
- add live hints to demo
- switch worker from module to iife importscripts
- release candidate
- added samples to git
- implemented drag & drop for image processing
- release candidate
- breaking changes to results.face output properties
- breaking changes to results.object output properties
- breaking changes to results.hand output properties
- breaking changes to results.body output properties
- implemented human.next global interpolation method
- finished draw buffering and smoothing and enabled by default
- implemented service worker
- release candidate
- added usage restrictions
- quantize handdetect model
- added experimental movenet-lightning and removed blazepose from default dist
- added experimental face.rotation.gaze
- fix and optimize for mobile platform
- lock typescript to 4.2 due to typedoc incompatibility with 4.3

### **1.9.4** 2021/05/27 <EMAIL>

- fix demo facecompare
- webhint and lighthouse optimizations
- add camera startup diag messages
- implemented unified result.persons that combines face, body and hands for each person
- added experimental results interpolation for smooth draw operations

### **1.9.3** 2021/05/23 <EMAIL>

- use green weighted for input diff calculation
- implement experimental drawoptions.bufferedoutput and bufferedfactor
- use explicit tensor interface
- add tfjs types and remove all instances of any
- enhance strong typing
- rebuild all for release

### **1.9.2** 2021/05/22 <EMAIL>

- add id and boxraw on missing objects
- restructure results strong typing

### **1.9.1** 2021/05/21 <EMAIL>

- caching improvements
- add experimental mb3-centernet object detection
- individual model skipframes values still max high threshold for caching
- config.videooptimized has been removed and config.cachesensitivity has been added instead
- caching determination is now dynamic based on detection of input change and not based on input types
- human 1.9.0 beta with breaking changes regarding caching

### **1.8.5** 2021/05/18 <EMAIL>

- add node-video sample
- add node-webcam demo
- fix node build and update model signatures

### **1.8.4** 2021/05/11 <EMAIL>


### **1.8.3** 2021/05/05 <EMAIL>

- switch posenet weights

### **1.8.2** 2021/05/04 <EMAIL>

- release 1.8 with major changes and tfjs 3.6.0

### **1.8.1** 2021/04/30 <EMAIL>

- blazeface optimizations
- add hand labels in draw
- cleanup demo workflow
- convert blazeface to module
- version 1.8 release candidate
- build nodejs deliverables in non-minified form
- stop building sourcemaps for nodejs deliverables
- remove deallocate, profile, scoped
- replaced maxfaces, maxdetections, maxhands, maxresults with maxdetected
- replaced nmsradius with built-in default
- unified minconfidence and scorethresdold as minconfidence
- add exception handlers to all demos
- remove blazeface-front and add unhandledrejection handler
- major update for 1.8 release candidate
- enable webworker detection

### **1.7.1** 2021/04/25 <EMAIL>

- remove obsolete binary models
- enable cross origin isolation
- rewrite posenet decoder
- remove efficientpose
- major version rebuild

### **1.6.1** 2021/04/22 <EMAIL>

- add npmrc
- added filter.flip feature
- added demo load image from http
- mobile demo optimization and iris gestures
- full rebuild
- new look
- added benchmarks
- added node-multiprocess demo
- fix image orientation
- flat app style
- add full nodejs test coverage

### **1.5.2** 2021/04/14 <EMAIL>

- experimental node-wasm support

### **1.5.1** 2021/04/13 <EMAIL>

- fix for safari imagebitmap
- refactored human.config and human.draw

### **1.4.3** 2021/04/12 <EMAIL>

- implement webrtc

### **1.4.2** 2021/04/12 <EMAIL>

- added support for multiple instances of human
- fix typedoc
- exception handling

### **1.4.1** 2021/04/09 <EMAIL>

- add modelbasepath option

### **1.3.5** 2021/04/06 <EMAIL>

- add dynamic viewport and fix web worker
- add cdn links

### **1.3.4** 2021/04/04 <EMAIL>

- implement webhint

### **1.3.3** 2021/04/03 <EMAIL>

- fix linting and tests

### **1.3.2** 2021/04/02 <EMAIL>

- input type validation
- normalize all scores

### **1.3.1** 2021/03/30 <EMAIL>

- added face3d demo
- initial work on face3d three.js demo
- enable buffering
- new icons
- new serve module and demo structure
- move gl flags to correct location
- minor rotation calculation fix
- remove debug output
- new face rotation calculations
- cleanup
- rotationmatrixtoeulerangle, and fixes
- experimental: add efficientpose
- implement nanodet
- face rotation matrix
- start working on efficientpose

### **1.2.5** 2021/03/25 <EMAIL>

- fix broken exports
- added face matching example to docs
- improve fact matching

### **1.2.4** 2021/03/23 <EMAIL>


### **1.2.3** 2021/03/21 <EMAIL>


### **1.2.2** 2021/03/21 <EMAIL>

- precise face rotation

### **1.2.1** 2021/03/21 <EMAIL>

- new module: face description

### **1.1.11** 2021/03/21 <EMAIL>

- refactor face classes

### **1.1.10** 2021/03/18 <EMAIL>

- cleanup
- redefine tensor
- enforce types
- regen type declarations
- switch to single jumbo dts
- type definitions

### **1.1.9** 2021/03/17 <EMAIL>

- fix box clamping and raw output
- hierarchical readme notes

### **1.1.8** 2021/03/17 <EMAIL>

- add experimental nanodet object detection
- full models signature

### **1.1.7** 2021/03/16 <EMAIL>

- fix for seedrandom
- custom typedoc

### **1.1.6** 2021/03/15 <EMAIL>

- implement human.match and embedding demo

### **1.1.5** 2021/03/15 <EMAIL>

- full rebuild

### **1.1.4** 2021/03/14 <EMAIL>

- fix broken build

### **1.1.3** 2021/03/14 <EMAIL>

- added api specs
- add typedocs and types
- strong typings

### **1.1.2** 2021/03/12 <EMAIL>

- distance based on minkowski space and limited euclidean space
- guard against invalid input images

### **1.1.1** 2021/03/12 <EMAIL>

- switched face embedding to mobileface

### **1.0.4** 2021/03/11 <EMAIL>

- add face return tensor
- add test for face descriptors
- wip on embedding
- simplify face box coordinate calculations
- annotated models and removed gender-ssrnet
- autodetect inputsizes

### **1.0.3** 2021/03/10 <EMAIL>

- strong typing for public classes and hide private classes
- enhanced age, gender, emotion detection
- full rebuild

### **1.0.2** 2021/03/09 <EMAIL>

- remove blazeface-front, blazepose-upper, faceboxes
- remove blazeface-front and faceboxes

### **1.0.1** 2021/03/09 <EMAIL>

- fix for face detector when mesh is disabled
- optimize for npm
- 0.40.9
- fix performance issue when running with low confidence
- 0.40.8
- 0.40.7
- implemented 3d face angle calculations
- 0.40.6
- add curve draw output
- 0.40.5
- fix human.draw
- 0.40.4
- fix demo
- 0.40.3
- 0.40.2
- added blazepose-upper
- 0.40.1
- implement blazepose and update demos
- add todo list
- 0.30.6
- fine tuning age and face models
- 0.30.5
- add debug logging flag
- 0.30.4
- added skipinitial flag
- 0.30.3
- typo
- 0.30.2
- rebuild
- fix typo
- 0.30.1
- 0.20.11
- 0.20.10
- 0.20.9
- 0.20.8
- 0.20.7
- build fix
- 0.20.6
- embedding fix
- 0.20.5
- fix imagefx and add dev builds
- 0.20.4
- 0.20.3
- rebuild
- 0.20.2
- merge branch 'main' of https://github.com/vladmandic/human into main
- create codeql-analysis.yml
- create security.md
- add templates
- 0.20.1
- menu fixes
- convert to typescript
- 0.11.5
- added faceboxes alternative model
- 0.11.4
- 0.11.3
- 0.11.2
- added warmup for nodejs
- 0.11.1
- 0.10.2
- 0.10.1

### **0.9.26** 2021/01/18 <EMAIL>

- fix face detection when mesh is disabled
- version bump

### **0.9.25** 2021/01/13 <EMAIL>

- added humangl custom backend
- rebuild
- code cleanup and enable minification
- fix safari incopatibility

### **0.9.24** 2021/01/12 <EMAIL>

- work on blazepose
- full rebuild

### **0.9.23** 2021/01/11 <EMAIL>

- added iris gesture
- fix emotion labels
- full rebuild

### **0.9.22** 2021/01/05 <EMAIL>

- remove iris coords if iris is disabled
- web worker fix

### **0.9.21** 2021/01/03 <EMAIL>


### **0.9.20** 2021/01/03 <EMAIL>

- stricter linting, fix face annotations

### **0.9.19** 2020/12/23 <EMAIL>

- added rawbox and rawmesh
- variable name changes, setting .rawcoords only if necessary
- option to return raw data (mesh, box) for facemesh / "preserve aspect ratio" fix from facemesh upstream

### **0.9.18** 2020/12/16 <EMAIL>

- add z axis scaling
- major work on body module
- republish due to tfjs 2.8.0 issues

### **0.9.17** 2020/12/15 <EMAIL>

- added custom webgl backend

### **0.9.16** 2020/12/12 <EMAIL>

- change default ports

### **0.9.15** 2020/12/11 <EMAIL>

- improved caching and warmup
- rebuild

### **0.9.14** 2020/12/10 <EMAIL>

- conditional hand rotation
- staggered skipframes

### **0.9.13** 2020/12/08 <EMAIL>

- implemented face and hand boundary checks
- embedded sample for warmup
- switch to central logger

### **0.9.12** 2020/11/26 <EMAIL>

- minor compatibility fixes

### **0.9.11** 2020/11/23 <EMAIL>

- implement multi-person gestures
- modularize pipeline models

### **0.9.10** 2020/11/21 <EMAIL>

- changed build for optimized node & browser

### **0.9.9** 2020/11/21 <EMAIL>

- new screenshots
- camera exception handling

### **0.9.8** 2020/11/19 <EMAIL>

- force f16 textures
- bugfix embedding check

### **0.9.7** 2020/11/19 <EMAIL>

- ui redesign

### **0.9.6** 2020/11/18 <EMAIL>

- optimize camera resize on mobile
- completed tfjs wrapper

### **0.9.5** 2020/11/17 <EMAIL>

- fix serious performance bug around skipframes
- swtich to custom tfjs bundle

### **0.9.4** 2020/11/17 <EMAIL>

- swtich to tfjs source import

### **0.9.3** 2020/11/16 <EMAIL>

- switched to minified build
- web worker fixes
- full rebuild

### **0.9.2** 2020/11/14 <EMAIL>

- fix camera restart on resize

### **0.9.1** 2020/11/13 <EMAIL>

- version bump
- full rebuild
- implemented face embedding
- added internal benchmark tool

### **0.8.8** 2020/11/12 <EMAIL>

- reduced bundle size
- implemented buffered processing
- fix for conditional model loading

### **0.8.7** 2020/11/11 <EMAIL>

- added performance notes
- added notes on models
- fix bug in async ops and change imports
- fix wiki links

### **0.8.6** 2020/11/09 <EMAIL>

- add wasm bundle

### **0.8.5** 2020/11/09 <EMAIL>

- reimplemented blazeface processing

### **0.8.4** 2020/11/09 <EMAIL>

- added additional gestures
- implemented blink detection
- fix wasm module

### **0.8.3** 2020/11/08 <EMAIL>

- refresh
- optimizations

### **0.8.2** 2020/11/08 <EMAIL>

- fix typos
- commit

### **0.8.1** 2020/11/07 <EMAIL>

- fix hand detection performance
- optimized model loader
- merge branch 'main' of https://github.com/vladmandic/human into main
- created wiki
- optimize font resizing
- fix nms sync call

### **0.7.6** 2020/11/06 <EMAIL>

- fixed memory leaks and updated docs
- model tuning
- cache invalidation improvements
- full async operations

### **0.7.5** 2020/11/05 <EMAIL>

- implemented dev-server

### **0.7.4** 2020/11/05 <EMAIL>

- fix canvas size on different orientation
- switched from es2020 to es2018 build target

### **0.7.3** 2020/11/05 <EMAIL>

- optimized camera and mobile layout
- fixed worker and filter compatibility

### **0.7.2** 2020/11/04 <EMAIL>

- major work on handpose model

### **0.7.1** 2020/11/04 <EMAIL>

- changed demo build process

### **0.6.7** 2020/11/04 <EMAIL>

- implemented simple gesture recognition

### **0.6.6** 2020/11/04 <EMAIL>

- remove debug code

### **0.6.5** 2020/11/04 <EMAIL>

- redo hand detection

### **0.6.4** 2020/11/03 <EMAIL>

- added manifest

### **0.6.3** 2020/11/03 <EMAIL>

- enhanced processing resolution
- fix pause restart
- complete model refactoring
- fixed typo

### **0.6.2** 2020/11/02 <EMAIL>

- optimized demo
- package update

### **0.6.1** 2020/11/02 <EMAIL>

- major performance improvements for all models
- revert "optimized canvas handling"
- optimized canvas handling
- minor optimization to imagefx
- fix demo image sample
- added tfjs-vis to distribution

### **0.5.5** 2020/11/01 <EMAIL>

- changed defaults

### **0.5.4** 2020/11/01 <EMAIL>

- implemented memory profiler

### **0.5.3** 2020/10/30 <EMAIL>

- improved debug logging

### **0.5.2** 2020/10/30 <EMAIL>

- added wasm and webgpu backends

### **0.5.1** 2020/10/30 <EMAIL>

- improve demo line continous draws

### **0.4.10** 2020/10/30 <EMAIL>

- fix for seedrandom

### **0.4.9** 2020/10/29 <EMAIL>


### **0.4.8** 2020/10/28 <EMAIL>

- revert "updated menu handler"

### **0.4.7** 2020/10/27 <EMAIL>


### **0.4.6** 2020/10/27 <EMAIL>

- fix firefox compatibility bug

### **0.4.5** 2020/10/27 <EMAIL>


### **0.4.4** 2020/10/27 <EMAIL>

- implelented input resizing

### **0.4.3** 2020/10/22 <EMAIL>


### **0.4.2** 2020/10/20 <EMAIL>

- log initialization

### **0.4.1** 2020/10/19 <EMAIL>

- breaking change: convert to object class
- compatibility notes

### **0.3.9** 2020/10/18 <EMAIL>

- implemented image filters
- pure tensor pipeline without image converts
- autodetect skipframes

### **0.3.8** 2020/10/17 <EMAIL>

- new menu layout

### **0.3.7** 2020/10/17 <EMAIL>

- added diagnostics output
- parallelized agegender operations

### **0.3.6** 2020/10/17 <EMAIL>

- fixed webcam initialization
- fixed memory leaks and added scoped runs
- modularized draw
- added state handling
- refactored package file layout

### **0.3.5** 2020/10/16 <EMAIL>

- added auto-generated changelog

### **0.3.4** 2020/10/16 <EMAIL>

- added camera selection
- optimized blazeface anchors
- added error handling

### **0.3.3** 2020/10/15 <EMAIL>

- added blazeface back and front models

### **0.3.2** 2020/10/15 <EMAIL>

- reduced web worker latency
- added debugging and versioning
- optimized demos and added scoped runs
- added multi backend support

### **0.3.1** 2020/10/14 <EMAIL>


### **0.2.10** 2020/10/14 <EMAIL>

- added emotion backend
- module parametrization and performance monitoring
- implemented multi-hand support
- fixed documentation typos

### **0.2.9** 2020/10/13 <EMAIL>

- added node build and demo

### **0.2.8** 2020/10/13 <EMAIL>


### **0.2.7** 2020/10/13 <EMAIL>

- new examples

### **0.2.6** 2020/10/13 <EMAIL>

- enable all models by default

### **0.2.5** 2020/10/12 <EMAIL>

- fixed memory leak

### **0.2.4** 2020/10/12 <EMAIL>


### **0.2.3** 2020/10/12 <EMAIL>


### **0.2.2** 2020/10/12 <EMAIL>


### **0.2.1** 2020/10/12 <EMAIL>

- added sample image
- initial public commit
- initial commit
