<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Human</title>
    <meta name="viewport" content="width=device-width" id="viewport">
    <meta name="keywords" content="Human">
    <meta name="description" content="Human: Demo; Author: <PERSON> <https://github.com/vladmandic>">
    <link rel="manifest" href="../manifest.webmanifest">
    <link rel="shortcut icon" href="../../favicon.ico" type="image/x-icon">
    <style>
      @font-face { font-family: 'Lato'; font-display: swap; font-style: normal; font-weight: 100; src: local('Lato'), url('../../assets/lato-light.woff2') }
      body { font-family: 'Lato', 'Segoe UI'; font-size: 16px; font-variant: small-caps; margin: 0; background: black; color: white; overflow: hidden; width: 100vw; height: 100vh; }
    </style>
  </head>
  <body>
    <canvas id="canvas" style="margin: 0 auto; width: 100%"></canvas>
    <pre id="log" style="padding: 8px; position: fixed; bottom: 0"></pre>
    <script type="module">
      import * as H from '../../dist/human.esm.js'; // equivalent of import @vladmandic/Human

      const humanConfig = { // user configuration for human, used to fine-tune behavior
        modelBasePath: '../../models', // models can be loaded directly from cdn as well
        filter: { enabled: true, equalization: true, flip: false },
        face: { enabled: true, detector: { rotation: false }, mesh: { enabled: true }, attention: { enabled: false }, iris: { enabled: true }, description: { enabled: true }, emotion: { enabled: true } },
        body: { enabled: true },
        hand: { enabled: true },
        gesture: { enabled: true },
        object: { enabled: false },
        segmentation: { enabled: false },
      };
      const human = new H.Human(humanConfig); // create instance of human with overrides from user configuration
      const canvas = document.getElementById('canvas'); // output canvas to draw both webcam and detection results

      async function drawLoop() { // main screen refresh loop
        const interpolated = human.next(); // get smoothened result using last-known results which are continously updated based on input webcam video
        human.draw.canvas(human.webcam.element, canvas); // draw webcam video to screen canvas // better than using procesed image as this loop happens faster than processing loop
        await human.draw.all(canvas, interpolated); // draw labels, boxes, lines, etc.
        setTimeout(drawLoop, 30); // use to slow down refresh from max refresh rate to target of 1000/30 ~ 30 fps
      }

      async function main() { // main entry point
        document.getElementById('log').innerHTML = `human version: ${human.version} | tfjs version: ${human.tf.version['tfjs-core']}<br>platform: ${human.env.platform} | agent ${human.env.agent}`;
        await human.webcam.start({ crop: true }); // find webcam and start it
        human.video(human.webcam.element); // instruct human to continously detect video frames
        canvas.width = human.webcam.width; // set canvas resolution to input webcam native resolution
        canvas.height = human.webcam.height;
        canvas.onclick = async () => { // pause when clicked on screen and resume on next click
          if (human.webcam.paused) await human.webcam.play();
          else human.webcam.pause();
        };
        await drawLoop(); // start draw loop
      }

      window.onload = main;
    </script>  
  </body>
</html>
