<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Human</title>
    <!-- <meta http-equiv="content-type" content="text/html; charset=utf-8"> -->
    <meta name="viewport" content="width=device-width, shrink-to-fit=yes">
    <meta name="keywords" content="Human">
    <meta name="application-name" content="Human">
    <meta name="description" content="Human: 3D Face Detection, Body Pose, Hand & Finger Tracking, Iris Tracking, Age & Gender Prediction, Emotion Prediction & Gesture Recognition; Author: <PERSON> <https://github.com/vladmandic>">
    <meta name="msapplication-tooltip" content="Human: 3D Face Detection, Body Pose, Hand & Finger Tracking, Iris Tracking, Age & Gender Prediction, Emotion Prediction & Gesture Recognition; Author: <PERSON> <https://github.com/vladmandic>">
    <meta name="theme-color" content="#000000">
    <link rel="manifest" href="../manifest.webmanifest">
    <link rel="shortcut icon" href="../../favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="../../assets/icon.png">
    <script src="./facematch.js" type="module"></script>
    <style>
      img { object-fit: contain; }
      @font-face { font-family: 'Lato'; font-display: swap; font-style: normal; font-weight: 100; src: local('Lato'), url('../../assets/lato-light.woff2') }
      html { font-family: 'Lato', 'Segoe UI'; font-size: 24px; font-variant: small-caps; }
      body { margin: 24px; background: black; color: white; overflow: hidden; text-align: -webkit-center; min-height: 100%; max-height: 100%; }
      ::-webkit-scrollbar { height: 8px; border: 0; border-radius: 0; }
      ::-webkit-scrollbar-thumb { background: grey }
      ::-webkit-scrollbar-track { margin: 3px; }
      .orig { width: 200px; height: 200px; padding-bottom: 20px; filter: blur(16px); transition : all 0.5s ease; }
      .text { margin: 24px; }
      .face { width: 128px; height: 128px; margin: 2px; padding: 2px; cursor: grab; transform: scale(1.00); transition : all 0.3s ease; }
      .face:hover { filter: grayscale(1); transform: scale(1.08); transition : all 0.3s ease; }   
    </style>
  </head>
  <body>
    <div style="display: block">
      <div style="display: flex">
        <div style="min-width: 400px">
          <div class="text" id="title"></div>
          <canvas id="orig" class="orig"></canvas>
          <div id="desc" style="font-size: 0.8rem; text-align: left;"></div>
        </div>
        <div style="width: 20px"></div>
        <div>
          <div class="text">Input Images</div>
          <div id="images" style="display: flex; width: 60vw; overflow-x: auto; overflow-y: hidden; scroll-behavior: smooth"></div>
        </div>
      </div>
      <div id="list" style="height: 10px"></div>
      <div class="text">Select person to sort by similarity and get a known face match</div>
      <div id="faces" style="height: 50vh; overflow-y: auto"></div>
    </div>
  </body>
</html>
