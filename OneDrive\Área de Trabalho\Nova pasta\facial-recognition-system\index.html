<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Reconhecimento Facial - Human AI</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-eye"></i> Sistema de Reconhecimento Facial</h1>
            <p>Powered by Human AI - vladimandic</p>
        </header>

        <div class="main-content">
            <div class="video-section">
                <div class="video-container">
                    <video id="video" autoplay muted playsinline></video>
                    <canvas id="canvas"></canvas>
                    <div class="overlay" id="overlay"></div>
                </div>
                
                <div class="controls">
                    <button id="startBtn" class="btn btn-primary">
                        <i class="fas fa-play"></i> Iniciar Câmera
                    </button>
                    <button id="stopBtn" class="btn btn-secondary" disabled>
                        <i class="fas fa-stop"></i> Parar
                    </button>
                    <button id="captureBtn" class="btn btn-success" disabled>
                        <i class="fas fa-camera"></i> Capturar Foto
                    </button>
                    <button id="toggleDetection" class="btn btn-info" disabled>
                        <i class="fas fa-search"></i> Pausar Detecção
                    </button>
                </div>

                <div class="settings">
                    <h3><i class="fas fa-cog"></i> Configurações</h3>
                    <div class="setting-group">
                        <label for="confidenceSlider">Confiança Mínima: <span id="confidenceValue">0.5</span></label>
                        <input type="range" id="confidenceSlider" min="0.1" max="1" step="0.1" value="0.5">
                    </div>
                    <div class="setting-group">
                        <label for="maxFaces">Máximo de Faces: <span id="maxFacesValue">5</span></label>
                        <input type="range" id="maxFaces" min="1" max="10" step="1" value="5">
                    </div>
                    <div class="checkbox-group">
                        <label><input type="checkbox" id="showAge" checked> Mostrar Idade</label>
                        <label><input type="checkbox" id="showGender" checked> Mostrar Gênero</label>
                        <label><input type="checkbox" id="showEmotion" checked> Mostrar Emoção</label>
                        <label><input type="checkbox" id="showMesh" checked> Mostrar Malha Facial</label>
                    </div>
                </div>
            </div>

            <div class="info-section">
                <div class="status-panel">
                    <h3><i class="fas fa-info-circle"></i> Status do Sistema</h3>
                    <div class="status-item">
                        <span class="label">Status:</span>
                        <span id="systemStatus" class="status-value">Desconectado</span>
                    </div>
                    <div class="status-item">
                        <span class="label">FPS:</span>
                        <span id="fpsCounter" class="status-value">0</span>
                    </div>
                    <div class="status-item">
                        <span class="label">Faces Detectadas:</span>
                        <span id="faceCount" class="status-value">0</span>
                    </div>
                    <div class="status-item">
                        <span class="label">Tempo de Processamento:</span>
                        <span id="processTime" class="status-value">0ms</span>
                    </div>
                </div>

                <div class="detection-results">
                    <h3><i class="fas fa-chart-bar"></i> Resultados da Detecção</h3>
                    <div id="resultsContainer">
                        <p class="no-results">Nenhuma face detectada</p>
                    </div>
                </div>

                <div class="captured-photos">
                    <h3><i class="fas fa-images"></i> Fotos Capturadas</h3>
                    <div id="photosContainer">
                        <p class="no-photos">Nenhuma foto capturada</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="logs-section">
            <h3><i class="fas fa-terminal"></i> Logs do Sistema</h3>
            <div id="logsContainer">
                <p class="log-entry">Sistema inicializado</p>
            </div>
            <button id="clearLogs" class="btn btn-outline">
                <i class="fas fa-trash"></i> Limpar Logs
            </button>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>Carregando Human AI...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="node_modules/@vladmandic/human/dist/human.esm.js"></script>
    <script type="module" src="script.js"></script>
</body>
</html>
