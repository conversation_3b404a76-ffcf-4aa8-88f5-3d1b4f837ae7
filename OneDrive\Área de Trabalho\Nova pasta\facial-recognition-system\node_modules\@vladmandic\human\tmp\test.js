const fs = require('fs');
const H = require('../dist/human.node.js');

const config = {
  debug: true,
  modelBasePath: 'https://vladmandic.github.io/human-models/models/',
  body: { enabled: false },
  hand: { enabled: false },
  face: {
    enabled: true,
    detector: {
      return: true,
      enabled: true,
      rotation: false,
      maxDetected: 10,
      minConfidence: 0.6,
    },
    mesh: { enabled: false },
    iris: { enabled: false },
    description: { enabled: true },
    emotion: { enabled: false },
  },
};

const human = new H.Human(config);
let i = 0;

async function detect(imgFile) {
  const imgBuffer = fs.readFileSync(imgFile);
  const imgTensor = human.tf.node.decodeImage(imgBuffer);
  const res = await human.detect(imgTensor);
  if (res?.face) {
    console.log(`loop=${i} faces=${res.face.length} tensors=${human.tf.engine().memory().numTensors}`);
    res.face.forEach((f) => human.tf.dispose(f.tensor)); // only needed if return:true
  }
  human.tf.dispose(imgTensor);
  return res.face;
}

async function loop(imgFile) {
  for (i = 0; i < 99; i++) {
    await detect(imgFile);
  }
}

loop('../samples/in/group-2.jpg');
