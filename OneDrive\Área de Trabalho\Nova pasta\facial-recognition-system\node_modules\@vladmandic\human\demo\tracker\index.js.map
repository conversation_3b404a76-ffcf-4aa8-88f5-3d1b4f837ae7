{"version": 3, "sources": ["index.ts", "tracker.js"], "sourcesContent": ["/**\n * Human demo for browsers\n * @default Human Library\n * @summary <https://github.com/vladmandic/human>\n * <AUTHOR>\n * @copyright <https://github.com/vladmandic>\n * @license MIT\n */\n\nimport * as H from '../../dist/human.esm.js'; // equivalent of @vladmandic/Human\nimport tracker from './tracker.js';\n\nconst humanConfig: Partial<H.Config> = { // user configuration for human, used to fine-tune behavior\n  debug: true,\n  backend: 'webgl',\n  // cacheSensitivity: 0,\n  // cacheModels: false,\n  // warmup: 'none',\n  modelBasePath: 'https://vladmandic.github.io/human-models/models',\n  filter: { enabled: true, equalization: false, flip: false },\n  face: {\n    enabled: true,\n    detector: { rotation: false, maxDetected: 10, minConfidence: 0.3 },\n    mesh: { enabled: true },\n    attention: { enabled: false },\n    iris: { enabled: false },\n    description: { enabled: false },\n    emotion: { enabled: false },\n    antispoof: { enabled: false },\n    liveness: { enabled: false },\n  },\n  body: { enabled: false, maxDetected: 6, modelPath: 'movenet-multipose.json' },\n  hand: { enabled: false },\n  object: { enabled: false, maxDetected: 10 },\n  segmentation: { enabled: false },\n  gesture: { enabled: false },\n};\n\ninterface TrackerConfig {\n  unMatchedFramesTolerance: number, // number of frame when an object is not matched before considering it gone; ignored if fastDelete is set\n  iouLimit: number, // exclude things from beeing matched if their IOU less than; 1 means total overlap; 0 means no overlap\n  fastDelete: boolean, // remove new objects immediately if they could not be matched in the next frames; if set, ignores unMatchedFramesTolerance\n  distanceLimit: number, // distance limit for matching; if values need to be excluded from matching set their distance to something greater than the distance limit\n  matchingAlgorithm: 'kdTree' | 'munkres', // algorithm used to match tracks with new detections\n}\n\ninterface TrackerResult {\n  id: number,\n  confidence: number,\n  bearing: number,\n  isZombie: boolean,\n  name: string,\n  x: number,\n  y: number,\n  w: number,\n  h: number,\n}\n\nconst trackerConfig: TrackerConfig = {\n  unMatchedFramesTolerance: 100,\n  iouLimit: 0.05,\n  fastDelete: false,\n  distanceLimit: 1e4,\n  matchingAlgorithm: 'kdTree',\n};\n\nconst human = new H.Human(humanConfig); // create instance of human with overrides from user configuration\n\nconst dom = { // grab instances of dom objects so we dont have to look them up later\n  video: document.getElementById('video') as HTMLVideoElement,\n  canvas: document.getElementById('canvas') as HTMLCanvasElement,\n  log: document.getElementById('log') as HTMLPreElement,\n  fps: document.getElementById('status') as HTMLPreElement,\n  tracker: document.getElementById('tracker') as HTMLInputElement,\n  interpolation: document.getElementById('interpolation') as HTMLInputElement,\n  config: document.getElementById('config') as HTMLFormElement,\n  ctx: (document.getElementById('canvas') as HTMLCanvasElement).getContext('2d') as CanvasRenderingContext2D,\n};\nconst timestamp = { detect: 0, draw: 0, tensors: 0, start: 0 }; // holds information used to calculate performance and possible memory leaks\nconst fps = { detectFPS: 0, drawFPS: 0, frames: 0, averageMs: 0 }; // holds calculated fps information for both detect and screen refresh\n\nconst log = (...msg) => { // helper method to output messages\n  dom.log.innerText += msg.join(' ') + '\\n';\n  console.log(...msg); // eslint-disable-line no-console\n};\nconst status = (msg) => dom.fps.innerText = msg; // print status element\n\nasync function detectionLoop() { // main detection loop\n  if (!dom.video.paused && dom.video.readyState >= 2) {\n    if (timestamp.start === 0) timestamp.start = human.now();\n    // log('profiling data:', await human.profile(dom.video));\n    await human.detect(dom.video, humanConfig); // actual detection; were not capturing output in a local variable as it can also be reached via human.result\n    const tensors = human.tf.memory().numTensors; // check current tensor usage for memory leaks\n    if (tensors - timestamp.tensors !== 0) log('allocated tensors:', tensors - timestamp.tensors); // printed on start and each time there is a tensor leak\n    timestamp.tensors = tensors;\n    fps.detectFPS = Math.round(1000 * 1000 / (human.now() - timestamp.detect)) / 1000;\n    fps.frames++;\n    fps.averageMs = Math.round(1000 * (human.now() - timestamp.start) / fps.frames) / 1000;\n  }\n  timestamp.detect = human.now();\n  requestAnimationFrame(detectionLoop); // start new frame immediately\n}\n\nfunction drawLoop() { // main screen refresh loop\n  if (!dom.video.paused && dom.video.readyState >= 2) {\n    const res: H.Result = dom.interpolation.checked ? human.next(human.result) : human.result; // interpolate results if enabled\n    let tracking: H.FaceResult[] | H.BodyResult[] | H.ObjectResult[] = [];\n    if (human.config.face.enabled) tracking = res.face;\n    else if (human.config.body.enabled) tracking = res.body;\n    else if (human.config.object.enabled) tracking = res.object;\n    else log('unknown object type');\n    let data: TrackerResult[] = [];\n    if (dom.tracker.checked) {\n      const items = tracking.map((obj) => ({\n        x: obj.box[0] + obj.box[2] / 2,\n        y: obj.box[1] + obj.box[3] / 2,\n        w: obj.box[2],\n        h: obj.box[3],\n        name: obj.label || (human.config.face.enabled ? 'face' : 'body'),\n        confidence: obj.score,\n      }));\n      tracker.updateTrackedItemsWithNewFrame(items, fps.frames);\n      data = tracker.getJSONOfTrackedItems(true) as TrackerResult[];\n    }\n    human.draw.canvas(dom.video, dom.canvas); // copy input video frame to output canvas\n    for (let i = 0; i < tracking.length; i++) {\n      // @ts-ignore\n      const name = tracking[i].label || (human.config.face.enabled ? 'face' : 'body');\n      dom.ctx.strokeRect(tracking[i].box[0], tracking[i].box[1], tracking[i].box[1], tracking[i].box[2]);\n      dom.ctx.fillText(`id: ${tracking[i].id} ${Math.round(100 * tracking[i].score)}% ${name}`, tracking[i].box[0] + 4, tracking[i].box[1] + 16);\n      if (data[i]) {\n        dom.ctx.fillText(`t: ${data[i].id} ${Math.round(100 * data[i].confidence)}% ${data[i].name} ${data[i].isZombie ? 'zombie' : ''}`, tracking[i].box[0] + 4, tracking[i].box[1] + 34);\n      }\n    }\n  }\n  const now = human.now();\n  fps.drawFPS = Math.round(1000 * 1000 / (now - timestamp.draw)) / 1000;\n  timestamp.draw = now;\n  status(dom.video.paused ? 'paused' : `fps: ${fps.detectFPS.toFixed(1).padStart(5, ' ')} detect | ${fps.drawFPS.toFixed(1).padStart(5, ' ')} draw`); // write status\n  setTimeout(drawLoop, 30); // use to slow down refresh from max refresh rate to target of 30 fps\n}\n\nasync function handleVideo(file: File) {\n  const url = URL.createObjectURL(file);\n  dom.video.src = url;\n  await dom.video.play();\n  log('loaded video:', file.name, 'resolution:', [dom.video.videoWidth, dom.video.videoHeight], 'duration:', dom.video.duration);\n  dom.canvas.width = dom.video.videoWidth;\n  dom.canvas.height = dom.video.videoHeight;\n  dom.ctx.strokeStyle = 'white';\n  dom.ctx.fillStyle = 'white';\n  dom.ctx.font = '16px Segoe UI';\n  dom.video.playbackRate = 0.25;\n}\n\nfunction initInput() {\n  document.body.addEventListener('dragenter', (evt) => evt.preventDefault());\n  document.body.addEventListener('dragleave', (evt) => evt.preventDefault());\n  document.body.addEventListener('dragover', (evt) => evt.preventDefault());\n  document.body.addEventListener('drop', async (evt) => {\n    evt.preventDefault();\n    if (evt.dataTransfer) evt.dataTransfer.dropEffect = 'copy';\n    const file = evt.dataTransfer?.files?.[0];\n    if (file) await handleVideo(file);\n    log(dom.video.readyState);\n  });\n  (document.getElementById('inputvideo') as HTMLInputElement).onchange = async (evt) => {\n    evt.preventDefault();\n    const file = evt.target?.['files']?.[0];\n    if (file) await handleVideo(file);\n  };\n  dom.config.onchange = () => {\n    trackerConfig.distanceLimit = (document.getElementById('distanceLimit') as HTMLInputElement).valueAsNumber;\n    trackerConfig.iouLimit = (document.getElementById('iouLimit') as HTMLInputElement).valueAsNumber;\n    trackerConfig.unMatchedFramesTolerance = (document.getElementById('unMatchedFramesTolerance') as HTMLInputElement).valueAsNumber;\n    trackerConfig.unMatchedFramesTolerance = (document.getElementById('unMatchedFramesTolerance') as HTMLInputElement).valueAsNumber;\n    trackerConfig.matchingAlgorithm = (document.getElementById('matchingAlgorithm-kdTree') as HTMLInputElement).checked ? 'kdTree' : 'munkres';\n    tracker.setParams(trackerConfig);\n    if ((document.getElementById('keepInMemory') as HTMLInputElement).checked) tracker.enableKeepInMemory();\n    else tracker.disableKeepInMemory();\n    tracker.reset();\n    log('tracker config change', JSON.stringify(trackerConfig));\n    humanConfig.face!.enabled = (document.getElementById('box-face') as HTMLInputElement).checked; // eslint-disable-line @typescript-eslint/no-non-null-assertion\n    humanConfig.body!.enabled = (document.getElementById('box-body') as HTMLInputElement).checked; // eslint-disable-line @typescript-eslint/no-non-null-assertion\n    humanConfig.object!.enabled = (document.getElementById('box-object') as HTMLInputElement).checked; // eslint-disable-line @typescript-eslint/no-non-null-assertion\n  };\n  dom.tracker.onchange = (evt) => {\n    log('tracker', (evt.target as HTMLInputElement).checked ? 'enabled' : 'disabled');\n    tracker.setParams(trackerConfig);\n    tracker.reset();\n  };\n}\n\nasync function main() { // main entry point\n  log('human version:', human.version, '| tfjs version:', human.tf.version['tfjs-core']);\n  log('platform:', human.env.platform, '| agent:', human.env.agent);\n  status('loading...');\n  await human.load(); // preload all models\n  log('backend:', human.tf.getBackend(), '| available:', human.env.backends);\n  log('models loaded:', human.models.loaded());\n  status('initializing...');\n  await human.warmup(); // warmup function to initialize backend for future faster detection\n  initInput(); // initialize input\n  await detectionLoop(); // start detection loop\n  drawLoop(); // start draw loop\n}\n\nwindow.onload = main;\n", "/* eslint-disable */\nvar __defProp = Object.defineProperty;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\n\n// node_modules/.pnpm/uuid@3.2.1/node_modules/uuid/lib/rng-browser.js\nvar require_rng_browser = __commonJS({\n  \"node_modules/.pnpm/uuid@3.2.1/node_modules/uuid/lib/rng-browser.js\"(exports, module) {\n    var getRandomValues = typeof crypto != \"undefined\" && crypto.getRandomValues.bind(crypto) || typeof msCrypto != \"undefined\" && msCrypto.getRandomValues.bind(msCrypto);\n    if (getRandomValues) {\n      rnds8 = new Uint8Array(16);\n      module.exports = /* @__PURE__ */ __name(function whatwgRNG() {\n        getRandomValues(rnds8);\n        return rnds8;\n      }, \"whatwgRNG\");\n    } else {\n      rnds = new Array(16);\n      module.exports = /* @__PURE__ */ __name(function mathRNG() {\n        for (var i = 0, r; i < 16; i++) {\n          if ((i & 3) === 0)\n            r = Math.random() * 4294967296;\n          rnds[i] = r >>> ((i & 3) << 3) & 255;\n        }\n        return rnds;\n      }, \"mathRNG\");\n    }\n    var rnds8;\n    var rnds;\n  }\n});\n\n// node_modules/.pnpm/uuid@3.2.1/node_modules/uuid/lib/bytesToUuid.js\nvar require_bytesToUuid = __commonJS({\n  \"node_modules/.pnpm/uuid@3.2.1/node_modules/uuid/lib/bytesToUuid.js\"(exports, module) {\n    var byteToHex = [];\n    for (i = 0; i < 256; ++i) {\n      byteToHex[i] = (i + 256).toString(16).substr(1);\n    }\n    function bytesToUuid(buf, offset) {\n      var i2 = offset || 0;\n      var bth = byteToHex;\n      return bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + \"-\" + bth[buf[i2++]] + bth[buf[i2++]] + \"-\" + bth[buf[i2++]] + bth[buf[i2++]] + \"-\" + bth[buf[i2++]] + bth[buf[i2++]] + \"-\" + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]] + bth[buf[i2++]];\n    }\n    __name(bytesToUuid, \"bytesToUuid\");\n    module.exports = bytesToUuid;\n    var i;\n  }\n});\n\n// node_modules/.pnpm/uuid@3.2.1/node_modules/uuid/v4.js\nvar require_v4 = __commonJS({\n  \"node_modules/.pnpm/uuid@3.2.1/node_modules/uuid/v4.js\"(exports, module) {\n    var rng = require_rng_browser();\n    var bytesToUuid = require_bytesToUuid();\n    function v4(options, buf, offset) {\n      var i = buf && offset || 0;\n      if (typeof options == \"string\") {\n        buf = options === \"binary\" ? new Array(16) : null;\n        options = null;\n      }\n      options = options || {};\n      var rnds = options.random || (options.rng || rng)();\n      rnds[6] = rnds[6] & 15 | 64;\n      rnds[8] = rnds[8] & 63 | 128;\n      if (buf) {\n        for (var ii = 0; ii < 16; ++ii) {\n          buf[i + ii] = rnds[ii];\n        }\n      }\n      return buf || bytesToUuid(rnds);\n    }\n    __name(v4, \"v4\");\n    module.exports = v4;\n  }\n});\n\n// utils.js\nvar require_utils = __commonJS({\n  \"utils.js\"(exports) {\n    exports.isDetectionTooLarge = (detections, largestAllowed) => {\n      if (detections.w >= largestAllowed) {\n        return true;\n      } else {\n        return false;\n      }\n    };\n    var isInsideArea = /* @__PURE__ */ __name((area, point) => {\n      const xMin = area.x - area.w / 2;\n      const xMax = area.x + area.w / 2;\n      const yMin = area.y - area.h / 2;\n      const yMax = area.y + area.h / 2;\n      if (point.x >= xMin && point.x <= xMax && point.y >= yMin && point.y <= yMax) {\n        return true;\n      } else {\n        return false;\n      }\n    }, \"isInsideArea\");\n    exports.isInsideArea = isInsideArea;\n    exports.isInsideSomeAreas = (areas, point) => {\n      const isInside = areas.some((area) => isInsideArea(area, point));\n      return isInside;\n    };\n    exports.ignoreObjectsNotToDetect = (detections, objectsToDetect) => {\n      return detections.filter((detection) => objectsToDetect.indexOf(detection.name) > -1);\n    };\n    var getRectangleEdges = /* @__PURE__ */ __name((item) => {\n      return {\n        x0: item.x - item.w / 2,\n        y0: item.y - item.h / 2,\n        x1: item.x + item.w / 2,\n        y1: item.y + item.h / 2\n      };\n    }, \"getRectangleEdges\");\n    exports.getRectangleEdges = getRectangleEdges;\n    exports.iouAreas = (item1, item2) => {\n      var rect1 = getRectangleEdges(item1);\n      var rect2 = getRectangleEdges(item2);\n      var overlap_x0 = Math.max(rect1.x0, rect2.x0);\n      var overlap_y0 = Math.max(rect1.y0, rect2.y0);\n      var overlap_x1 = Math.min(rect1.x1, rect2.x1);\n      var overlap_y1 = Math.min(rect1.y1, rect2.y1);\n      if (overlap_x1 - overlap_x0 <= 0 || overlap_y1 - overlap_y0 <= 0) {\n        return 0;\n      } else {\n        const area_rect1 = item1.w * item1.h;\n        const area_rect2 = item2.w * item2.h;\n        const area_intersection = (overlap_x1 - overlap_x0) * (overlap_y1 - overlap_y0);\n        const area_union = area_rect1 + area_rect2 - area_intersection;\n        return area_intersection / area_union;\n      }\n    };\n    exports.computeVelocityVector = (item1, item2, nbFrame) => {\n      return {\n        dx: (item2.x - item1.x) / nbFrame,\n        dy: (item2.y - item1.y) / nbFrame\n      };\n    };\n    exports.computeBearingIn360 = function(dx, dy) {\n      var angle = Math.atan(dx / dy) / (Math.PI / 180);\n      if (angle > 0) {\n        if (dy > 0)\n          return angle;\n        else\n          return 180 + angle;\n      } else {\n        if (dx > 0)\n          return 180 + angle;\n        else\n          return 360 + angle;\n      }\n    };\n  }\n});\n\n// ItemTracked.js\nvar require_ItemTracked = __commonJS({\n  \"ItemTracked.js\"(exports) {\n    var uuidv4 = require_v4();\n    var computeBearingIn360 = require_utils().computeBearingIn360;\n    var computeVelocityVector = require_utils().computeVelocityVector;\n    exports.ITEM_HISTORY_MAX_LENGTH = 15;\n    var idDisplay = 0;\n    exports.ItemTracked = function(properties, frameNb, unMatchedFramesTolerance, fastDelete) {\n      var DEFAULT_UNMATCHEDFRAMES_TOLERANCE = unMatchedFramesTolerance;\n      var itemTracked = {};\n      itemTracked.available = true;\n      itemTracked.delete = false;\n      itemTracked.fastDelete = fastDelete;\n      itemTracked.frameUnmatchedLeftBeforeDying = unMatchedFramesTolerance;\n      itemTracked.isZombie = false;\n      itemTracked.appearFrame = frameNb;\n      itemTracked.disappearFrame = null;\n      itemTracked.disappearArea = {};\n      itemTracked.nameCount = {};\n      itemTracked.nameCount[properties.name] = 1;\n      itemTracked.x = properties.x;\n      itemTracked.y = properties.y;\n      itemTracked.w = properties.w;\n      itemTracked.h = properties.h;\n      itemTracked.name = properties.name;\n      itemTracked.confidence = properties.confidence;\n      itemTracked.itemHistory = [];\n      itemTracked.itemHistory.push({\n        x: properties.x,\n        y: properties.y,\n        w: properties.w,\n        h: properties.h,\n        confidence: properties.confidence\n      });\n      if (itemTracked.itemHistory.length >= exports.ITEM_HISTORY_MAX_LENGTH) {\n        itemTracked.itemHistory.shift();\n      }\n      itemTracked.velocity = {\n        dx: 0,\n        dy: 0\n      };\n      itemTracked.nbTimeMatched = 1;\n      itemTracked.id = uuidv4();\n      itemTracked.idDisplay = idDisplay;\n      idDisplay++;\n      itemTracked.update = function(properties2, frameNb2) {\n        if (this.disappearFrame) {\n          this.disappearFrame = null;\n          this.disappearArea = {};\n        }\n        this.isZombie = false;\n        this.nbTimeMatched += 1;\n        this.x = properties2.x;\n        this.y = properties2.y;\n        this.w = properties2.w;\n        this.h = properties2.h;\n        this.confidence = properties2.confidence;\n        this.itemHistory.push({\n          x: this.x,\n          y: this.y,\n          w: this.w,\n          h: this.h,\n          confidence: this.confidence\n        });\n        if (itemTracked.itemHistory.length >= exports.ITEM_HISTORY_MAX_LENGTH) {\n          itemTracked.itemHistory.shift();\n        }\n        this.name = properties2.name;\n        if (this.nameCount[properties2.name]) {\n          this.nameCount[properties2.name]++;\n        } else {\n          this.nameCount[properties2.name] = 1;\n        }\n        this.frameUnmatchedLeftBeforeDying = DEFAULT_UNMATCHEDFRAMES_TOLERANCE;\n        this.velocity = this.updateVelocityVector();\n      };\n      itemTracked.makeAvailable = function() {\n        this.available = true;\n        return this;\n      };\n      itemTracked.makeUnavailable = function() {\n        this.available = false;\n        return this;\n      };\n      itemTracked.countDown = function(frameNb2) {\n        if (this.disappearFrame === null) {\n          this.disappearFrame = frameNb2;\n          this.disappearArea = {\n            x: this.x,\n            y: this.y,\n            w: this.w,\n            h: this.h\n          };\n        }\n        this.frameUnmatchedLeftBeforeDying--;\n        this.isZombie = true;\n        if (this.fastDelete && this.nbTimeMatched <= 1) {\n          this.frameUnmatchedLeftBeforeDying = -1;\n        }\n      };\n      itemTracked.updateTheoricalPositionAndSize = function() {\n        this.itemHistory.push({\n          x: this.x,\n          y: this.y,\n          w: this.w,\n          h: this.h,\n          confidence: this.confidence\n        });\n        if (itemTracked.itemHistory.length >= exports.ITEM_HISTORY_MAX_LENGTH) {\n          itemTracked.itemHistory.shift();\n        }\n        this.x = this.x + this.velocity.dx;\n        this.y = this.y + this.velocity.dy;\n      };\n      itemTracked.predictNextPosition = function() {\n        return {\n          x: this.x + this.velocity.dx,\n          y: this.y + this.velocity.dy,\n          w: this.w,\n          h: this.h\n        };\n      };\n      itemTracked.isDead = function() {\n        return this.frameUnmatchedLeftBeforeDying < 0;\n      };\n      itemTracked.updateVelocityVector = function() {\n        if (exports.ITEM_HISTORY_MAX_LENGTH <= 2) {\n          return { dx: void 0, dy: void 0 };\n        }\n        if (this.itemHistory.length <= exports.ITEM_HISTORY_MAX_LENGTH) {\n          const start = this.itemHistory[0];\n          const end = this.itemHistory[this.itemHistory.length - 1];\n          return computeVelocityVector(start, end, this.itemHistory.length);\n        } else {\n          const start = this.itemHistory[this.itemHistory.length - exports.ITEM_HISTORY_MAX_LENGTH];\n          const end = this.itemHistory[this.itemHistory.length - 1];\n          return computeVelocityVector(start, end, exports.ITEM_HISTORY_MAX_LENGTH);\n        }\n      };\n      itemTracked.getMostlyMatchedName = function() {\n        var nameMostlyMatchedOccurences = 0;\n        var nameMostlyMatched = \"\";\n        Object.keys(this.nameCount).map((name) => {\n          if (this.nameCount[name] > nameMostlyMatchedOccurences) {\n            nameMostlyMatched = name;\n            nameMostlyMatchedOccurences = this.nameCount[name];\n          }\n        });\n        return nameMostlyMatched;\n      };\n      itemTracked.toJSONDebug = function(roundInt = true) {\n        return {\n          id: this.id,\n          idDisplay: this.idDisplay,\n          x: roundInt ? parseInt(this.x, 10) : this.x,\n          y: roundInt ? parseInt(this.y, 10) : this.y,\n          w: roundInt ? parseInt(this.w, 10) : this.w,\n          h: roundInt ? parseInt(this.h, 10) : this.h,\n          confidence: Math.round(this.confidence * 100) / 100,\n          // Here we negate dy to be in \"normal\" carthesian coordinates\n          bearing: parseInt(computeBearingIn360(this.velocity.dx, -this.velocity.dy)),\n          name: this.getMostlyMatchedName(),\n          isZombie: this.isZombie,\n          appearFrame: this.appearFrame,\n          disappearFrame: this.disappearFrame\n        };\n      };\n      itemTracked.toJSON = function(roundInt = true) {\n        return {\n          id: this.idDisplay,\n          x: roundInt ? parseInt(this.x, 10) : this.x,\n          y: roundInt ? parseInt(this.y, 10) : this.y,\n          w: roundInt ? parseInt(this.w, 10) : this.w,\n          h: roundInt ? parseInt(this.h, 10) : this.h,\n          confidence: Math.round(this.confidence * 100) / 100,\n          // Here we negate dy to be in \"normal\" carthesian coordinates\n          bearing: parseInt(computeBearingIn360(this.velocity.dx, -this.velocity.dy), 10),\n          name: this.getMostlyMatchedName(),\n          isZombie: this.isZombie\n        };\n      };\n      itemTracked.toMOT = function(frameIndex) {\n        return `${frameIndex},${this.idDisplay},${this.x - this.w / 2},${this.y - this.h / 2},${this.w},${this.h},${this.confidence / 100},-1,-1,-1`;\n      };\n      itemTracked.toJSONGenericInfo = function() {\n        return {\n          id: this.id,\n          idDisplay: this.idDisplay,\n          appearFrame: this.appearFrame,\n          disappearFrame: this.disappearFrame,\n          disappearArea: this.disappearArea,\n          nbActiveFrame: this.disappearFrame - this.appearFrame,\n          name: this.getMostlyMatchedName()\n        };\n      };\n      return itemTracked;\n    };\n    exports.reset = function() {\n      idDisplay = 0;\n    };\n  }\n});\n\n// lib/kdTree-min.js\nvar require_kdTree_min = __commonJS({\n  \"lib/kdTree-min.js\"(exports) {\n    (function(root, factory) {\n      if (typeof define === \"function\" && define.amd) {\n        define([\"exports\"], factory);\n      } else if (typeof exports === \"object\") {\n        factory(exports);\n      } else {\n        factory(root);\n      }\n    })(exports, function(exports2) {\n      function Node(obj, dimension, parent) {\n        this.obj = obj;\n        this.left = null;\n        this.right = null;\n        this.parent = parent;\n        this.dimension = dimension;\n      }\n      __name(Node, \"Node\");\n      function kdTree(points, metric, dimensions) {\n        var self = this;\n        function buildTree(points2, depth, parent) {\n          var dim = depth % dimensions.length, median, node;\n          if (points2.length === 0) {\n            return null;\n          }\n          if (points2.length === 1) {\n            return new Node(points2[0], dim, parent);\n          }\n          points2.sort(function(a, b) {\n            return a[dimensions[dim]] - b[dimensions[dim]];\n          });\n          median = Math.floor(points2.length / 2);\n          node = new Node(points2[median], dim, parent);\n          node.left = buildTree(points2.slice(0, median), depth + 1, node);\n          node.right = buildTree(points2.slice(median + 1), depth + 1, node);\n          return node;\n        }\n        __name(buildTree, \"buildTree\");\n        function loadTree(data) {\n          self.root = data;\n          function restoreParent(root) {\n            if (root.left) {\n              root.left.parent = root;\n              restoreParent(root.left);\n            }\n            if (root.right) {\n              root.right.parent = root;\n              restoreParent(root.right);\n            }\n          }\n          __name(restoreParent, \"restoreParent\");\n          restoreParent(self.root);\n        }\n        __name(loadTree, \"loadTree\");\n        if (!Array.isArray(points))\n          loadTree(points, metric, dimensions);\n        else\n          this.root = buildTree(points, 0, null);\n        this.toJSON = function(src) {\n          if (!src)\n            src = this.root;\n          var dest = new Node(src.obj, src.dimension, null);\n          if (src.left)\n            dest.left = self.toJSON(src.left);\n          if (src.right)\n            dest.right = self.toJSON(src.right);\n          return dest;\n        };\n        this.insert = function(point) {\n          function innerSearch(node, parent) {\n            if (node === null) {\n              return parent;\n            }\n            var dimension2 = dimensions[node.dimension];\n            if (point[dimension2] < node.obj[dimension2]) {\n              return innerSearch(node.left, node);\n            } else {\n              return innerSearch(node.right, node);\n            }\n          }\n          __name(innerSearch, \"innerSearch\");\n          var insertPosition = innerSearch(this.root, null), newNode, dimension;\n          if (insertPosition === null) {\n            this.root = new Node(point, 0, null);\n            return;\n          }\n          newNode = new Node(point, (insertPosition.dimension + 1) % dimensions.length, insertPosition);\n          dimension = dimensions[insertPosition.dimension];\n          if (point[dimension] < insertPosition.obj[dimension]) {\n            insertPosition.left = newNode;\n          } else {\n            insertPosition.right = newNode;\n          }\n        };\n        this.remove = function(point) {\n          var node;\n          function nodeSearch(node2) {\n            if (node2 === null) {\n              return null;\n            }\n            if (node2.obj === point) {\n              return node2;\n            }\n            var dimension = dimensions[node2.dimension];\n            if (point[dimension] < node2.obj[dimension]) {\n              return nodeSearch(node2.left, node2);\n            } else {\n              return nodeSearch(node2.right, node2);\n            }\n          }\n          __name(nodeSearch, \"nodeSearch\");\n          function removeNode(node2) {\n            var nextNode, nextObj, pDimension;\n            function findMin(node3, dim) {\n              var dimension, own, left, right, min;\n              if (node3 === null) {\n                return null;\n              }\n              dimension = dimensions[dim];\n              if (node3.dimension === dim) {\n                if (node3.left !== null) {\n                  return findMin(node3.left, dim);\n                }\n                return node3;\n              }\n              own = node3.obj[dimension];\n              left = findMin(node3.left, dim);\n              right = findMin(node3.right, dim);\n              min = node3;\n              if (left !== null && left.obj[dimension] < own) {\n                min = left;\n              }\n              if (right !== null && right.obj[dimension] < min.obj[dimension]) {\n                min = right;\n              }\n              return min;\n            }\n            __name(findMin, \"findMin\");\n            if (node2.left === null && node2.right === null) {\n              if (node2.parent === null) {\n                self.root = null;\n                return;\n              }\n              pDimension = dimensions[node2.parent.dimension];\n              if (node2.obj[pDimension] < node2.parent.obj[pDimension]) {\n                node2.parent.left = null;\n              } else {\n                node2.parent.right = null;\n              }\n              return;\n            }\n            if (node2.right !== null) {\n              nextNode = findMin(node2.right, node2.dimension);\n              nextObj = nextNode.obj;\n              removeNode(nextNode);\n              node2.obj = nextObj;\n            } else {\n              nextNode = findMin(node2.left, node2.dimension);\n              nextObj = nextNode.obj;\n              removeNode(nextNode);\n              node2.right = node2.left;\n              node2.left = null;\n              node2.obj = nextObj;\n            }\n          }\n          __name(removeNode, \"removeNode\");\n          node = nodeSearch(self.root);\n          if (node === null) {\n            return;\n          }\n          removeNode(node);\n        };\n        this.nearest = function(point, maxNodes, maxDistance) {\n          var i, result, bestNodes;\n          bestNodes = new BinaryHeap(\n            function(e) {\n              return -e[1];\n            }\n          );\n          function nearestSearch(node) {\n            var bestChild, dimension = dimensions[node.dimension], ownDistance = metric(point, node.obj), linearPoint = {}, linearDistance, otherChild, i2;\n            function saveNode(node2, distance) {\n              bestNodes.push([node2, distance]);\n              if (bestNodes.size() > maxNodes) {\n                bestNodes.pop();\n              }\n            }\n            __name(saveNode, \"saveNode\");\n            for (i2 = 0; i2 < dimensions.length; i2 += 1) {\n              if (i2 === node.dimension) {\n                linearPoint[dimensions[i2]] = point[dimensions[i2]];\n              } else {\n                linearPoint[dimensions[i2]] = node.obj[dimensions[i2]];\n              }\n            }\n            linearDistance = metric(linearPoint, node.obj);\n            if (node.right === null && node.left === null) {\n              if (bestNodes.size() < maxNodes || ownDistance < bestNodes.peek()[1]) {\n                saveNode(node, ownDistance);\n              }\n              return;\n            }\n            if (node.right === null) {\n              bestChild = node.left;\n            } else if (node.left === null) {\n              bestChild = node.right;\n            } else {\n              if (point[dimension] < node.obj[dimension]) {\n                bestChild = node.left;\n              } else {\n                bestChild = node.right;\n              }\n            }\n            nearestSearch(bestChild);\n            if (bestNodes.size() < maxNodes || ownDistance < bestNodes.peek()[1]) {\n              saveNode(node, ownDistance);\n            }\n            if (bestNodes.size() < maxNodes || Math.abs(linearDistance) < bestNodes.peek()[1]) {\n              if (bestChild === node.left) {\n                otherChild = node.right;\n              } else {\n                otherChild = node.left;\n              }\n              if (otherChild !== null) {\n                nearestSearch(otherChild);\n              }\n            }\n          }\n          __name(nearestSearch, \"nearestSearch\");\n          if (maxDistance) {\n            for (i = 0; i < maxNodes; i += 1) {\n              bestNodes.push([null, maxDistance]);\n            }\n          }\n          if (self.root)\n            nearestSearch(self.root);\n          result = [];\n          for (i = 0; i < Math.min(maxNodes, bestNodes.content.length); i += 1) {\n            if (bestNodes.content[i][0]) {\n              result.push([bestNodes.content[i][0].obj, bestNodes.content[i][1]]);\n            }\n          }\n          return result;\n        };\n        this.balanceFactor = function() {\n          function height(node) {\n            if (node === null) {\n              return 0;\n            }\n            return Math.max(height(node.left), height(node.right)) + 1;\n          }\n          __name(height, \"height\");\n          function count(node) {\n            if (node === null) {\n              return 0;\n            }\n            return count(node.left) + count(node.right) + 1;\n          }\n          __name(count, \"count\");\n          return height(self.root) / (Math.log(count(self.root)) / Math.log(2));\n        };\n      }\n      __name(kdTree, \"kdTree\");\n      function BinaryHeap(scoreFunction) {\n        this.content = [];\n        this.scoreFunction = scoreFunction;\n      }\n      __name(BinaryHeap, \"BinaryHeap\");\n      BinaryHeap.prototype = {\n        push: function(element) {\n          this.content.push(element);\n          this.bubbleUp(this.content.length - 1);\n        },\n        pop: function() {\n          var result = this.content[0];\n          var end = this.content.pop();\n          if (this.content.length > 0) {\n            this.content[0] = end;\n            this.sinkDown(0);\n          }\n          return result;\n        },\n        peek: function() {\n          return this.content[0];\n        },\n        remove: function(node) {\n          var len = this.content.length;\n          for (var i = 0; i < len; i++) {\n            if (this.content[i] == node) {\n              var end = this.content.pop();\n              if (i != len - 1) {\n                this.content[i] = end;\n                if (this.scoreFunction(end) < this.scoreFunction(node))\n                  this.bubbleUp(i);\n                else\n                  this.sinkDown(i);\n              }\n              return;\n            }\n          }\n          throw new Error(\"Node not found.\");\n        },\n        size: function() {\n          return this.content.length;\n        },\n        bubbleUp: function(n) {\n          var element = this.content[n];\n          while (n > 0) {\n            var parentN = Math.floor((n + 1) / 2) - 1, parent = this.content[parentN];\n            if (this.scoreFunction(element) < this.scoreFunction(parent)) {\n              this.content[parentN] = element;\n              this.content[n] = parent;\n              n = parentN;\n            } else {\n              break;\n            }\n          }\n        },\n        sinkDown: function(n) {\n          var length = this.content.length, element = this.content[n], elemScore = this.scoreFunction(element);\n          while (true) {\n            var child2N = (n + 1) * 2, child1N = child2N - 1;\n            var swap = null;\n            if (child1N < length) {\n              var child1 = this.content[child1N], child1Score = this.scoreFunction(child1);\n              if (child1Score < elemScore)\n                swap = child1N;\n            }\n            if (child2N < length) {\n              var child2 = this.content[child2N], child2Score = this.scoreFunction(child2);\n              if (child2Score < (swap == null ? elemScore : child1Score)) {\n                swap = child2N;\n              }\n            }\n            if (swap != null) {\n              this.content[n] = this.content[swap];\n              this.content[swap] = element;\n              n = swap;\n            } else {\n              break;\n            }\n          }\n        }\n      };\n      exports2.kdTree = kdTree;\n      exports2.BinaryHeap = BinaryHeap;\n    });\n  }\n});\n\n// node_modules/.pnpm/munkres-js@1.2.2/node_modules/munkres-js/munkres.js\nvar require_munkres = __commonJS({\n  \"node_modules/.pnpm/munkres-js@1.2.2/node_modules/munkres-js/munkres.js\"(exports, module) {\n    var MAX_SIZE = parseInt(Number.MAX_SAFE_INTEGER / 2) || (1 << 26) * (1 << 26);\n    var DEFAULT_PAD_VALUE = 0;\n    function Munkres() {\n      this.C = null;\n      this.row_covered = [];\n      this.col_covered = [];\n      this.n = 0;\n      this.Z0_r = 0;\n      this.Z0_c = 0;\n      this.marked = null;\n      this.path = null;\n    }\n    __name(Munkres, \"Munkres\");\n    Munkres.prototype.pad_matrix = function(matrix, pad_value) {\n      pad_value = pad_value || DEFAULT_PAD_VALUE;\n      var max_columns = 0;\n      var total_rows = matrix.length;\n      var i;\n      for (i = 0; i < total_rows; ++i)\n        if (matrix[i].length > max_columns)\n          max_columns = matrix[i].length;\n      total_rows = max_columns > total_rows ? max_columns : total_rows;\n      var new_matrix = [];\n      for (i = 0; i < total_rows; ++i) {\n        var row = matrix[i] || [];\n        var new_row = row.slice();\n        while (total_rows > new_row.length)\n          new_row.push(pad_value);\n        new_matrix.push(new_row);\n      }\n      return new_matrix;\n    };\n    Munkres.prototype.compute = function(cost_matrix, options) {\n      options = options || {};\n      options.padValue = options.padValue || DEFAULT_PAD_VALUE;\n      this.C = this.pad_matrix(cost_matrix, options.padValue);\n      this.n = this.C.length;\n      this.original_length = cost_matrix.length;\n      this.original_width = cost_matrix[0].length;\n      var nfalseArray = [];\n      while (nfalseArray.length < this.n)\n        nfalseArray.push(false);\n      this.row_covered = nfalseArray.slice();\n      this.col_covered = nfalseArray.slice();\n      this.Z0_r = 0;\n      this.Z0_c = 0;\n      this.path = this.__make_matrix(this.n * 2, 0);\n      this.marked = this.__make_matrix(this.n, 0);\n      var step = 1;\n      var steps = {\n        1: this.__step1,\n        2: this.__step2,\n        3: this.__step3,\n        4: this.__step4,\n        5: this.__step5,\n        6: this.__step6\n      };\n      while (true) {\n        var func = steps[step];\n        if (!func)\n          break;\n        step = func.apply(this);\n      }\n      var results = [];\n      for (var i = 0; i < this.original_length; ++i)\n        for (var j = 0; j < this.original_width; ++j)\n          if (this.marked[i][j] == 1)\n            results.push([i, j]);\n      return results;\n    };\n    Munkres.prototype.__make_matrix = function(n, val) {\n      var matrix = [];\n      for (var i = 0; i < n; ++i) {\n        matrix[i] = [];\n        for (var j = 0; j < n; ++j)\n          matrix[i][j] = val;\n      }\n      return matrix;\n    };\n    Munkres.prototype.__step1 = function() {\n      for (var i = 0; i < this.n; ++i) {\n        var minval = Math.min.apply(Math, this.C[i]);\n        for (var j = 0; j < this.n; ++j)\n          this.C[i][j] -= minval;\n      }\n      return 2;\n    };\n    Munkres.prototype.__step2 = function() {\n      for (var i = 0; i < this.n; ++i) {\n        for (var j = 0; j < this.n; ++j) {\n          if (this.C[i][j] === 0 && !this.col_covered[j] && !this.row_covered[i]) {\n            this.marked[i][j] = 1;\n            this.col_covered[j] = true;\n            this.row_covered[i] = true;\n            break;\n          }\n        }\n      }\n      this.__clear_covers();\n      return 3;\n    };\n    Munkres.prototype.__step3 = function() {\n      var count = 0;\n      for (var i = 0; i < this.n; ++i) {\n        for (var j = 0; j < this.n; ++j) {\n          if (this.marked[i][j] == 1 && this.col_covered[j] == false) {\n            this.col_covered[j] = true;\n            ++count;\n          }\n        }\n      }\n      return count >= this.n ? 7 : 4;\n    };\n    Munkres.prototype.__step4 = function() {\n      var done = false;\n      var row = -1, col = -1, star_col = -1;\n      while (!done) {\n        var z = this.__find_a_zero();\n        row = z[0];\n        col = z[1];\n        if (row < 0)\n          return 6;\n        this.marked[row][col] = 2;\n        star_col = this.__find_star_in_row(row);\n        if (star_col >= 0) {\n          col = star_col;\n          this.row_covered[row] = true;\n          this.col_covered[col] = false;\n        } else {\n          this.Z0_r = row;\n          this.Z0_c = col;\n          return 5;\n        }\n      }\n    };\n    Munkres.prototype.__step5 = function() {\n      var count = 0;\n      this.path[count][0] = this.Z0_r;\n      this.path[count][1] = this.Z0_c;\n      var done = false;\n      while (!done) {\n        var row = this.__find_star_in_col(this.path[count][1]);\n        if (row >= 0) {\n          count++;\n          this.path[count][0] = row;\n          this.path[count][1] = this.path[count - 1][1];\n        } else {\n          done = true;\n        }\n        if (!done) {\n          var col = this.__find_prime_in_row(this.path[count][0]);\n          count++;\n          this.path[count][0] = this.path[count - 1][0];\n          this.path[count][1] = col;\n        }\n      }\n      this.__convert_path(this.path, count);\n      this.__clear_covers();\n      this.__erase_primes();\n      return 3;\n    };\n    Munkres.prototype.__step6 = function() {\n      var minval = this.__find_smallest();\n      for (var i = 0; i < this.n; ++i) {\n        for (var j = 0; j < this.n; ++j) {\n          if (this.row_covered[i])\n            this.C[i][j] += minval;\n          if (!this.col_covered[j])\n            this.C[i][j] -= minval;\n        }\n      }\n      return 4;\n    };\n    Munkres.prototype.__find_smallest = function() {\n      var minval = MAX_SIZE;\n      for (var i = 0; i < this.n; ++i)\n        for (var j = 0; j < this.n; ++j)\n          if (!this.row_covered[i] && !this.col_covered[j]) {\n            if (minval > this.C[i][j])\n              minval = this.C[i][j];\n          }\n      return minval;\n    };\n    Munkres.prototype.__find_a_zero = function() {\n      for (var i = 0; i < this.n; ++i)\n        for (var j = 0; j < this.n; ++j)\n          if (this.C[i][j] === 0 && !this.row_covered[i] && !this.col_covered[j])\n            return [i, j];\n      return [-1, -1];\n    };\n    Munkres.prototype.__find_star_in_row = function(row) {\n      for (var j = 0; j < this.n; ++j)\n        if (this.marked[row][j] == 1)\n          return j;\n      return -1;\n    };\n    Munkres.prototype.__find_star_in_col = function(col) {\n      for (var i = 0; i < this.n; ++i)\n        if (this.marked[i][col] == 1)\n          return i;\n      return -1;\n    };\n    Munkres.prototype.__find_prime_in_row = function(row) {\n      for (var j = 0; j < this.n; ++j)\n        if (this.marked[row][j] == 2)\n          return j;\n      return -1;\n    };\n    Munkres.prototype.__convert_path = function(path, count) {\n      for (var i = 0; i <= count; ++i)\n        this.marked[path[i][0]][path[i][1]] = this.marked[path[i][0]][path[i][1]] == 1 ? 0 : 1;\n    };\n    Munkres.prototype.__clear_covers = function() {\n      for (var i = 0; i < this.n; ++i) {\n        this.row_covered[i] = false;\n        this.col_covered[i] = false;\n      }\n    };\n    Munkres.prototype.__erase_primes = function() {\n      for (var i = 0; i < this.n; ++i)\n        for (var j = 0; j < this.n; ++j)\n          if (this.marked[i][j] == 2)\n            this.marked[i][j] = 0;\n    };\n    function make_cost_matrix(profit_matrix, inversion_function) {\n      var i, j;\n      if (!inversion_function) {\n        var maximum = -1 / 0;\n        for (i = 0; i < profit_matrix.length; ++i)\n          for (j = 0; j < profit_matrix[i].length; ++j)\n            if (profit_matrix[i][j] > maximum)\n              maximum = profit_matrix[i][j];\n        inversion_function = /* @__PURE__ */ __name(function(x) {\n          return maximum - x;\n        }, \"inversion_function\");\n      }\n      var cost_matrix = [];\n      for (i = 0; i < profit_matrix.length; ++i) {\n        var row = profit_matrix[i];\n        cost_matrix[i] = [];\n        for (j = 0; j < row.length; ++j)\n          cost_matrix[i][j] = inversion_function(profit_matrix[i][j]);\n      }\n      return cost_matrix;\n    }\n    __name(make_cost_matrix, \"make_cost_matrix\");\n    function format_matrix(matrix) {\n      var columnWidths = [];\n      var i, j;\n      for (i = 0; i < matrix.length; ++i) {\n        for (j = 0; j < matrix[i].length; ++j) {\n          var entryWidth = String(matrix[i][j]).length;\n          if (!columnWidths[j] || entryWidth >= columnWidths[j])\n            columnWidths[j] = entryWidth;\n        }\n      }\n      var formatted = \"\";\n      for (i = 0; i < matrix.length; ++i) {\n        for (j = 0; j < matrix[i].length; ++j) {\n          var s = String(matrix[i][j]);\n          while (s.length < columnWidths[j])\n            s = \" \" + s;\n          formatted += s;\n          if (j != matrix[i].length - 1)\n            formatted += \" \";\n        }\n        if (i != matrix[i].length - 1)\n          formatted += \"\\n\";\n      }\n      return formatted;\n    }\n    __name(format_matrix, \"format_matrix\");\n    function computeMunkres(cost_matrix, options) {\n      var m = new Munkres();\n      return m.compute(cost_matrix, options);\n    }\n    __name(computeMunkres, \"computeMunkres\");\n    computeMunkres.version = \"1.2.2\";\n    computeMunkres.format_matrix = format_matrix;\n    computeMunkres.make_cost_matrix = make_cost_matrix;\n    computeMunkres.Munkres = Munkres;\n    if (typeof module !== \"undefined\" && module.exports) {\n      module.exports = computeMunkres;\n    }\n  }\n});\n\n// tracker.js\nvar require_tracker = __commonJS({\n  \"tracker.js\"(exports) {\n    var itemTrackedModule = require_ItemTracked();\n    var ItemTracked = itemTrackedModule.ItemTracked;\n    var kdTree = require_kdTree_min().kdTree;\n    var iouAreas = require_utils().iouAreas;\n    var munkres = require_munkres();\n    var DEBUG_MODE = false;\n    var iouDistance = /* @__PURE__ */ __name(function(item1, item2) {\n      var iou = iouAreas(item1, item2);\n      var distance = 1 - iou;\n      if (distance > 1 - params.iouLimit) {\n        distance = params.distanceLimit + 1;\n      }\n      return distance;\n    }, \"iouDistance\");\n    var params = {\n      // DEFAULT_UNMATCHEDFRAMES_TOLERANCE\n      // This the number of frame we wait when an object isn't matched before considering it gone\n      unMatchedFramesTolerance: 5,\n      // DEFAULT_IOU_LIMIT, exclude things from beeing matched if their IOU is lower than this\n      // 1 means total overlap whereas 0 means no overlap\n      iouLimit: 0.05,\n      // Remove new objects fast if they could not be matched in the next frames.\n      // Setting this to false ensures the object will stick around at least\n      // unMatchedFramesTolerance frames, even if they could neven be matched in\n      // subsequent frames.\n      fastDelete: true,\n      // The function to use to determine the distance between to detected objects\n      distanceFunc: iouDistance,\n      // The distance limit for matching. If values need to be excluded from\n      // matching set their distance to something greater than the distance limit\n      distanceLimit: 1e4,\n      // The algorithm used to match tracks with new detections. Can be either\n      // 'kdTree' or 'munkres'.\n      matchingAlgorithm: \"kdTree\"\n    };\n    var mapOfItemsTracked = /* @__PURE__ */ new Map();\n    var mapOfAllItemsTracked = /* @__PURE__ */ new Map();\n    var keepAllHistoryInMemory = false;\n    exports.computeDistance = iouDistance;\n    exports.updateTrackedItemsWithNewFrame = function(detectionsOfThisFrame, frameNb) {\n      var treeItemsTracked = new kdTree(Array.from(mapOfItemsTracked.values()), params.distanceFunc, [\"x\", \"y\", \"w\", \"h\"]);\n      var treeDetectionsOfThisFrame = new kdTree(detectionsOfThisFrame, params.distanceFunc, [\"x\", \"y\", \"w\", \"h\"]);\n      if (mapOfItemsTracked.size === 0) {\n        detectionsOfThisFrame.forEach(function(itemDetected) {\n          var newItemTracked = new ItemTracked(itemDetected, frameNb, params.unMatchedFramesTolerance, params.fastDelete);\n          mapOfItemsTracked.set(newItemTracked.id, newItemTracked);\n          treeItemsTracked.insert(newItemTracked);\n        });\n      } else {\n        var matchedList = new Array(detectionsOfThisFrame.length);\n        matchedList.fill(false);\n        if (detectionsOfThisFrame.length > 0) {\n          if (params.matchingAlgorithm === \"munkres\") {\n            var trackedItemIds = Array.from(mapOfItemsTracked.keys());\n            var costMatrix = Array.from(mapOfItemsTracked.values()).map((itemTracked) => {\n              var predictedPosition = itemTracked.predictNextPosition();\n              return detectionsOfThisFrame.map(\n                (detection) => params.distanceFunc(predictedPosition, detection)\n              );\n            });\n            mapOfItemsTracked.forEach(function(itemTracked) {\n              itemTracked.makeAvailable();\n            });\n            munkres(costMatrix).filter((m) => costMatrix[m[0]][m[1]] <= params.distanceLimit).forEach((m) => {\n              var itemTracked = mapOfItemsTracked.get(trackedItemIds[m[0]]);\n              var updatedTrackedItemProperties = detectionsOfThisFrame[m[1]];\n              matchedList[m[1]] = { idDisplay: itemTracked.idDisplay };\n              itemTracked.makeUnavailable().update(updatedTrackedItemProperties, frameNb);\n            });\n            matchedList.forEach(function(matched, index) {\n              if (!matched) {\n                if (Math.min(...costMatrix.map((m) => m[index])) > params.distanceLimit) {\n                  var newItemTracked = ItemTracked(detectionsOfThisFrame[index], frameNb, params.unMatchedFramesTolerance, params.fastDelete);\n                  mapOfItemsTracked.set(newItemTracked.id, newItemTracked);\n                  newItemTracked.makeUnavailable();\n                  costMatrix.push(detectionsOfThisFrame.map(\n                    (detection) => params.distanceFunc(newItemTracked, detection)\n                  ));\n                }\n              }\n            });\n          } else if (params.matchingAlgorithm === \"kdTree\") {\n            mapOfItemsTracked.forEach(function(itemTracked) {\n              var predictedPosition = itemTracked.predictNextPosition();\n              itemTracked.makeAvailable();\n              var treeSearchResult = treeDetectionsOfThisFrame.nearest(predictedPosition, 1, params.distanceLimit)[0];\n              var treeSearchResultWithoutPrediction = treeDetectionsOfThisFrame.nearest(itemTracked, 1, params.distanceLimit)[0];\n              var treeSearchMultipleResults = treeDetectionsOfThisFrame.nearest(predictedPosition, 2, params.distanceLimit);\n              if (treeSearchResult) {\n                var indexClosestNewDetectedItem = detectionsOfThisFrame.indexOf(treeSearchResult[0]);\n                if (!matchedList[indexClosestNewDetectedItem]) {\n                  matchedList[indexClosestNewDetectedItem] = {\n                    idDisplay: itemTracked.idDisplay\n                  };\n                  var updatedTrackedItemProperties = detectionsOfThisFrame[indexClosestNewDetectedItem];\n                  mapOfItemsTracked.get(itemTracked.id).makeUnavailable().update(updatedTrackedItemProperties, frameNb);\n                } else {\n                }\n              }\n            });\n          } else {\n            throw `Unknown matching algorithm \"${params.matchingAlgorithm}\"`;\n          }\n        } else {\n          if (DEBUG_MODE) {\n            console.log(\"[Tracker] Nothing detected for frame n\\xBA\" + frameNb);\n          }\n          mapOfItemsTracked.forEach(function(itemTracked) {\n            itemTracked.makeAvailable();\n          });\n        }\n        if (params.matchingAlgorithm === \"kdTree\") {\n          if (mapOfItemsTracked.size > 0) {\n            treeItemsTracked = new kdTree(Array.from(mapOfItemsTracked.values()), params.distanceFunc, [\"x\", \"y\", \"w\", \"h\"]);\n            matchedList.forEach(function(matched, index) {\n              if (!matched) {\n                var treeSearchResult = treeItemsTracked.nearest(detectionsOfThisFrame[index], 1, params.distanceLimit)[0];\n                if (!treeSearchResult) {\n                  var newItemTracked = ItemTracked(detectionsOfThisFrame[index], frameNb, params.unMatchedFramesTolerance, params.fastDelete);\n                  mapOfItemsTracked.set(newItemTracked.id, newItemTracked);\n                  treeItemsTracked.insert(newItemTracked);\n                  newItemTracked.makeUnavailable();\n                } else {\n                }\n              }\n            });\n          }\n        }\n        mapOfItemsTracked.forEach(function(itemTracked) {\n          if (itemTracked.available) {\n            itemTracked.countDown(frameNb);\n            itemTracked.updateTheoricalPositionAndSize();\n            if (itemTracked.isDead()) {\n              mapOfItemsTracked.delete(itemTracked.id);\n              treeItemsTracked.remove(itemTracked);\n              if (keepAllHistoryInMemory) {\n                mapOfAllItemsTracked.set(itemTracked.id, itemTracked);\n              }\n            }\n          }\n        });\n      }\n    };\n    exports.reset = function() {\n      mapOfItemsTracked = /* @__PURE__ */ new Map();\n      mapOfAllItemsTracked = /* @__PURE__ */ new Map();\n      itemTrackedModule.reset();\n    };\n    exports.setParams = function(newParams) {\n      Object.keys(newParams).forEach((key) => {\n        params[key] = newParams[key];\n      });\n    };\n    exports.enableKeepInMemory = function() {\n      keepAllHistoryInMemory = true;\n    };\n    exports.disableKeepInMemory = function() {\n      keepAllHistoryInMemory = false;\n    };\n    exports.getJSONOfTrackedItems = function(roundInt = true) {\n      return Array.from(mapOfItemsTracked.values()).map(function(itemTracked) {\n        return itemTracked.toJSON(roundInt);\n      });\n    };\n    exports.getJSONDebugOfTrackedItems = function(roundInt = true) {\n      return Array.from(mapOfItemsTracked.values()).map(function(itemTracked) {\n        return itemTracked.toJSONDebug(roundInt);\n      });\n    };\n    exports.getTrackedItemsInMOTFormat = function(frameNb) {\n      return Array.from(mapOfItemsTracked.values()).map(function(itemTracked) {\n        return itemTracked.toMOT(frameNb);\n      });\n    };\n    exports.getAllTrackedItems = function() {\n      return mapOfAllItemsTracked;\n    };\n    exports.getJSONOfAllTrackedItems = function() {\n      return Array.from(mapOfAllItemsTracked.values()).map(function(itemTracked) {\n        return itemTracked.toJSONGenericInfo();\n      });\n    };\n  }\n});\nexport default require_tracker();\n/**\n * k-d Tree JavaScript - V 1.01\n *\n * https://github.com/ubilabs/kd-tree-javascript\n *\n * <AUTHOR> Pricop <<EMAIL>>, 2012\n * <AUTHOR> Kleppe <<EMAIL>>, 2012\n * <AUTHOR> http://ubilabs.net, 2012\n * @license MIT License <http://www.opensource.org/licenses/mit-license.php>\n */\n"], "mappings": ";;;;;;AASA,UAAYA,MAAO,0BCRnB,IAAIC,EAAY,OAAO,eACnBC,EAAoB,OAAO,oBAC3BC,EAAS,CAACC,EAAQC,IAAUJ,EAAUG,EAAQ,OAAQ,CAAE,MAAAC,EAAO,aAAc,EAAK,CAAC,EACnFC,EAAa,CAACC,EAAIC,IAAQ,UAAqB,CACjD,OAAOA,MAAWD,EAAGL,EAAkBK,CAAE,EAAE,CAAC,CAAC,IAAIC,EAAM,CAAE,QAAS,CAAC,CAAE,GAAG,QAASA,CAAG,EAAGA,EAAI,OAC7F,EAGIC,EAAsBH,EAAW,CACnC,qEAAqEI,EAASC,EAAQ,CACpF,IAAIC,EAAkB,OAAO,QAAU,aAAe,OAAO,gBAAgB,KAAK,MAAM,GAAK,OAAO,UAAY,aAAe,SAAS,gBAAgB,KAAK,QAAQ,EACjKA,GACFC,EAAQ,IAAI,WAAW,EAAE,EACzBF,EAAO,QAA0BR,EAAO,UAAqB,CAC3D,OAAAS,EAAgBC,CAAK,EACdA,CACT,EAAG,WAAW,IAEdC,EAAO,IAAI,MAAM,EAAE,EACnBH,EAAO,QAA0BR,EAAO,UAAmB,CACzD,QAASY,EAAI,EAAGC,EAAGD,EAAI,GAAIA,IACpBA,EAAI,IACPC,EAAI,KAAK,OAAO,EAAI,YACtBF,EAAKC,CAAC,EAAIC,MAAQD,EAAI,IAAM,GAAK,IAEnC,OAAOD,CACT,EAAG,SAAS,GAEd,IAAID,EACAC,CACN,CACF,CAAC,EAGGG,EAAsBX,EAAW,CACnC,qEAAqEI,EAASC,EAAQ,CACpF,IAAIO,EAAY,CAAC,EACjB,IAAKH,EAAI,EAAGA,EAAI,IAAK,EAAEA,EACrBG,EAAUH,CAAC,GAAKA,EAAI,KAAK,SAAS,EAAE,EAAE,OAAO,CAAC,EAEhD,SAASI,EAAYC,EAAKC,EAAQ,CAChC,IAAIC,EAAKD,GAAU,EACfE,EAAML,EACV,OAAOK,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAI,IAAMC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAI,IAAMC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAI,IAAMC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAI,IAAMC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,EAAIC,EAAIH,EAAIE,GAAI,CAAC,CAC7S,CACAnB,EAAOgB,EAAa,aAAa,EACjCR,EAAO,QAAUQ,EACjB,IAAIJ,CACN,CACF,CAAC,EAGGS,EAAalB,EAAW,CAC1B,wDAAwDI,EAASC,EAAQ,CACvE,IAAIc,EAAMhB,EAAoB,EAC1BU,EAAcF,EAAoB,EACtC,SAASS,EAAGC,EAASP,EAAKC,EAAQ,CAChC,IAAIN,EAAIK,GAAOC,GAAU,EACrB,OAAOM,GAAW,WACpBP,EAAMO,IAAY,SAAW,IAAI,MAAM,EAAE,EAAI,KAC7CA,EAAU,MAEZA,EAAUA,GAAW,CAAC,EACtB,IAAIb,EAAOa,EAAQ,SAAWA,EAAQ,KAAOF,GAAK,EAGlD,GAFAX,EAAK,CAAC,EAAIA,EAAK,CAAC,EAAI,GAAK,GACzBA,EAAK,CAAC,EAAIA,EAAK,CAAC,EAAI,GAAK,IACrBM,EACF,QAASQ,EAAK,EAAGA,EAAK,GAAI,EAAEA,EAC1BR,EAAIL,EAAIa,CAAE,EAAId,EAAKc,CAAE,EAGzB,OAAOR,GAAOD,EAAYL,CAAI,CAChC,CACAX,EAAOuB,EAAI,IAAI,EACff,EAAO,QAAUe,CACnB,CACF,CAAC,EAGGG,EAAgBvB,EAAW,CAC7B,WAAWI,EAAS,CAClBA,EAAQ,oBAAsB,CAACoB,EAAYC,IACrCD,EAAW,GAAKC,EAMtB,IAAIC,EAA+B7B,EAAO,CAAC8B,EAAMC,IAAU,CACzD,IAAMC,EAAOF,EAAK,EAAIA,EAAK,EAAI,EACzBG,EAAOH,EAAK,EAAIA,EAAK,EAAI,EACzBI,EAAOJ,EAAK,EAAIA,EAAK,EAAI,EACzBK,EAAOL,EAAK,EAAIA,EAAK,EAAI,EAC/B,OAAIC,EAAM,GAAKC,GAAQD,EAAM,GAAKE,GAAQF,EAAM,GAAKG,GAAQH,EAAM,GAAKI,CAK1E,EAAG,cAAc,EACjB5B,EAAQ,aAAesB,EACvBtB,EAAQ,kBAAoB,CAAC6B,EAAOL,IACjBK,EAAM,KAAMN,GAASD,EAAaC,EAAMC,CAAK,CAAC,EAGjExB,EAAQ,yBAA2B,CAACoB,EAAYU,IACvCV,EAAW,OAAQW,GAAcD,EAAgB,QAAQC,EAAU,IAAI,EAAI,EAAE,EAEtF,IAAIC,EAAoCvC,EAAQwC,IACvC,CACL,GAAIA,EAAK,EAAIA,EAAK,EAAI,EACtB,GAAIA,EAAK,EAAIA,EAAK,EAAI,EACtB,GAAIA,EAAK,EAAIA,EAAK,EAAI,EACtB,GAAIA,EAAK,EAAIA,EAAK,EAAI,CACxB,GACC,mBAAmB,EACtBjC,EAAQ,kBAAoBgC,EAC5BhC,EAAQ,SAAW,CAACkC,EAAOC,IAAU,CACnC,IAAIC,EAAQJ,EAAkBE,CAAK,EAC/BG,EAAQL,EAAkBG,CAAK,EAC/BG,EAAa,KAAK,IAAIF,EAAM,GAAIC,EAAM,EAAE,EACxCE,EAAa,KAAK,IAAIH,EAAM,GAAIC,EAAM,EAAE,EACxCG,EAAa,KAAK,IAAIJ,EAAM,GAAIC,EAAM,EAAE,EACxCI,EAAa,KAAK,IAAIL,EAAM,GAAIC,EAAM,EAAE,EAC5C,GAAIG,EAAaF,GAAc,GAAKG,EAAaF,GAAc,EAC7D,MAAO,GACF,CACL,IAAMG,EAAaR,EAAM,EAAIA,EAAM,EAC7BS,EAAaR,EAAM,EAAIA,EAAM,EAC7BS,GAAqBJ,EAAaF,IAAeG,EAAaF,GAC9DM,EAAaH,EAAaC,EAAaC,EAC7C,OAAOA,EAAoBC,CAC7B,CACF,EACA7C,EAAQ,sBAAwB,CAACkC,EAAOC,EAAOW,KACtC,CACL,IAAKX,EAAM,EAAID,EAAM,GAAKY,EAC1B,IAAKX,EAAM,EAAID,EAAM,GAAKY,CAC5B,GAEF9C,EAAQ,oBAAsB,SAAS+C,EAAIC,EAAI,CAC7C,IAAIC,EAAQ,KAAK,KAAKF,EAAKC,CAAE,GAAK,KAAK,GAAK,KAC5C,OAAIC,EAAQ,EACND,EAAK,EACAC,EAEA,IAAMA,EAEXF,EAAK,EACA,IAAME,EAEN,IAAMA,CAEnB,CACF,CACF,CAAC,EAGGC,EAAsBtD,EAAW,CACnC,iBAAiBI,EAAS,CACxB,IAAImD,EAASrC,EAAW,EACpBsC,EAAsBjC,EAAc,EAAE,oBACtCkC,EAAwBlC,EAAc,EAAE,sBAC5CnB,EAAQ,wBAA0B,GAClC,IAAIsD,EAAY,EAChBtD,EAAQ,YAAc,SAASuD,EAAYC,EAASC,EAA0BC,EAAY,CACxF,IAAIC,EAAoCF,EACpCG,EAAc,CAAC,EACnB,OAAAA,EAAY,UAAY,GACxBA,EAAY,OAAS,GACrBA,EAAY,WAAaF,EACzBE,EAAY,8BAAgCH,EAC5CG,EAAY,SAAW,GACvBA,EAAY,YAAcJ,EAC1BI,EAAY,eAAiB,KAC7BA,EAAY,cAAgB,CAAC,EAC7BA,EAAY,UAAY,CAAC,EACzBA,EAAY,UAAUL,EAAW,IAAI,EAAI,EACzCK,EAAY,EAAIL,EAAW,EAC3BK,EAAY,EAAIL,EAAW,EAC3BK,EAAY,EAAIL,EAAW,EAC3BK,EAAY,EAAIL,EAAW,EAC3BK,EAAY,KAAOL,EAAW,KAC9BK,EAAY,WAAaL,EAAW,WACpCK,EAAY,YAAc,CAAC,EAC3BA,EAAY,YAAY,KAAK,CAC3B,EAAGL,EAAW,EACd,EAAGA,EAAW,EACd,EAAGA,EAAW,EACd,EAAGA,EAAW,EACd,WAAYA,EAAW,UACzB,CAAC,EACGK,EAAY,YAAY,QAAU5D,EAAQ,yBAC5C4D,EAAY,YAAY,MAAM,EAEhCA,EAAY,SAAW,CACrB,GAAI,EACJ,GAAI,CACN,EACAA,EAAY,cAAgB,EAC5BA,EAAY,GAAKT,EAAO,EACxBS,EAAY,UAAYN,EACxBA,IACAM,EAAY,OAAS,SAASC,EAAaC,EAAU,CAC/C,KAAK,iBACP,KAAK,eAAiB,KACtB,KAAK,cAAgB,CAAC,GAExB,KAAK,SAAW,GAChB,KAAK,eAAiB,EACtB,KAAK,EAAID,EAAY,EACrB,KAAK,EAAIA,EAAY,EACrB,KAAK,EAAIA,EAAY,EACrB,KAAK,EAAIA,EAAY,EACrB,KAAK,WAAaA,EAAY,WAC9B,KAAK,YAAY,KAAK,CACpB,EAAG,KAAK,EACR,EAAG,KAAK,EACR,EAAG,KAAK,EACR,EAAG,KAAK,EACR,WAAY,KAAK,UACnB,CAAC,EACGD,EAAY,YAAY,QAAU5D,EAAQ,yBAC5C4D,EAAY,YAAY,MAAM,EAEhC,KAAK,KAAOC,EAAY,KACpB,KAAK,UAAUA,EAAY,IAAI,EACjC,KAAK,UAAUA,EAAY,IAAI,IAE/B,KAAK,UAAUA,EAAY,IAAI,EAAI,EAErC,KAAK,8BAAgCF,EACrC,KAAK,SAAW,KAAK,qBAAqB,CAC5C,EACAC,EAAY,cAAgB,UAAW,CACrC,YAAK,UAAY,GACV,IACT,EACAA,EAAY,gBAAkB,UAAW,CACvC,YAAK,UAAY,GACV,IACT,EACAA,EAAY,UAAY,SAASE,EAAU,CACrC,KAAK,iBAAmB,OAC1B,KAAK,eAAiBA,EACtB,KAAK,cAAgB,CACnB,EAAG,KAAK,EACR,EAAG,KAAK,EACR,EAAG,KAAK,EACR,EAAG,KAAK,CACV,GAEF,KAAK,gCACL,KAAK,SAAW,GACZ,KAAK,YAAc,KAAK,eAAiB,IAC3C,KAAK,8BAAgC,GAEzC,EACAF,EAAY,+BAAiC,UAAW,CACtD,KAAK,YAAY,KAAK,CACpB,EAAG,KAAK,EACR,EAAG,KAAK,EACR,EAAG,KAAK,EACR,EAAG,KAAK,EACR,WAAY,KAAK,UACnB,CAAC,EACGA,EAAY,YAAY,QAAU5D,EAAQ,yBAC5C4D,EAAY,YAAY,MAAM,EAEhC,KAAK,EAAI,KAAK,EAAI,KAAK,SAAS,GAChC,KAAK,EAAI,KAAK,EAAI,KAAK,SAAS,EAClC,EACAA,EAAY,oBAAsB,UAAW,CAC3C,MAAO,CACL,EAAG,KAAK,EAAI,KAAK,SAAS,GAC1B,EAAG,KAAK,EAAI,KAAK,SAAS,GAC1B,EAAG,KAAK,EACR,EAAG,KAAK,CACV,CACF,EACAA,EAAY,OAAS,UAAW,CAC9B,OAAO,KAAK,8BAAgC,CAC9C,EACAA,EAAY,qBAAuB,UAAW,CAC5C,GAAI5D,EAAQ,yBAA2B,EACrC,MAAO,CAAE,GAAI,OAAQ,GAAI,MAAO,EAElC,GAAI,KAAK,YAAY,QAAUA,EAAQ,wBAAyB,CAC9D,IAAM+D,EAAQ,KAAK,YAAY,CAAC,EAC1BC,EAAM,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EACxD,OAAOX,EAAsBU,EAAOC,EAAK,KAAK,YAAY,MAAM,CAClE,KAAO,CACL,IAAMD,EAAQ,KAAK,YAAY,KAAK,YAAY,OAAS/D,EAAQ,uBAAuB,EAClFgE,EAAM,KAAK,YAAY,KAAK,YAAY,OAAS,CAAC,EACxD,OAAOX,EAAsBU,EAAOC,EAAKhE,EAAQ,uBAAuB,CAC1E,CACF,EACA4D,EAAY,qBAAuB,UAAW,CAC5C,IAAIK,EAA8B,EAC9BC,EAAoB,GACxB,cAAO,KAAK,KAAK,SAAS,EAAE,IAAKC,GAAS,CACpC,KAAK,UAAUA,CAAI,EAAIF,IACzBC,EAAoBC,EACpBF,EAA8B,KAAK,UAAUE,CAAI,EAErD,CAAC,EACMD,CACT,EACAN,EAAY,YAAc,SAASQ,EAAW,GAAM,CAClD,MAAO,CACL,GAAI,KAAK,GACT,UAAW,KAAK,UAChB,EAAGA,EAAW,SAAS,KAAK,EAAG,EAAE,EAAI,KAAK,EAC1C,EAAGA,EAAW,SAAS,KAAK,EAAG,EAAE,EAAI,KAAK,EAC1C,EAAGA,EAAW,SAAS,KAAK,EAAG,EAAE,EAAI,KAAK,EAC1C,EAAGA,EAAW,SAAS,KAAK,EAAG,EAAE,EAAI,KAAK,EAC1C,WAAY,KAAK,MAAM,KAAK,WAAa,GAAG,EAAI,IAEhD,QAAS,SAAShB,EAAoB,KAAK,SAAS,GAAI,CAAC,KAAK,SAAS,EAAE,CAAC,EAC1E,KAAM,KAAK,qBAAqB,EAChC,SAAU,KAAK,SACf,YAAa,KAAK,YAClB,eAAgB,KAAK,cACvB,CACF,EACAQ,EAAY,OAAS,SAASQ,EAAW,GAAM,CAC7C,MAAO,CACL,GAAI,KAAK,UACT,EAAGA,EAAW,SAAS,KAAK,EAAG,EAAE,EAAI,KAAK,EAC1C,EAAGA,EAAW,SAAS,KAAK,EAAG,EAAE,EAAI,KAAK,EAC1C,EAAGA,EAAW,SAAS,KAAK,EAAG,EAAE,EAAI,KAAK,EAC1C,EAAGA,EAAW,SAAS,KAAK,EAAG,EAAE,EAAI,KAAK,EAC1C,WAAY,KAAK,MAAM,KAAK,WAAa,GAAG,EAAI,IAEhD,QAAS,SAAShB,EAAoB,KAAK,SAAS,GAAI,CAAC,KAAK,SAAS,EAAE,EAAG,EAAE,EAC9E,KAAM,KAAK,qBAAqB,EAChC,SAAU,KAAK,QACjB,CACF,EACAQ,EAAY,MAAQ,SAASS,EAAY,CACvC,MAAO,GAAGA,CAAU,IAAI,KAAK,SAAS,IAAI,KAAK,EAAI,KAAK,EAAI,CAAC,IAAI,KAAK,EAAI,KAAK,EAAI,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,WAAa,GAAG,WACnI,EACAT,EAAY,kBAAoB,UAAW,CACzC,MAAO,CACL,GAAI,KAAK,GACT,UAAW,KAAK,UAChB,YAAa,KAAK,YAClB,eAAgB,KAAK,eACrB,cAAe,KAAK,cACpB,cAAe,KAAK,eAAiB,KAAK,YAC1C,KAAM,KAAK,qBAAqB,CAClC,CACF,EACOA,CACT,EACA5D,EAAQ,MAAQ,UAAW,CACzBsD,EAAY,CACd,CACF,CACF,CAAC,EAGGgB,EAAqB1E,EAAW,CAClC,oBAAoBI,EAAS,EAC1B,SAASuE,EAAMC,EAAS,CACnB,OAAO,QAAW,YAAc,OAAO,IACzC,OAAO,CAAC,SAAS,EAAGA,CAAO,EAE3BA,EADS,OAAOxE,GAAY,SACpBA,EAEAuE,CAFO,CAInB,GAAGvE,EAAS,SAASyE,EAAU,CAC7B,SAASC,EAAKC,EAAKC,EAAWC,EAAQ,CACpC,KAAK,IAAMF,EACX,KAAK,KAAO,KACZ,KAAK,MAAQ,KACb,KAAK,OAASE,EACd,KAAK,UAAYD,CACnB,CACAnF,EAAOiF,EAAM,MAAM,EACnB,SAASI,EAAOC,EAAQC,EAAQC,EAAY,CAC1C,IAAIC,EAAO,KACX,SAASC,EAAUC,EAASC,EAAOR,EAAQ,CACzC,IAAIS,EAAMD,EAAQJ,EAAW,OAAQM,EAAQC,EAC7C,OAAIJ,EAAQ,SAAW,EACd,KAELA,EAAQ,SAAW,EACd,IAAIV,EAAKU,EAAQ,CAAC,EAAGE,EAAKT,CAAM,GAEzCO,EAAQ,KAAK,SAASK,EAAGC,EAAG,CAC1B,OAAOD,EAAER,EAAWK,CAAG,CAAC,EAAII,EAAET,EAAWK,CAAG,CAAC,CAC/C,CAAC,EACDC,EAAS,KAAK,MAAMH,EAAQ,OAAS,CAAC,EACtCI,EAAO,IAAId,EAAKU,EAAQG,CAAM,EAAGD,EAAKT,CAAM,EAC5CW,EAAK,KAAOL,EAAUC,EAAQ,MAAM,EAAGG,CAAM,EAAGF,EAAQ,EAAGG,CAAI,EAC/DA,EAAK,MAAQL,EAAUC,EAAQ,MAAMG,EAAS,CAAC,EAAGF,EAAQ,EAAGG,CAAI,EAC1DA,EACT,CACA/F,EAAO0F,EAAW,WAAW,EAC7B,SAASQ,EAASC,EAAM,CACtBV,EAAK,KAAOU,EACZ,SAASC,EAActB,EAAM,CACvBA,EAAK,OACPA,EAAK,KAAK,OAASA,EACnBsB,EAActB,EAAK,IAAI,GAErBA,EAAK,QACPA,EAAK,MAAM,OAASA,EACpBsB,EAActB,EAAK,KAAK,EAE5B,CACA9E,EAAOoG,EAAe,eAAe,EACrCA,EAAcX,EAAK,IAAI,CACzB,CACAzF,EAAOkG,EAAU,UAAU,EACtB,MAAM,QAAQZ,CAAM,EAGvB,KAAK,KAAOI,EAAUJ,EAAQ,EAAG,IAAI,EAFrCY,EAASZ,EAAQC,EAAQC,CAAU,EAGrC,KAAK,OAAS,SAASa,EAAK,CACrBA,IACHA,EAAM,KAAK,MACb,IAAIC,EAAO,IAAIrB,EAAKoB,EAAI,IAAKA,EAAI,UAAW,IAAI,EAChD,OAAIA,EAAI,OACNC,EAAK,KAAOb,EAAK,OAAOY,EAAI,IAAI,GAC9BA,EAAI,QACNC,EAAK,MAAQb,EAAK,OAAOY,EAAI,KAAK,GAC7BC,CACT,EACA,KAAK,OAAS,SAASvE,EAAO,CAC5B,SAASwE,EAAYR,EAAMX,EAAQ,CACjC,GAAIW,IAAS,KACX,OAAOX,EAET,IAAIoB,EAAahB,EAAWO,EAAK,SAAS,EAC1C,OAAIhE,EAAMyE,CAAU,EAAIT,EAAK,IAAIS,CAAU,EAClCD,EAAYR,EAAK,KAAMA,CAAI,EAE3BQ,EAAYR,EAAK,MAAOA,CAAI,CAEvC,CACA/F,EAAOuG,EAAa,aAAa,EACjC,IAAIE,EAAiBF,EAAY,KAAK,KAAM,IAAI,EAAGG,EAASvB,EAC5D,GAAIsB,IAAmB,KAAM,CAC3B,KAAK,KAAO,IAAIxB,EAAKlD,EAAO,EAAG,IAAI,EACnC,MACF,CACA2E,EAAU,IAAIzB,EAAKlD,GAAQ0E,EAAe,UAAY,GAAKjB,EAAW,OAAQiB,CAAc,EAC5FtB,EAAYK,EAAWiB,EAAe,SAAS,EAC3C1E,EAAMoD,CAAS,EAAIsB,EAAe,IAAItB,CAAS,EACjDsB,EAAe,KAAOC,EAEtBD,EAAe,MAAQC,CAE3B,EACA,KAAK,OAAS,SAAS3E,EAAO,CAC5B,IAAIgE,EACJ,SAASY,EAAWC,EAAO,CACzB,GAAIA,IAAU,KACZ,OAAO,KAET,GAAIA,EAAM,MAAQ7E,EAChB,OAAO6E,EAET,IAAIzB,EAAYK,EAAWoB,EAAM,SAAS,EAC1C,OAAI7E,EAAMoD,CAAS,EAAIyB,EAAM,IAAIzB,CAAS,EACjCwB,EAAWC,EAAM,KAAMA,CAAK,EAE5BD,EAAWC,EAAM,MAAOA,CAAK,CAExC,CACA5G,EAAO2G,EAAY,YAAY,EAC/B,SAASE,EAAWD,EAAO,CACzB,IAAIE,EAAUC,EAASC,EACvB,SAASC,EAAQC,EAAOrB,EAAK,CAC3B,IAAIV,EAAWgC,EAAKC,EAAMC,EAAOC,EACjC,OAAIJ,IAAU,KACL,MAET/B,EAAYK,EAAWK,CAAG,EACtBqB,EAAM,YAAcrB,EAClBqB,EAAM,OAAS,KACVD,EAAQC,EAAM,KAAMrB,CAAG,EAEzBqB,GAETC,EAAMD,EAAM,IAAI/B,CAAS,EACzBiC,EAAOH,EAAQC,EAAM,KAAMrB,CAAG,EAC9BwB,EAAQJ,EAAQC,EAAM,MAAOrB,CAAG,EAChCyB,EAAMJ,EACFE,IAAS,MAAQA,EAAK,IAAIjC,CAAS,EAAIgC,IACzCG,EAAMF,GAEJC,IAAU,MAAQA,EAAM,IAAIlC,CAAS,EAAImC,EAAI,IAAInC,CAAS,IAC5DmC,EAAMD,GAEDC,GACT,CAEA,GADAtH,EAAOiH,EAAS,SAAS,EACrBL,EAAM,OAAS,MAAQA,EAAM,QAAU,KAAM,CAC/C,GAAIA,EAAM,SAAW,KAAM,CACzBnB,EAAK,KAAO,KACZ,MACF,CACAuB,EAAaxB,EAAWoB,EAAM,OAAO,SAAS,EAC1CA,EAAM,IAAII,CAAU,EAAIJ,EAAM,OAAO,IAAII,CAAU,EACrDJ,EAAM,OAAO,KAAO,KAEpBA,EAAM,OAAO,MAAQ,KAEvB,MACF,CACIA,EAAM,QAAU,MAClBE,EAAWG,EAAQL,EAAM,MAAOA,EAAM,SAAS,EAC/CG,EAAUD,EAAS,IACnBD,EAAWC,CAAQ,EACnBF,EAAM,IAAMG,IAEZD,EAAWG,EAAQL,EAAM,KAAMA,EAAM,SAAS,EAC9CG,EAAUD,EAAS,IACnBD,EAAWC,CAAQ,EACnBF,EAAM,MAAQA,EAAM,KACpBA,EAAM,KAAO,KACbA,EAAM,IAAMG,EAEhB,CACA/G,EAAO6G,EAAY,YAAY,EAC/Bd,EAAOY,EAAWlB,EAAK,IAAI,EACvBM,IAAS,MAGbc,EAAWd,CAAI,CACjB,EACA,KAAK,QAAU,SAAShE,EAAOwF,EAAUC,EAAa,CACpD,IAAI5G,EAAG6G,EAAQC,EACfA,EAAY,IAAIC,EACd,SAASC,EAAG,CACV,MAAO,CAACA,EAAE,CAAC,CACb,CACF,EACA,SAASC,EAAc9B,EAAM,CAC3B,IAAI+B,EAAW3C,EAAYK,EAAWO,EAAK,SAAS,EAAGgC,EAAcxC,EAAOxD,EAAOgE,EAAK,GAAG,EAAGiC,EAAc,CAAC,EAAGC,EAAgBC,EAAY/G,EAC5I,SAASgH,EAASvB,EAAOwB,EAAU,CACjCV,EAAU,KAAK,CAACd,EAAOwB,CAAQ,CAAC,EAC5BV,EAAU,KAAK,EAAIH,GACrBG,EAAU,IAAI,CAElB,CAEA,IADA1H,EAAOmI,EAAU,UAAU,EACtBhH,EAAK,EAAGA,EAAKqE,EAAW,OAAQrE,GAAM,EACrCA,IAAO4E,EAAK,UACdiC,EAAYxC,EAAWrE,CAAE,CAAC,EAAIY,EAAMyD,EAAWrE,CAAE,CAAC,EAElD6G,EAAYxC,EAAWrE,CAAE,CAAC,EAAI4E,EAAK,IAAIP,EAAWrE,CAAE,CAAC,EAIzD,GADA8G,EAAiB1C,EAAOyC,EAAajC,EAAK,GAAG,EACzCA,EAAK,QAAU,MAAQA,EAAK,OAAS,KAAM,EACzC2B,EAAU,KAAK,EAAIH,GAAYQ,EAAcL,EAAU,KAAK,EAAE,CAAC,IACjES,EAASpC,EAAMgC,CAAW,EAE5B,MACF,CACIhC,EAAK,QAAU,KACjB+B,EAAY/B,EAAK,KACRA,EAAK,OAAS,KACvB+B,EAAY/B,EAAK,MAEbhE,EAAMoD,CAAS,EAAIY,EAAK,IAAIZ,CAAS,EACvC2C,EAAY/B,EAAK,KAEjB+B,EAAY/B,EAAK,MAGrB8B,EAAcC,CAAS,GACnBJ,EAAU,KAAK,EAAIH,GAAYQ,EAAcL,EAAU,KAAK,EAAE,CAAC,IACjES,EAASpC,EAAMgC,CAAW,GAExBL,EAAU,KAAK,EAAIH,GAAY,KAAK,IAAIU,CAAc,EAAIP,EAAU,KAAK,EAAE,CAAC,KAC1EI,IAAc/B,EAAK,KACrBmC,EAAanC,EAAK,MAElBmC,EAAanC,EAAK,KAEhBmC,IAAe,MACjBL,EAAcK,CAAU,EAG9B,CAEA,GADAlI,EAAO6H,EAAe,eAAe,EACjCL,EACF,IAAK5G,EAAI,EAAGA,EAAI2G,EAAU3G,GAAK,EAC7B8G,EAAU,KAAK,CAAC,KAAMF,CAAW,CAAC,EAMtC,IAHI/B,EAAK,MACPoC,EAAcpC,EAAK,IAAI,EACzBgC,EAAS,CAAC,EACL7G,EAAI,EAAGA,EAAI,KAAK,IAAI2G,EAAUG,EAAU,QAAQ,MAAM,EAAG9G,GAAK,EAC7D8G,EAAU,QAAQ9G,CAAC,EAAE,CAAC,GACxB6G,EAAO,KAAK,CAACC,EAAU,QAAQ9G,CAAC,EAAE,CAAC,EAAE,IAAK8G,EAAU,QAAQ9G,CAAC,EAAE,CAAC,CAAC,CAAC,EAGtE,OAAO6G,CACT,EACA,KAAK,cAAgB,UAAW,CAC9B,SAASY,EAAOtC,EAAM,CACpB,OAAIA,IAAS,KACJ,EAEF,KAAK,IAAIsC,EAAOtC,EAAK,IAAI,EAAGsC,EAAOtC,EAAK,KAAK,CAAC,EAAI,CAC3D,CACA/F,EAAOqI,EAAQ,QAAQ,EACvB,SAASC,EAAMvC,EAAM,CACnB,OAAIA,IAAS,KACJ,EAEFuC,EAAMvC,EAAK,IAAI,EAAIuC,EAAMvC,EAAK,KAAK,EAAI,CAChD,CACA,OAAA/F,EAAOsI,EAAO,OAAO,EACdD,EAAO5C,EAAK,IAAI,GAAK,KAAK,IAAI6C,EAAM7C,EAAK,IAAI,CAAC,EAAI,KAAK,IAAI,CAAC,EACrE,CACF,CACAzF,EAAOqF,EAAQ,QAAQ,EACvB,SAASsC,EAAWY,EAAe,CACjC,KAAK,QAAU,CAAC,EAChB,KAAK,cAAgBA,CACvB,CACAvI,EAAO2H,EAAY,YAAY,EAC/BA,EAAW,UAAY,CACrB,KAAM,SAASa,EAAS,CACtB,KAAK,QAAQ,KAAKA,CAAO,EACzB,KAAK,SAAS,KAAK,QAAQ,OAAS,CAAC,CACvC,EACA,IAAK,UAAW,CACd,IAAIf,EAAS,KAAK,QAAQ,CAAC,EACvBlD,EAAM,KAAK,QAAQ,IAAI,EAC3B,OAAI,KAAK,QAAQ,OAAS,IACxB,KAAK,QAAQ,CAAC,EAAIA,EAClB,KAAK,SAAS,CAAC,GAEVkD,CACT,EACA,KAAM,UAAW,CACf,OAAO,KAAK,QAAQ,CAAC,CACvB,EACA,OAAQ,SAAS1B,EAAM,CAErB,QADI0C,EAAM,KAAK,QAAQ,OACd7H,EAAI,EAAGA,EAAI6H,EAAK7H,IACvB,GAAI,KAAK,QAAQA,CAAC,GAAKmF,EAAM,CAC3B,IAAIxB,EAAM,KAAK,QAAQ,IAAI,EACvB3D,GAAK6H,EAAM,IACb,KAAK,QAAQ7H,CAAC,EAAI2D,EACd,KAAK,cAAcA,CAAG,EAAI,KAAK,cAAcwB,CAAI,EACnD,KAAK,SAASnF,CAAC,EAEf,KAAK,SAASA,CAAC,GAEnB,MACF,CAEF,MAAM,IAAI,MAAM,iBAAiB,CACnC,EACA,KAAM,UAAW,CACf,OAAO,KAAK,QAAQ,MACtB,EACA,SAAU,SAAS8H,EAAG,CAEpB,QADIF,EAAU,KAAK,QAAQE,CAAC,EACrBA,EAAI,GAAG,CACZ,IAAIC,EAAU,KAAK,OAAOD,EAAI,GAAK,CAAC,EAAI,EAAGtD,EAAS,KAAK,QAAQuD,CAAO,EACxE,GAAI,KAAK,cAAcH,CAAO,EAAI,KAAK,cAAcpD,CAAM,EACzD,KAAK,QAAQuD,CAAO,EAAIH,EACxB,KAAK,QAAQE,CAAC,EAAItD,EAClBsD,EAAIC,MAEJ,MAEJ,CACF,EACA,SAAU,SAASD,EAAG,CAEpB,QADIE,EAAS,KAAK,QAAQ,OAAQJ,EAAU,KAAK,QAAQE,CAAC,EAAGG,EAAY,KAAK,cAAcL,CAAO,IACtF,CACX,IAAIM,GAAWJ,EAAI,GAAK,EAAGK,EAAUD,EAAU,EAC3CE,EAAO,KACX,GAAID,EAAUH,EAAQ,CACpB,IAAIK,EAAS,KAAK,QAAQF,CAAO,EAAGG,EAAc,KAAK,cAAcD,CAAM,EACvEC,EAAcL,IAChBG,EAAOD,EACX,CACA,GAAID,EAAUF,EAAQ,CACpB,IAAIO,EAAS,KAAK,QAAQL,CAAO,EAAGM,EAAc,KAAK,cAAcD,CAAM,EACvEC,GAAeJ,GAAQ,KAAOH,EAAYK,KAC5CF,EAAOF,EAEX,CACA,GAAIE,GAAQ,KACV,KAAK,QAAQN,CAAC,EAAI,KAAK,QAAQM,CAAI,EACnC,KAAK,QAAQA,CAAI,EAAIR,EACrBE,EAAIM,MAEJ,MAEJ,CACF,CACF,EACAhE,EAAS,OAASK,EAClBL,EAAS,WAAa2C,CACxB,CAAC,CACH,CACF,CAAC,EAGG0B,EAAkBlJ,EAAW,CAC/B,yEAAyEI,EAASC,EAAQ,CACxF,IAAI8I,EAAW,SAAS,OAAO,iBAAmB,CAAC,GAAM,iBACrDC,EAAoB,EACxB,SAASC,GAAU,CACjB,KAAK,EAAI,KACT,KAAK,YAAc,CAAC,EACpB,KAAK,YAAc,CAAC,EACpB,KAAK,EAAI,EACT,KAAK,KAAO,EACZ,KAAK,KAAO,EACZ,KAAK,OAAS,KACd,KAAK,KAAO,IACd,CACAxJ,EAAOwJ,EAAS,SAAS,EACzBA,EAAQ,UAAU,WAAa,SAASC,EAAQC,EAAW,CACzDA,EAAYA,GAAaH,EACzB,IAAII,EAAc,EACdC,EAAaH,EAAO,OACpB7I,EACJ,IAAKA,EAAI,EAAGA,EAAIgJ,EAAY,EAAEhJ,EACxB6I,EAAO7I,CAAC,EAAE,OAAS+I,IACrBA,EAAcF,EAAO7I,CAAC,EAAE,QAC5BgJ,EAAaD,EAAcC,EAAaD,EAAcC,EACtD,IAAIC,EAAa,CAAC,EAClB,IAAKjJ,EAAI,EAAGA,EAAIgJ,EAAY,EAAEhJ,EAAG,CAG/B,QAFIkJ,EAAML,EAAO7I,CAAC,GAAK,CAAC,EACpBmJ,EAAUD,EAAI,MAAM,EACjBF,EAAaG,EAAQ,QAC1BA,EAAQ,KAAKL,CAAS,EACxBG,EAAW,KAAKE,CAAO,CACzB,CACA,OAAOF,CACT,EACAL,EAAQ,UAAU,QAAU,SAASQ,EAAaxI,EAAS,CACzDA,EAAUA,GAAW,CAAC,EACtBA,EAAQ,SAAWA,EAAQ,UAAY+H,EACvC,KAAK,EAAI,KAAK,WAAWS,EAAaxI,EAAQ,QAAQ,EACtD,KAAK,EAAI,KAAK,EAAE,OAChB,KAAK,gBAAkBwI,EAAY,OACnC,KAAK,eAAiBA,EAAY,CAAC,EAAE,OAErC,QADIC,EAAc,CAAC,EACZA,EAAY,OAAS,KAAK,GAC/BA,EAAY,KAAK,EAAK,EACxB,KAAK,YAAcA,EAAY,MAAM,EACrC,KAAK,YAAcA,EAAY,MAAM,EACrC,KAAK,KAAO,EACZ,KAAK,KAAO,EACZ,KAAK,KAAO,KAAK,cAAc,KAAK,EAAI,EAAG,CAAC,EAC5C,KAAK,OAAS,KAAK,cAAc,KAAK,EAAG,CAAC,EAU1C,QATIC,EAAO,EACPC,EAAQ,CACV,EAAG,KAAK,QACR,EAAG,KAAK,QACR,EAAG,KAAK,QACR,EAAG,KAAK,QACR,EAAG,KAAK,QACR,EAAG,KAAK,OACV,IACa,CACX,IAAIC,EAAOD,EAAMD,CAAI,EACrB,GAAI,CAACE,EACH,MACFF,EAAOE,EAAK,MAAM,IAAI,CACxB,CAEA,QADIC,EAAU,CAAC,EACNzJ,EAAI,EAAGA,EAAI,KAAK,gBAAiB,EAAEA,EAC1C,QAAS0J,EAAI,EAAGA,EAAI,KAAK,eAAgB,EAAEA,EACrC,KAAK,OAAO1J,CAAC,EAAE0J,CAAC,GAAK,GACvBD,EAAQ,KAAK,CAACzJ,EAAG0J,CAAC,CAAC,EACzB,OAAOD,CACT,EACAb,EAAQ,UAAU,cAAgB,SAASd,EAAG6B,EAAK,CAEjD,QADId,EAAS,CAAC,EACL7I,EAAI,EAAGA,EAAI8H,EAAG,EAAE9H,EAAG,CAC1B6I,EAAO7I,CAAC,EAAI,CAAC,EACb,QAAS0J,EAAI,EAAGA,EAAI5B,EAAG,EAAE4B,EACvBb,EAAO7I,CAAC,EAAE0J,CAAC,EAAIC,CACnB,CACA,OAAOd,CACT,EACAD,EAAQ,UAAU,QAAU,UAAW,CACrC,QAAS5I,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAE5B,QADI4J,EAAS,KAAK,IAAI,MAAM,KAAM,KAAK,EAAE5J,CAAC,CAAC,EAClC0J,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,KAAK,EAAE1J,CAAC,EAAE0J,CAAC,GAAKE,EAEpB,MAAO,EACT,EACAhB,EAAQ,UAAU,QAAU,UAAW,CACrC,QAAS5I,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,QAAS0J,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,GAAI,KAAK,EAAE1J,CAAC,EAAE0J,CAAC,IAAM,GAAK,CAAC,KAAK,YAAYA,CAAC,GAAK,CAAC,KAAK,YAAY1J,CAAC,EAAG,CACtE,KAAK,OAAOA,CAAC,EAAE0J,CAAC,EAAI,EACpB,KAAK,YAAYA,CAAC,EAAI,GACtB,KAAK,YAAY1J,CAAC,EAAI,GACtB,KACF,CAGJ,YAAK,eAAe,EACb,CACT,EACA4I,EAAQ,UAAU,QAAU,UAAW,CAErC,QADIlB,EAAQ,EACH1H,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,QAAS0J,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EACxB,KAAK,OAAO1J,CAAC,EAAE0J,CAAC,GAAK,GAAK,KAAK,YAAYA,CAAC,GAAK,KACnD,KAAK,YAAYA,CAAC,EAAI,GACtB,EAAEhC,GAIR,OAAOA,GAAS,KAAK,EAAI,EAAI,CAC/B,EACAkB,EAAQ,UAAU,QAAU,UAAW,CAGrC,QAFIiB,EAAO,GACPX,EAAM,GAAIY,EAAM,GAAIC,EAAW,GAC5B,CAACF,GAAM,CACZ,IAAIG,EAAI,KAAK,cAAc,EAG3B,GAFAd,EAAMc,EAAE,CAAC,EACTF,EAAME,EAAE,CAAC,EACLd,EAAM,EACR,MAAO,GAGT,GAFA,KAAK,OAAOA,CAAG,EAAEY,CAAG,EAAI,EACxBC,EAAW,KAAK,mBAAmBb,CAAG,EAClCa,GAAY,EACdD,EAAMC,EACN,KAAK,YAAYb,CAAG,EAAI,GACxB,KAAK,YAAYY,CAAG,EAAI,OAExB,aAAK,KAAOZ,EACZ,KAAK,KAAOY,EACL,CAEX,CACF,EACAlB,EAAQ,UAAU,QAAU,UAAW,CACrC,IAAIlB,EAAQ,EACZ,KAAK,KAAKA,CAAK,EAAE,CAAC,EAAI,KAAK,KAC3B,KAAK,KAAKA,CAAK,EAAE,CAAC,EAAI,KAAK,KAE3B,QADImC,EAAO,GACJ,CAACA,GAAM,CACZ,IAAIX,EAAM,KAAK,mBAAmB,KAAK,KAAKxB,CAAK,EAAE,CAAC,CAAC,EAQrD,GAPIwB,GAAO,GACTxB,IACA,KAAK,KAAKA,CAAK,EAAE,CAAC,EAAIwB,EACtB,KAAK,KAAKxB,CAAK,EAAE,CAAC,EAAI,KAAK,KAAKA,EAAQ,CAAC,EAAE,CAAC,GAE5CmC,EAAO,GAEL,CAACA,EAAM,CACT,IAAIC,EAAM,KAAK,oBAAoB,KAAK,KAAKpC,CAAK,EAAE,CAAC,CAAC,EACtDA,IACA,KAAK,KAAKA,CAAK,EAAE,CAAC,EAAI,KAAK,KAAKA,EAAQ,CAAC,EAAE,CAAC,EAC5C,KAAK,KAAKA,CAAK,EAAE,CAAC,EAAIoC,CACxB,CACF,CACA,YAAK,eAAe,KAAK,KAAMpC,CAAK,EACpC,KAAK,eAAe,EACpB,KAAK,eAAe,EACb,CACT,EACAkB,EAAQ,UAAU,QAAU,UAAW,CAErC,QADIgB,EAAS,KAAK,gBAAgB,EACzB5J,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,QAAS0J,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EACxB,KAAK,YAAY1J,CAAC,IACpB,KAAK,EAAEA,CAAC,EAAE0J,CAAC,GAAKE,GACb,KAAK,YAAYF,CAAC,IACrB,KAAK,EAAE1J,CAAC,EAAE0J,CAAC,GAAKE,GAGtB,MAAO,EACT,EACAhB,EAAQ,UAAU,gBAAkB,UAAW,CAE7C,QADIgB,EAASlB,EACJ1I,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,QAAS0J,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EACxB,CAAC,KAAK,YAAY1J,CAAC,GAAK,CAAC,KAAK,YAAY0J,CAAC,GACzCE,EAAS,KAAK,EAAE5J,CAAC,EAAE0J,CAAC,IACtBE,EAAS,KAAK,EAAE5J,CAAC,EAAE0J,CAAC,GAE5B,OAAOE,CACT,EACAhB,EAAQ,UAAU,cAAgB,UAAW,CAC3C,QAAS5I,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,QAAS0J,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,GAAI,KAAK,EAAE1J,CAAC,EAAE0J,CAAC,IAAM,GAAK,CAAC,KAAK,YAAY1J,CAAC,GAAK,CAAC,KAAK,YAAY0J,CAAC,EACnE,MAAO,CAAC1J,EAAG0J,CAAC,EAClB,MAAO,CAAC,GAAI,EAAE,CAChB,EACAd,EAAQ,UAAU,mBAAqB,SAASM,EAAK,CACnD,QAASQ,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,GAAI,KAAK,OAAOR,CAAG,EAAEQ,CAAC,GAAK,EACzB,OAAOA,EACX,MAAO,EACT,EACAd,EAAQ,UAAU,mBAAqB,SAASkB,EAAK,CACnD,QAAS9J,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,GAAI,KAAK,OAAOA,CAAC,EAAE8J,CAAG,GAAK,EACzB,OAAO9J,EACX,MAAO,EACT,EACA4I,EAAQ,UAAU,oBAAsB,SAASM,EAAK,CACpD,QAASQ,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,GAAI,KAAK,OAAOR,CAAG,EAAEQ,CAAC,GAAK,EACzB,OAAOA,EACX,MAAO,EACT,EACAd,EAAQ,UAAU,eAAiB,SAASqB,EAAMvC,EAAO,CACvD,QAAS1H,EAAI,EAAGA,GAAK0H,EAAO,EAAE1H,EAC5B,KAAK,OAAOiK,EAAKjK,CAAC,EAAE,CAAC,CAAC,EAAEiK,EAAKjK,CAAC,EAAE,CAAC,CAAC,EAAI,KAAK,OAAOiK,EAAKjK,CAAC,EAAE,CAAC,CAAC,EAAEiK,EAAKjK,CAAC,EAAE,CAAC,CAAC,GAAK,EAAI,EAAI,CACzF,EACA4I,EAAQ,UAAU,eAAiB,UAAW,CAC5C,QAAS5I,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,KAAK,YAAYA,CAAC,EAAI,GACtB,KAAK,YAAYA,CAAC,EAAI,EAE1B,EACA4I,EAAQ,UAAU,eAAiB,UAAW,CAC5C,QAAS5I,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EAC5B,QAAS0J,EAAI,EAAGA,EAAI,KAAK,EAAG,EAAEA,EACxB,KAAK,OAAO1J,CAAC,EAAE0J,CAAC,GAAK,IACvB,KAAK,OAAO1J,CAAC,EAAE0J,CAAC,EAAI,EAC5B,EACA,SAASQ,EAAiBC,EAAeC,EAAoB,CAC3D,IAAIpK,EAAG0J,EACP,GAAI,CAACU,EAAoB,CACvB,IAAIC,EAAU,KACd,IAAKrK,EAAI,EAAGA,EAAImK,EAAc,OAAQ,EAAEnK,EACtC,IAAK0J,EAAI,EAAGA,EAAIS,EAAcnK,CAAC,EAAE,OAAQ,EAAE0J,EACrCS,EAAcnK,CAAC,EAAE0J,CAAC,EAAIW,IACxBA,EAAUF,EAAcnK,CAAC,EAAE0J,CAAC,GAClCU,EAAqChL,EAAO,SAASkL,EAAG,CACtD,OAAOD,EAAUC,CACnB,EAAG,oBAAoB,CACzB,CACA,IAAIlB,EAAc,CAAC,EACnB,IAAKpJ,EAAI,EAAGA,EAAImK,EAAc,OAAQ,EAAEnK,EAAG,CACzC,IAAIkJ,EAAMiB,EAAcnK,CAAC,EAEzB,IADAoJ,EAAYpJ,CAAC,EAAI,CAAC,EACb0J,EAAI,EAAGA,EAAIR,EAAI,OAAQ,EAAEQ,EAC5BN,EAAYpJ,CAAC,EAAE0J,CAAC,EAAIU,EAAmBD,EAAcnK,CAAC,EAAE0J,CAAC,CAAC,CAC9D,CACA,OAAON,CACT,CACAhK,EAAO8K,EAAkB,kBAAkB,EAC3C,SAASK,EAAc1B,EAAQ,CAC7B,IAAI2B,EAAe,CAAC,EAChBxK,EAAG0J,EACP,IAAK1J,EAAI,EAAGA,EAAI6I,EAAO,OAAQ,EAAE7I,EAC/B,IAAK0J,EAAI,EAAGA,EAAIb,EAAO7I,CAAC,EAAE,OAAQ,EAAE0J,EAAG,CACrC,IAAIe,EAAa,OAAO5B,EAAO7I,CAAC,EAAE0J,CAAC,CAAC,EAAE,QAClC,CAACc,EAAad,CAAC,GAAKe,GAAcD,EAAad,CAAC,KAClDc,EAAad,CAAC,EAAIe,EACtB,CAEF,IAAIC,EAAY,GAChB,IAAK1K,EAAI,EAAGA,EAAI6I,EAAO,OAAQ,EAAE7I,EAAG,CAClC,IAAK0J,EAAI,EAAGA,EAAIb,EAAO7I,CAAC,EAAE,OAAQ,EAAE0J,EAAG,CAErC,QADIiB,EAAI,OAAO9B,EAAO7I,CAAC,EAAE0J,CAAC,CAAC,EACpBiB,EAAE,OAASH,EAAad,CAAC,GAC9BiB,EAAI,IAAMA,EACZD,GAAaC,EACTjB,GAAKb,EAAO7I,CAAC,EAAE,OAAS,IAC1B0K,GAAa,IACjB,CACI1K,GAAK6I,EAAO7I,CAAC,EAAE,OAAS,IAC1B0K,GAAa;AAAA,EACjB,CACA,OAAOA,CACT,CACAtL,EAAOmL,EAAe,eAAe,EACrC,SAASK,EAAexB,EAAaxI,EAAS,CAC5C,IAAIiK,EAAI,IAAIjC,EACZ,OAAOiC,EAAE,QAAQzB,EAAaxI,CAAO,CACvC,CACAxB,EAAOwL,EAAgB,gBAAgB,EACvCA,EAAe,QAAU,QACzBA,EAAe,cAAgBL,EAC/BK,EAAe,iBAAmBV,EAClCU,EAAe,QAAUhC,EACrB,OAAOhJ,GAAW,aAAeA,EAAO,UAC1CA,EAAO,QAAUgL,EAErB,CACF,CAAC,EAGGE,EAAkBvL,EAAW,CAC/B,aAAaI,EAAS,CACpB,IAAIoL,EAAoBlI,EAAoB,EACxCmI,EAAcD,EAAkB,YAChCtG,EAASR,EAAmB,EAAE,OAC9BgH,EAAWnK,EAAc,EAAE,SAC3BoK,EAAUzC,EAAgB,EAC1B0C,EAAa,GACbC,EAA8BhM,EAAO,SAASyC,EAAOC,EAAO,CAC9D,IAAIuJ,EAAMJ,EAASpJ,EAAOC,CAAK,EAC3B0F,EAAW,EAAI6D,EACnB,OAAI7D,EAAW,EAAI8D,EAAO,WACxB9D,EAAW8D,EAAO,cAAgB,GAE7B9D,CACT,EAAG,aAAa,EACZ8D,EAAS,CAGX,yBAA0B,EAG1B,SAAU,IAKV,WAAY,GAEZ,aAAcF,EAGd,cAAe,IAGf,kBAAmB,QACrB,EACIG,EAAoC,IAAI,IACxCC,EAAuC,IAAI,IAC3CC,EAAyB,GAC7B9L,EAAQ,gBAAkByL,EAC1BzL,EAAQ,+BAAiC,SAAS+L,EAAuBvI,EAAS,CAChF,IAAIwI,EAAmB,IAAIlH,EAAO,MAAM,KAAK8G,EAAkB,OAAO,CAAC,EAAGD,EAAO,aAAc,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC,EAC/GM,EAA4B,IAAInH,EAAOiH,EAAuBJ,EAAO,aAAc,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC,EAC3G,GAAIC,EAAkB,OAAS,EAC7BG,EAAsB,QAAQ,SAASG,EAAc,CACnD,IAAIC,EAAiB,IAAId,EAAYa,EAAc1I,EAASmI,EAAO,yBAA0BA,EAAO,UAAU,EAC9GC,EAAkB,IAAIO,EAAe,GAAIA,CAAc,EACvDH,EAAiB,OAAOG,CAAc,CACxC,CAAC,MACI,CACL,IAAIC,EAAc,IAAI,MAAML,EAAsB,MAAM,EAExD,GADAK,EAAY,KAAK,EAAK,EAClBL,EAAsB,OAAS,EACjC,GAAIJ,EAAO,oBAAsB,UAAW,CAC1C,IAAIU,EAAiB,MAAM,KAAKT,EAAkB,KAAK,CAAC,EACpDU,EAAa,MAAM,KAAKV,EAAkB,OAAO,CAAC,EAAE,IAAKhI,GAAgB,CAC3E,IAAI2I,EAAoB3I,EAAY,oBAAoB,EACxD,OAAOmI,EAAsB,IAC1BhK,GAAc4J,EAAO,aAAaY,EAAmBxK,CAAS,CACjE,CACF,CAAC,EACD6J,EAAkB,QAAQ,SAAShI,EAAa,CAC9CA,EAAY,cAAc,CAC5B,CAAC,EACD2H,EAAQe,CAAU,EAAE,OAAQpB,GAAMoB,EAAWpB,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,GAAKS,EAAO,aAAa,EAAE,QAAST,GAAM,CAC/F,IAAItH,EAAcgI,EAAkB,IAAIS,EAAenB,EAAE,CAAC,CAAC,CAAC,EACxDsB,EAA+BT,EAAsBb,EAAE,CAAC,CAAC,EAC7DkB,EAAYlB,EAAE,CAAC,CAAC,EAAI,CAAE,UAAWtH,EAAY,SAAU,EACvDA,EAAY,gBAAgB,EAAE,OAAO4I,EAA8BhJ,CAAO,CAC5E,CAAC,EACD4I,EAAY,QAAQ,SAASK,EAASC,EAAO,CAC3C,GAAI,CAACD,GACC,KAAK,IAAI,GAAGH,EAAW,IAAKpB,GAAMA,EAAEwB,CAAK,CAAC,CAAC,EAAIf,EAAO,cAAe,CACvE,IAAIQ,EAAiBd,EAAYU,EAAsBW,CAAK,EAAGlJ,EAASmI,EAAO,yBAA0BA,EAAO,UAAU,EAC1HC,EAAkB,IAAIO,EAAe,GAAIA,CAAc,EACvDA,EAAe,gBAAgB,EAC/BG,EAAW,KAAKP,EAAsB,IACnChK,GAAc4J,EAAO,aAAaQ,EAAgBpK,CAAS,CAC9D,CAAC,CACH,CAEJ,CAAC,CACH,SAAW4J,EAAO,oBAAsB,SACtCC,EAAkB,QAAQ,SAAShI,EAAa,CAC9C,IAAI2I,EAAoB3I,EAAY,oBAAoB,EACxDA,EAAY,cAAc,EAC1B,IAAI+I,EAAmBV,EAA0B,QAAQM,EAAmB,EAAGZ,EAAO,aAAa,EAAE,CAAC,EAClGiB,EAAoCX,EAA0B,QAAQrI,EAAa,EAAG+H,EAAO,aAAa,EAAE,CAAC,EAC7GkB,EAA4BZ,EAA0B,QAAQM,EAAmB,EAAGZ,EAAO,aAAa,EAC5G,GAAIgB,EAAkB,CACpB,IAAIG,EAA8Bf,EAAsB,QAAQY,EAAiB,CAAC,CAAC,EACnF,GAAI,CAACP,EAAYU,CAA2B,EAAG,CAC7CV,EAAYU,CAA2B,EAAI,CACzC,UAAWlJ,EAAY,SACzB,EACA,IAAI4I,EAA+BT,EAAsBe,CAA2B,EACpFlB,EAAkB,IAAIhI,EAAY,EAAE,EAAE,gBAAgB,EAAE,OAAO4I,EAA8BhJ,CAAO,CACtG,CAEF,CACF,CAAC,MAED,MAAM,+BAA+BmI,EAAO,iBAAiB,SAG3DH,GACF,QAAQ,IAAI,6CAA+ChI,CAAO,EAEpEoI,EAAkB,QAAQ,SAAShI,EAAa,CAC9CA,EAAY,cAAc,CAC5B,CAAC,EAEC+H,EAAO,oBAAsB,UAC3BC,EAAkB,KAAO,IAC3BI,EAAmB,IAAIlH,EAAO,MAAM,KAAK8G,EAAkB,OAAO,CAAC,EAAGD,EAAO,aAAc,CAAC,IAAK,IAAK,IAAK,GAAG,CAAC,EAC/GS,EAAY,QAAQ,SAASK,EAASC,EAAO,CAC3C,GAAI,CAACD,EAAS,CACZ,IAAIE,EAAmBX,EAAiB,QAAQD,EAAsBW,CAAK,EAAG,EAAGf,EAAO,aAAa,EAAE,CAAC,EACxG,GAAI,CAACgB,EAAkB,CACrB,IAAIR,EAAiBd,EAAYU,EAAsBW,CAAK,EAAGlJ,EAASmI,EAAO,yBAA0BA,EAAO,UAAU,EAC1HC,EAAkB,IAAIO,EAAe,GAAIA,CAAc,EACvDH,EAAiB,OAAOG,CAAc,EACtCA,EAAe,gBAAgB,CACjC,CAEF,CACF,CAAC,GAGLP,EAAkB,QAAQ,SAAShI,EAAa,CAC1CA,EAAY,YACdA,EAAY,UAAUJ,CAAO,EAC7BI,EAAY,+BAA+B,EACvCA,EAAY,OAAO,IACrBgI,EAAkB,OAAOhI,EAAY,EAAE,EACvCoI,EAAiB,OAAOpI,CAAW,EAC/BkI,GACFD,EAAqB,IAAIjI,EAAY,GAAIA,CAAW,GAI5D,CAAC,CACH,CACF,EACA5D,EAAQ,MAAQ,UAAW,CACzB4L,EAAoC,IAAI,IACxCC,EAAuC,IAAI,IAC3CT,EAAkB,MAAM,CAC1B,EACApL,EAAQ,UAAY,SAAS+M,EAAW,CACtC,OAAO,KAAKA,CAAS,EAAE,QAASC,GAAQ,CACtCrB,EAAOqB,CAAG,EAAID,EAAUC,CAAG,CAC7B,CAAC,CACH,EACAhN,EAAQ,mBAAqB,UAAW,CACtC8L,EAAyB,EAC3B,EACA9L,EAAQ,oBAAsB,UAAW,CACvC8L,EAAyB,EAC3B,EACA9L,EAAQ,sBAAwB,SAASoE,EAAW,GAAM,CACxD,OAAO,MAAM,KAAKwH,EAAkB,OAAO,CAAC,EAAE,IAAI,SAAShI,EAAa,CACtE,OAAOA,EAAY,OAAOQ,CAAQ,CACpC,CAAC,CACH,EACApE,EAAQ,2BAA6B,SAASoE,EAAW,GAAM,CAC7D,OAAO,MAAM,KAAKwH,EAAkB,OAAO,CAAC,EAAE,IAAI,SAAShI,EAAa,CACtE,OAAOA,EAAY,YAAYQ,CAAQ,CACzC,CAAC,CACH,EACApE,EAAQ,2BAA6B,SAASwD,EAAS,CACrD,OAAO,MAAM,KAAKoI,EAAkB,OAAO,CAAC,EAAE,IAAI,SAAShI,EAAa,CACtE,OAAOA,EAAY,MAAMJ,CAAO,CAClC,CAAC,CACH,EACAxD,EAAQ,mBAAqB,UAAW,CACtC,OAAO6L,CACT,EACA7L,EAAQ,yBAA2B,UAAW,CAC5C,OAAO,MAAM,KAAK6L,EAAqB,OAAO,CAAC,EAAE,IAAI,SAASjI,EAAa,CACzE,OAAOA,EAAY,kBAAkB,CACvC,CAAC,CACH,CACF,CACF,CAAC,EACMqJ,EAAQ9B,EAAgB,ED1pC/B,IAAM+B,EAAiC,CACrC,MAAO,GACP,QAAS,QAIT,cAAe,mDACf,OAAQ,CAAE,QAAS,GAAM,aAAc,GAAO,KAAM,EAAM,EAC1D,KAAM,CACJ,QAAS,GACT,SAAU,CAAE,SAAU,GAAO,YAAa,GAAI,cAAe,EAAI,EACjE,KAAM,CAAE,QAAS,EAAK,EACtB,UAAW,CAAE,QAAS,EAAM,EAC5B,KAAM,CAAE,QAAS,EAAM,EACvB,YAAa,CAAE,QAAS,EAAM,EAC9B,QAAS,CAAE,QAAS,EAAM,EAC1B,UAAW,CAAE,QAAS,EAAM,EAC5B,SAAU,CAAE,QAAS,EAAM,CAC7B,EACA,KAAM,CAAE,QAAS,GAAO,YAAa,EAAG,UAAW,wBAAyB,EAC5E,KAAM,CAAE,QAAS,EAAM,EACvB,OAAQ,CAAE,QAAS,GAAO,YAAa,EAAG,EAC1C,aAAc,CAAE,QAAS,EAAM,EAC/B,QAAS,CAAE,QAAS,EAAM,CAC5B,EAsBMC,EAA+B,CACnC,yBAA0B,IAC1B,SAAU,IACV,WAAY,GACZ,cAAe,IACf,kBAAmB,QACrB,EAEMC,EAAQ,IAAM,QAAMF,CAAW,EAE/BG,EAAM,CACV,MAAO,SAAS,eAAe,OAAO,EACtC,OAAQ,SAAS,eAAe,QAAQ,EACxC,IAAK,SAAS,eAAe,KAAK,EAClC,IAAK,SAAS,eAAe,QAAQ,EACrC,QAAS,SAAS,eAAe,SAAS,EAC1C,cAAe,SAAS,eAAe,eAAe,EACtD,OAAQ,SAAS,eAAe,QAAQ,EACxC,IAAM,SAAS,eAAe,QAAQ,EAAwB,WAAW,IAAI,CAC/E,EACMC,EAAY,CAAE,OAAQ,EAAG,KAAM,EAAG,QAAS,EAAG,MAAO,CAAE,EACvDC,EAAM,CAAE,UAAW,EAAG,QAAS,EAAG,OAAQ,EAAG,UAAW,CAAE,EAE1DC,EAAM,IAAIC,IAAQ,CACtBJ,EAAI,IAAI,WAAaI,EAAI,KAAK,GAAG,EAAI;AAAA,EACrC,QAAQ,IAAI,GAAGA,CAAG,CACpB,EACMC,EAAUD,GAAQJ,EAAI,IAAI,UAAYI,EAE5C,eAAeE,GAAgB,CAC7B,GAAI,CAACN,EAAI,MAAM,QAAUA,EAAI,MAAM,YAAc,EAAG,CAC9CC,EAAU,QAAU,IAAGA,EAAU,MAAQF,EAAM,IAAI,GAEvD,MAAMA,EAAM,OAAOC,EAAI,MAAOH,CAAW,EACzC,IAAMU,EAAUR,EAAM,GAAG,OAAO,EAAE,WAC9BQ,EAAUN,EAAU,UAAY,GAAGE,EAAI,qBAAsBI,EAAUN,EAAU,OAAO,EAC5FA,EAAU,QAAUM,EACpBL,EAAI,UAAY,KAAK,MAAM,IAAO,KAAQH,EAAM,IAAI,EAAIE,EAAU,OAAO,EAAI,IAC7EC,EAAI,SACJA,EAAI,UAAY,KAAK,MAAM,KAAQH,EAAM,IAAI,EAAIE,EAAU,OAASC,EAAI,MAAM,EAAI,GACpF,CACAD,EAAU,OAASF,EAAM,IAAI,EAC7B,sBAAsBO,CAAa,CACrC,CAEA,SAASE,GAAW,CAClB,GAAI,CAACR,EAAI,MAAM,QAAUA,EAAI,MAAM,YAAc,EAAG,CAClD,IAAMS,EAAgBT,EAAI,cAAc,QAAUD,EAAM,KAAKA,EAAM,MAAM,EAAIA,EAAM,OAC/EW,EAA+D,CAAC,EAChEX,EAAM,OAAO,KAAK,QAASW,EAAWD,EAAI,KACrCV,EAAM,OAAO,KAAK,QAASW,EAAWD,EAAI,KAC1CV,EAAM,OAAO,OAAO,QAASW,EAAWD,EAAI,OAChDN,EAAI,qBAAqB,EAC9B,IAAIQ,EAAwB,CAAC,EAC7B,GAAIX,EAAI,QAAQ,QAAS,CACvB,IAAMY,EAAQF,EAAS,IAAKG,IAAS,CACnC,EAAGA,EAAI,IAAI,CAAC,EAAIA,EAAI,IAAI,CAAC,EAAI,EAC7B,EAAGA,EAAI,IAAI,CAAC,EAAIA,EAAI,IAAI,CAAC,EAAI,EAC7B,EAAGA,EAAI,IAAI,CAAC,EACZ,EAAGA,EAAI,IAAI,CAAC,EACZ,KAAMA,EAAI,QAAUd,EAAM,OAAO,KAAK,QAAU,OAAS,QACzD,WAAYc,EAAI,KAClB,EAAE,EACFC,EAAQ,+BAA+BF,EAAOV,EAAI,MAAM,EACxDS,EAAOG,EAAQ,sBAAsB,EAAI,CAC3C,CACAf,EAAM,KAAK,OAAOC,EAAI,MAAOA,EAAI,MAAM,EACvC,QAASe,EAAI,EAAGA,EAAIL,EAAS,OAAQK,IAAK,CAExC,IAAMC,EAAON,EAASK,CAAC,EAAE,QAAUhB,EAAM,OAAO,KAAK,QAAU,OAAS,QACxEC,EAAI,IAAI,WAAWU,EAASK,CAAC,EAAE,IAAI,CAAC,EAAGL,EAASK,CAAC,EAAE,IAAI,CAAC,EAAGL,EAASK,CAAC,EAAE,IAAI,CAAC,EAAGL,EAASK,CAAC,EAAE,IAAI,CAAC,CAAC,EACjGf,EAAI,IAAI,SAAS,OAAOU,EAASK,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,IAAML,EAASK,CAAC,EAAE,KAAK,CAAC,KAAKC,CAAI,GAAIN,EAASK,CAAC,EAAE,IAAI,CAAC,EAAI,EAAGL,EAASK,CAAC,EAAE,IAAI,CAAC,EAAI,EAAE,EACrIJ,EAAKI,CAAC,GACRf,EAAI,IAAI,SAAS,MAAMW,EAAKI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,IAAMJ,EAAKI,CAAC,EAAE,UAAU,CAAC,KAAKJ,EAAKI,CAAC,EAAE,IAAI,IAAIJ,EAAKI,CAAC,EAAE,SAAW,SAAW,EAAE,GAAIL,EAASK,CAAC,EAAE,IAAI,CAAC,EAAI,EAAGL,EAASK,CAAC,EAAE,IAAI,CAAC,EAAI,EAAE,CAErL,CACF,CACA,IAAME,EAAMlB,EAAM,IAAI,EACtBG,EAAI,QAAU,KAAK,MAAM,IAAO,KAAQe,EAAMhB,EAAU,KAAK,EAAI,IACjEA,EAAU,KAAOgB,EACjBZ,EAAOL,EAAI,MAAM,OAAS,SAAW,QAAQE,EAAI,UAAU,QAAQ,CAAC,EAAE,SAAS,EAAG,GAAG,CAAC,aAAaA,EAAI,QAAQ,QAAQ,CAAC,EAAE,SAAS,EAAG,GAAG,CAAC,OAAO,EACjJ,WAAWM,EAAU,EAAE,CACzB,CAEA,eAAeU,EAAYC,EAAY,CACrC,IAAMC,EAAM,IAAI,gBAAgBD,CAAI,EACpCnB,EAAI,MAAM,IAAMoB,EAChB,MAAMpB,EAAI,MAAM,KAAK,EACrBG,EAAI,gBAAiBgB,EAAK,KAAM,cAAe,CAACnB,EAAI,MAAM,WAAYA,EAAI,MAAM,WAAW,EAAG,YAAaA,EAAI,MAAM,QAAQ,EAC7HA,EAAI,OAAO,MAAQA,EAAI,MAAM,WAC7BA,EAAI,OAAO,OAASA,EAAI,MAAM,YAC9BA,EAAI,IAAI,YAAc,QACtBA,EAAI,IAAI,UAAY,QACpBA,EAAI,IAAI,KAAO,gBACfA,EAAI,MAAM,aAAe,GAC3B,CAEA,SAASqB,IAAY,CACnB,SAAS,KAAK,iBAAiB,YAAcC,GAAQA,EAAI,eAAe,CAAC,EACzE,SAAS,KAAK,iBAAiB,YAAcA,GAAQA,EAAI,eAAe,CAAC,EACzE,SAAS,KAAK,iBAAiB,WAAaA,GAAQA,EAAI,eAAe,CAAC,EACxE,SAAS,KAAK,iBAAiB,OAAQ,MAAOA,GAAQ,CA/JxD,IAAAC,EAAAC,EAgKIF,EAAI,eAAe,EACfA,EAAI,eAAcA,EAAI,aAAa,WAAa,QACpD,IAAMH,GAAOK,GAAAD,EAAAD,EAAI,eAAJ,YAAAC,EAAkB,QAAlB,YAAAC,EAA0B,GACnCL,GAAM,MAAMD,EAAYC,CAAI,EAChChB,EAAIH,EAAI,MAAM,UAAU,CAC1B,CAAC,EACA,SAAS,eAAe,YAAY,EAAuB,SAAW,MAAOsB,GAAQ,CAtKxF,IAAAC,EAAAC,EAuKIF,EAAI,eAAe,EACnB,IAAMH,GAAOK,GAAAD,EAAAD,EAAI,SAAJ,YAAAC,EAAa,QAAb,YAAAC,EAAwB,GACjCL,GAAM,MAAMD,EAAYC,CAAI,CAClC,EACAnB,EAAI,OAAO,SAAW,IAAM,CAC1BF,EAAc,cAAiB,SAAS,eAAe,eAAe,EAAuB,cAC7FA,EAAc,SAAY,SAAS,eAAe,UAAU,EAAuB,cACnFA,EAAc,yBAA4B,SAAS,eAAe,0BAA0B,EAAuB,cACnHA,EAAc,yBAA4B,SAAS,eAAe,0BAA0B,EAAuB,cACnHA,EAAc,kBAAqB,SAAS,eAAe,0BAA0B,EAAuB,QAAU,SAAW,UACjIgB,EAAQ,UAAUhB,CAAa,EAC1B,SAAS,eAAe,cAAc,EAAuB,QAASgB,EAAQ,mBAAmB,EACjGA,EAAQ,oBAAoB,EACjCA,EAAQ,MAAM,EACdX,EAAI,wBAAyB,KAAK,UAAUL,CAAa,CAAC,EAC1DD,EAAY,KAAM,QAAW,SAAS,eAAe,UAAU,EAAuB,QACtFA,EAAY,KAAM,QAAW,SAAS,eAAe,UAAU,EAAuB,QACtFA,EAAY,OAAQ,QAAW,SAAS,eAAe,YAAY,EAAuB,OAC5F,EACAG,EAAI,QAAQ,SAAYsB,GAAQ,CAC9BnB,EAAI,UAAYmB,EAAI,OAA4B,QAAU,UAAY,UAAU,EAChFR,EAAQ,UAAUhB,CAAa,EAC/BgB,EAAQ,MAAM,CAChB,CACF,CAEA,eAAeW,IAAO,CACpBtB,EAAI,iBAAkBJ,EAAM,QAAS,kBAAmBA,EAAM,GAAG,QAAQ,WAAW,CAAC,EACrFI,EAAI,YAAaJ,EAAM,IAAI,SAAU,WAAYA,EAAM,IAAI,KAAK,EAChEM,EAAO,YAAY,EACnB,MAAMN,EAAM,KAAK,EACjBI,EAAI,WAAYJ,EAAM,GAAG,WAAW,EAAG,eAAgBA,EAAM,IAAI,QAAQ,EACzEI,EAAI,iBAAkBJ,EAAM,OAAO,OAAO,CAAC,EAC3CM,EAAO,iBAAiB,EACxB,MAAMN,EAAM,OAAO,EACnBsB,GAAU,EACV,MAAMf,EAAc,EACpBE,EAAS,CACX,CAEA,OAAO,OAASiB", "names": ["H", "__defProp", "__getOwnPropNames", "__name", "target", "value", "__commonJS", "cb", "mod", "require_rng_browser", "exports", "module", "getRandomValues", "rnds8", "rnds", "i", "r", "require_bytesToUuid", "byteToHex", "bytesToUuid", "buf", "offset", "i2", "bth", "require_v4", "rng", "v4", "options", "ii", "require_utils", "detections", "largestAllowed", "isInsideArea", "area", "point", "xMin", "xMax", "yMin", "yMax", "areas", "objectsToDetect", "detection", "getRectangleEdges", "item", "item1", "item2", "rect1", "rect2", "overlap_x0", "overlap_y0", "overlap_x1", "overlap_y1", "area_rect1", "area_rect2", "area_intersection", "area_union", "nbFrame", "dx", "dy", "angle", "require_ItemTracked", "uuidv4", "computeBearingIn360", "computeVelocityVector", "idDisplay", "properties", "frameNb", "unMatchedFramesTolerance", "fastDelete", "DEFAULT_UNMATCHEDFRAMES_TOLERANCE", "itemTracked", "properties2", "frameNb2", "start", "end", "nameMostlyMatchedOccurences", "nameMostlyMatched", "name", "roundInt", "frameIndex", "require_kdTree_min", "root", "factory", "exports2", "Node", "obj", "dimension", "parent", "kdTree", "points", "metric", "dimensions", "self", "buildTree", "points2", "depth", "dim", "median", "node", "a", "b", "loadTree", "data", "restoreParent", "src", "dest", "innerSearch", "dimension2", "insertPosition", "newNode", "nodeSearch", "node2", "removeNode", "nextNode", "nextObj", "pDimension", "findMin", "node3", "own", "left", "right", "min", "maxNodes", "maxDistance", "result", "bestNodes", "BinaryHeap", "e", "nearestSearch", "<PERSON><PERSON><PERSON><PERSON>", "ownDistance", "linearPoint", "linearDistance", "<PERSON><PERSON><PERSON><PERSON>", "saveNode", "distance", "height", "count", "scoreFunction", "element", "len", "n", "parentN", "length", "elemScore", "child2N", "child1N", "swap", "child1", "child1Score", "child2", "child2Score", "require_munkres", "MAX_SIZE", "DEFAULT_PAD_VALUE", "<PERSON><PERSON><PERSON>", "matrix", "pad_value", "max_columns", "total_rows", "new_matrix", "row", "new_row", "cost_matrix", "nfalseArray", "step", "steps", "func", "results", "j", "val", "min<PERSON>", "done", "col", "star_col", "z", "path", "make_cost_matrix", "profit_matrix", "inversion_function", "maximum", "x", "format_matrix", "columnWidths", "entryWidth", "formatted", "s", "computeMunkres", "m", "require_tracker", "itemTrackedModule", "ItemTracked", "iou<PERSON><PERSON><PERSON>", "munkres", "DEBUG_MODE", "iouDistance", "iou", "params", "mapOfItemsTracked", "mapOfAllItemsTracked", "keepAllHistoryInMemory", "detectionsOfThisFrame", "treeItemsTracked", "treeDetectionsOfThisFrame", "itemDetected", "newItemTracked", "matchedList", "trackedItemIds", "costMatrix", "predictedPosition", "updatedTrackedItemProperties", "matched", "index", "treeSearchResult", "treeSearchResultWithoutPrediction", "treeSearchMultipleResults", "indexClosestNewDetectedItem", "newParams", "key", "tracker_default", "humanConfig", "trackerConfig", "human", "dom", "timestamp", "fps", "log", "msg", "status", "detectionLoop", "tensors", "drawLoop", "res", "tracking", "data", "items", "obj", "tracker_default", "i", "name", "now", "handleVideo", "file", "url", "initInput", "evt", "_a", "_b", "main"]}