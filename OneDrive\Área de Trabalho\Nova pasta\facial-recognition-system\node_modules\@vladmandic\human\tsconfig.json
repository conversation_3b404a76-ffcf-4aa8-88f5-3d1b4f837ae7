{"compilerOptions": {"module": "ESNext", "target": "ESNext", "moduleResolution": "node", "outDir": "types", "baseUrl": "./", "paths": {"tslib": ["./node_modules/tslib/tslib.d.ts"]}, "lib": ["esnext", "dom"], "allowJs": true, "allowSyntheticDefaultImports": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "alwaysStrict": true, "declaration": true, "declarationMap": true, "emitDecoratorMetadata": true, "esModuleInterop": false, "exactOptionalPropertyTypes": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "importHelpers": true, "isolatedModules": false, "noEmitHelpers": true, "noEmitOnError": false, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitThis": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "noUnusedLocals": true, "noUnusedParameters": true, "preserveConstEnums": true, "pretty": true, "removeComments": false, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strictBindCallApply": true, "strictFunctionTypes": true, "strictNullChecks": true, "strictPropertyInitialization": true, "stripInternal": false, "useDefineForClassFields": true, "useUnknownInCatchVariables": true}, "formatCodeOptions": {"indentSize": 2, "tabSize": 2}, "exclude": ["node_modules/", "types/", "dist/**/*.js"], "include": ["src", "tfjs/*.ts", "types/human.d.ts", "test/**/*.ts"], "typedocOptions": {"excludeExternals": true, "externalPattern": ["**/node_modules/**", "tfjs/", "demo/"]}}