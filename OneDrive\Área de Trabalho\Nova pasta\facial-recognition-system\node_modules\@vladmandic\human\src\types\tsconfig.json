{"compilerOptions": {"module": "esnext", "target": "esnext", "moduleResolution": "node", "baseUrl": "./", "lib": ["esnext", "dom"], "allowJs": true, "allowSyntheticDefaultImports": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "alwaysStrict": true, "declaration": true, "declarationMap": true, "emitDecoratorMetadata": true, "esModuleInterop": false, "exactOptionalPropertyTypes": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "importHelpers": true, "isolatedModules": false, "noEmitHelpers": true, "noEmitOnError": false, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitThis": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "noUnusedLocals": true, "noUnusedParameters": true, "preserveConstEnums": true, "pretty": true, "removeComments": false, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strictBindCallApply": true, "strictFunctionTypes": true, "strictNullChecks": true, "strictPropertyInitialization": true, "stripInternal": false, "useDefineForClassFields": true, "useUnknownInCatchVariables": true}, "exclude": ["node_modules/", "dist/**/*.js"]}