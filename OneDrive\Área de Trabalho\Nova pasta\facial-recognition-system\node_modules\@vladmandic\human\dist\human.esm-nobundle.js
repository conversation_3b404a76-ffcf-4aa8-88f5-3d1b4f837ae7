/*
  Human
  homepage: <https://github.com/vladmandic/human>
  author: <https://github.com/vladmandic>'
*/

var at=Object.defineProperty;var _n=Object.getOwnPropertyDescriptor;var $n=Object.getOwnPropertyNames;var eo=Object.prototype.hasOwnProperty;var q5=e=>{throw TypeError(e)};var to=(e,t,n)=>t in e?at(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var re=(e,t)=>{for(var n in t)at(e,n,{get:t[n],enumerable:!0})},X5=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of $n(t))!eo.call(e,s)&&s!==n&&at(e,s,{get:()=>t[s],enumerable:!(o=_n(t,s))||o.enumerable});return e},E=(e,t,n)=>(X5(e,t,"default"),n&&X5(n,t,"default"));var z=(e,t,n)=>to(e,typeof t!="symbol"?t+"":t,n),U5=(e,t,n)=>t.has(e)||q5("Cannot "+n);var E0=(e,t,n)=>(U5(e,t,"read from private field"),n?n.call(e):t.get(e)),q0=(e,t,n)=>t.has(e)?q5("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),_0=(e,t,n,o)=>(U5(e,t,"write to private field"),o?o.call(e,n):t.set(e,n),n);var r={};re(r,{node:()=>it,version:()=>Je});E(r,MA);E(r,PA);E(r,kA);E(r,wA);E(r,EA);E(r,zA);import*as MA from"@tensorflow/tfjs-core/dist/index.js";import*as PA from"@tensorflow/tfjs-converter/dist/index.js";import*as kA from"@tensorflow/tfjs-backend-cpu/dist/index.js";import*as wA from"@tensorflow/tfjs-backend-webgl/dist/index.js";import*as EA from"@tensorflow/tfjs-backend-wasm/dist/index.js";import*as zA from"@tensorflow/tfjs-backend-webgpu/dist/index.js";var Y5="4.22.0",no="4.22.0",oo="4.22.0",ro="4.22.0",so="4.22.0",Ao="4.22.0",Je={tfjs:Y5,"tfjs-core":Y5,"tfjs-converter":no,"tfjs-backend-cpu":oo,"tfjs-backend-webgl":ro,"tfjs-backend-wasm":so,"tfjs-backend-webgpu":Ao},it=void 0;function g(...e){let t=new Date,n=`${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}:${t.getSeconds().toString().padStart(2,"0")}.${t.getMilliseconds().toString().padStart(3,"0")}`;e&&console.log(n,"Human:",...e)}function K5(e,t){let n=e.endsWith("/")?"":"/",s=t.startsWith(".")||t.startsWith("/")||t.startsWith("http:")||t.startsWith("https:")||t.startsWith("file:")?`${t}`:`${e}${n}${t}`;if(!s.toLocaleLowerCase().includes(".json"))throw new Error(`modelpath error: expecting json file: ${s}`);return s}var R=()=>typeof performance!="undefined"?performance.now():parseInt((Number(process.hrtime.bigint())/1e3/1e3).toString());function lt(e,t,n="config",o=[]){for(let s of Object.keys(t))if(typeof t[s]=="object")lt(e[s],t[s],s,o);else{let A=e&&typeof e[s]!="undefined";A||o.push({reason:"unknown property",where:`${n}.${s} = ${t[s]}`});let a=e&&typeof e[s]==typeof t[s];A&&!a&&o.push({reason:"property type mismatch",where:`${n}.${s} = ${t[s]}`,expected:typeof e[s]})}return t.debug&&n==="config"&&o.length>0&&g("invalid configuration",o),o}function Q(...e){let t=n=>n&&typeof n=="object";return e.reduce((n,o)=>(Object.keys(o||{}).forEach(s=>{let A=n[s],a=o[s];Array.isArray(A)&&Array.isArray(a)?n[s]=A.concat(...a):t(A)&&t(a)?n[s]=Q(A,a):n[s]=a}),n),{})}var Ce={backend:"",modelBasePath:"",cacheModels:!0,validateModels:!0,wasmPath:"",wasmPlatformFetch:!1,debug:!1,async:!0,warmup:"full",cacheSensitivity:.7,skipAllowed:!1,deallocate:!1,flags:{},softwareKernels:!1,filter:{enabled:!0,equalization:!1,width:0,height:0,flip:!1,return:!0,autoBrightness:!0,brightness:0,contrast:0,sharpness:0,blur:0,saturation:0,hue:0,negative:!1,sepia:!1,vintage:!1,kodachrome:!1,technicolor:!1,polaroid:!1,pixelate:0},gesture:{enabled:!0},face:{enabled:!0,detector:{modelPath:"blazeface.json",rotation:!1,maxDetected:1,skipFrames:99,skipTime:2500,minConfidence:.2,minSize:0,iouThreshold:.1,scale:1.4,mask:!1,return:!1},mesh:{enabled:!0,modelPath:"facemesh.json",keepInvalid:!1},attention:{enabled:!1,modelPath:"facemesh-attention.json"},iris:{enabled:!0,scale:2.3,modelPath:"iris.json"},emotion:{enabled:!0,minConfidence:.1,skipFrames:99,skipTime:1500,modelPath:"emotion.json"},description:{enabled:!0,modelPath:"faceres.json",skipFrames:99,skipTime:3e3,minConfidence:.1},antispoof:{enabled:!1,skipFrames:99,skipTime:4e3,modelPath:"antispoof.json"},liveness:{enabled:!1,skipFrames:99,skipTime:4e3,modelPath:"liveness.json"}},body:{enabled:!0,modelPath:"movenet-lightning.json",maxDetected:-1,minConfidence:.3,skipFrames:1,skipTime:200},hand:{enabled:!0,rotation:!0,skipFrames:99,skipTime:1e3,minConfidence:.5,iouThreshold:.2,maxDetected:-1,landmarks:!0,detector:{modelPath:"handtrack.json"},skeleton:{modelPath:"handlandmark-lite.json"}},object:{enabled:!1,modelPath:"centernet.json",minConfidence:.2,iouThreshold:.4,maxDetected:10,skipFrames:99,skipTime:2e3},segmentation:{enabled:!1,modelPath:"rvm.json",ratio:.5,mode:"default"}};var J5=`
  precision highp float;
  attribute vec2 pos;
  attribute vec2 uv;
  varying vec2 vUv;
  uniform float flipY;
  void main(void) {
    vUv = uv;
    gl_Position = vec4(pos.x, pos.y*flipY, 0.0, 1.);
  }
`;var Q5=`
  precision highp float;
  varying vec2 vUv;
  uniform sampler2D texture;
  uniform float m[20];
  void main(void) {
    vec4 c = texture2D(texture, vUv);
    gl_FragColor.r = m[0] * c.r + m[1] * c.g + m[2] * c.b + m[3] * c.a + m[4];
    gl_FragColor.g = m[5] * c.r + m[6] * c.g + m[7] * c.b + m[8] * c.a + m[9];
    gl_FragColor.b = m[10] * c.r + m[11] * c.g + m[12] * c.b + m[13] * c.a + m[14];
    gl_FragColor.a = m[15] * c.r + m[16] * c.g + m[17] * c.b + m[18] * c.a + m[19];
  }
`,_5=`
  precision highp float;
  varying vec2 vUv;
  uniform sampler2D texture;
  uniform float m[20];
  void main(void) {
    vec4 c = texture2D(texture, vUv);
    gl_FragColor.r = m[0] * c.r + m[1] * c.g + m[2] * c.b + m[4];
    gl_FragColor.g = m[5] * c.r + m[6] * c.g + m[7] * c.b + m[9];
    gl_FragColor.b = m[10] * c.r + m[11] * c.g + m[12] * c.b + m[14];
    gl_FragColor.a = c.a;
  }
`,$5=`
  precision highp float;
  varying vec2 vUv;
  uniform vec2 size;
  uniform sampler2D texture;
  vec2 pixelate(vec2 coord, vec2 size) {
    return floor( coord / size ) * size;
  }
  void main(void) {
    gl_FragColor = vec4(0.0);
    vec2 coord = pixelate(vUv, size);
    gl_FragColor += texture2D(texture, coord);
  }
`,e1=`
  precision highp float;
  varying vec2 vUv;
  uniform sampler2D texture;
  uniform vec2 px;
  void main(void) {
    gl_FragColor = vec4(0.0);
    gl_FragColor += texture2D(texture, vUv + vec2(-7.0*px.x, -7.0*px.y))*0.0044299121055113265;
    gl_FragColor += texture2D(texture, vUv + vec2(-6.0*px.x, -6.0*px.y))*0.00895781211794;
    gl_FragColor += texture2D(texture, vUv + vec2(-5.0*px.x, -5.0*px.y))*0.0215963866053;
    gl_FragColor += texture2D(texture, vUv + vec2(-4.0*px.x, -4.0*px.y))*0.0443683338718;
    gl_FragColor += texture2D(texture, vUv + vec2(-3.0*px.x, -3.0*px.y))*0.0776744219933;
    gl_FragColor += texture2D(texture, vUv + vec2(-2.0*px.x, -2.0*px.y))*0.115876621105;
    gl_FragColor += texture2D(texture, vUv + vec2(-1.0*px.x, -1.0*px.y))*0.147308056121;
    gl_FragColor += texture2D(texture, vUv                             )*0.159576912161;
    gl_FragColor += texture2D(texture, vUv + vec2( 1.0*px.x,  1.0*px.y))*0.147308056121;
    gl_FragColor += texture2D(texture, vUv + vec2( 2.0*px.x,  2.0*px.y))*0.115876621105;
    gl_FragColor += texture2D(texture, vUv + vec2( 3.0*px.x,  3.0*px.y))*0.0776744219933;
    gl_FragColor += texture2D(texture, vUv + vec2( 4.0*px.x,  4.0*px.y))*0.0443683338718;
    gl_FragColor += texture2D(texture, vUv + vec2( 5.0*px.x,  5.0*px.y))*0.0215963866053;
    gl_FragColor += texture2D(texture, vUv + vec2( 6.0*px.x,  6.0*px.y))*0.00895781211794;
    gl_FragColor += texture2D(texture, vUv + vec2( 7.0*px.x,  7.0*px.y))*0.0044299121055113265;
  }
`,t1=`
  precision highp float;
  varying vec2 vUv;
  uniform sampler2D texture;
  uniform vec2 px;
  uniform float m[9];
  void main(void) {
    vec4 c11 = texture2D(texture, vUv - px); // top left
    vec4 c12 = texture2D(texture, vec2(vUv.x, vUv.y - px.y)); // top center
    vec4 c13 = texture2D(texture, vec2(vUv.x + px.x, vUv.y - px.y)); // top right
    vec4 c21 = texture2D(texture, vec2(vUv.x - px.x, vUv.y) ); // mid left
    vec4 c22 = texture2D(texture, vUv); // mid center
    vec4 c23 = texture2D(texture, vec2(vUv.x + px.x, vUv.y) ); // mid right
    vec4 c31 = texture2D(texture, vec2(vUv.x - px.x, vUv.y + px.y) ); // bottom left
    vec4 c32 = texture2D(texture, vec2(vUv.x, vUv.y + px.y) ); // bottom center
    vec4 c33 = texture2D(texture, vUv + px ); // bottom right
    gl_FragColor = 
    c11 * m[0] + c12 * m[1] + c22 * m[2] +
    c21 * m[3] + c22 * m[4] + c23 * m[5] +
    c31 * m[6] + c32 * m[7] + c33 * m[8];
    gl_FragColor.a = c22.a;
  }
`;var ct=(e,t,n)=>{let o=new RegExp("\\b"+t+" \\w+ (\\w+)","ig");e.replace(o,(s,A)=>(n[A]=0,s))},dt=class{constructor(t,n,o){z(this,"uniform",{});z(this,"attribute",{});z(this,"gl");z(this,"id");z(this,"compile",(t,n)=>{let o=this.gl.createShader(n);return o?(this.gl.shaderSource(o,t),this.gl.compileShader(o),this.gl.getShaderParameter(o,this.gl.COMPILE_STATUS)?o:(g(`filter: gl compile failed: ${this.gl.getShaderInfoLog(o)||"unknown"}`),null)):(g("filter: could not create shader"),null)});this.gl=t;let s=this.compile(n,this.gl.VERTEX_SHADER),A=this.compile(o,this.gl.FRAGMENT_SHADER);if(this.id=this.gl.createProgram(),!(!s||!A)){if(!this.id){g("filter: could not create webgl program");return}if(this.gl.attachShader(this.id,s),this.gl.attachShader(this.id,A),this.gl.linkProgram(this.id),!this.gl.getProgramParameter(this.id,this.gl.LINK_STATUS)){g(`filter: gl link failed: ${this.gl.getProgramInfoLog(this.id)||"unknown"}`);return}this.gl.useProgram(this.id),ct(n,"attribute",this.attribute);for(let a in this.attribute)this.attribute[a]=this.gl.getAttribLocation(this.id,a);ct(n,"uniform",this.uniform),ct(o,"uniform",this.uniform);for(let a in this.uniform)this.uniform[a]=this.gl.getUniformLocation(this.id,a)}}};function n1(){let e=0,t=null,n=!1,o=-1,s=[null,null],A=[],a=null,i=null,c=D0(100,100),d={},x={INTERMEDIATE:1},l=c.getContext("webgl");if(!l){g("filter: cannot get webgl context");return}this.gl=l;function f(T,u){if(!(T===c.width&&u===c.height)){if(c.width=T,c.height=u,!a){let h=new Float32Array([-1,-1,0,1,1,-1,1,1,-1,1,0,0,-1,1,0,0,1,-1,1,1,1,1,1,0]);a=l.createBuffer(),l.bindBuffer(l.ARRAY_BUFFER,a),l.bufferData(l.ARRAY_BUFFER,h,l.STATIC_DRAW),l.pixelStorei(l.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0)}l.viewport(0,0,c.width,c.height),s=[null,null]}}function y(T,u){let h=l.createFramebuffer();l.bindFramebuffer(l.FRAMEBUFFER,h);let w=l.createRenderbuffer();l.bindRenderbuffer(l.RENDERBUFFER,w);let k=l.createTexture();return l.bindTexture(l.TEXTURE_2D,k),l.texImage2D(l.TEXTURE_2D,0,l.RGBA,T,u,0,l.RGBA,l.UNSIGNED_BYTE,null),l.texParameteri(l.TEXTURE_2D,l.TEXTURE_MAG_FILTER,l.LINEAR),l.texParameteri(l.TEXTURE_2D,l.TEXTURE_MIN_FILTER,l.LINEAR),l.texParameteri(l.TEXTURE_2D,l.TEXTURE_WRAP_S,l.CLAMP_TO_EDGE),l.texParameteri(l.TEXTURE_2D,l.TEXTURE_WRAP_T,l.CLAMP_TO_EDGE),l.framebufferTexture2D(l.FRAMEBUFFER,l.COLOR_ATTACHMENT0,l.TEXTURE_2D,k,0),l.bindTexture(l.TEXTURE_2D,null),l.bindFramebuffer(l.FRAMEBUFFER,null),{fbo:h,texture:k}}function p(T){return s[T]=s[T]||y(c.width,c.height),s[T]}function m(T=0){if(!i)return;let u=null,h=null,w=!1;e===0?u=t:u=p(o).texture||null,e++,n&&!(T&x.INTERMEDIATE)?(h=null,w=e%2===0):(o=(o+1)%2,h=p(o).fbo||null),l.bindTexture(l.TEXTURE_2D,u),l.bindFramebuffer(l.FRAMEBUFFER,h),l.uniform1f(i.uniform.flipY,w?-1:1),l.drawArrays(l.TRIANGLES,0,6)}function b(T){if(d[T])return i=d[T],l.useProgram((i?i.id:null)||null),i;if(i=new dt(l,J5,T),!i)return g("filter: could not get webgl program"),null;let u=Float32Array.BYTES_PER_ELEMENT,h=4*u;return l.enableVertexAttribArray(i.attribute.pos),l.vertexAttribPointer(i.attribute.pos,2,l.FLOAT,!1,h,0*u),l.enableVertexAttribArray(i.attribute.uv),l.vertexAttribPointer(i.attribute.uv,2,l.FLOAT,!1,h,2*u),d[T]=i,i}let v={colorMatrix:T=>{let u=new Float32Array(T);u[4]/=255,u[9]/=255,u[14]/=255,u[19]/=255;let h=u[18]===1&&u[3]===0&&u[8]===0&&u[13]===0&&u[15]===0&&u[16]===0&&u[17]===0&&u[19]===0?_5:Q5,w=b(h);w&&(l.uniform1fv(w.uniform.m,u),m())},brightness:T=>{let u=(T||0)+1;v.colorMatrix([u,0,0,0,0,0,u,0,0,0,0,0,u,0,0,0,0,0,1,0])},saturation:T=>{let u=(T||0)*2/3+1,h=(u-1)*-.5;v.colorMatrix([u,h,h,0,0,h,u,h,0,0,h,h,u,0,0,0,0,0,1,0])},desaturate:()=>{v.saturation(-1)},contrast:T=>{let u=(T||0)+1,h=-128*(u-1);v.colorMatrix([u,0,0,0,h,0,u,0,0,h,0,0,u,0,h,0,0,0,1,0])},negative:()=>{v.contrast(-2)},hue:T=>{T=(T||0)/180*Math.PI;let u=Math.cos(T),h=Math.sin(T),w=.213,k=.715,I=.072;v.colorMatrix([w+u*(1-w)+h*-w,k+u*-k+h*-k,I+u*-I+h*(1-I),0,0,w+u*-w+h*.143,k+u*(1-k)+h*.14,I+u*-I+h*-.283,0,0,w+u*-w+h*-(1-w),k+u*-k+h*k,I+u*(1-I)+h*I,0,0,0,0,0,1,0])},desaturateLuminance:()=>{v.colorMatrix([.2764723,.929708,.0938197,0,-37.1,.2764723,.929708,.0938197,0,-37.1,.2764723,.929708,.0938197,0,-37.1,0,0,0,1,0])},sepia:()=>{v.colorMatrix([.393,.7689999,.18899999,0,0,.349,.6859999,.16799999,0,0,.272,.5339999,.13099999,0,0,0,0,0,1,0])},brownie:()=>{v.colorMatrix([.5997023498159715,.34553243048391263,-.2708298674538042,0,47.43192855600873,-.037703249837783157,.8609577587992641,.15059552388459913,0,-36.96841498319127,.24113635128153335,-.07441037908422492,.44972182064877153,0,-7.562075277591283,0,0,0,1,0])},vintagePinhole:()=>{v.colorMatrix([.6279345635605994,.3202183420819367,-.03965408211312453,0,9.651285835294123,.02578397704808868,.6441188644374771,.03259127616149294,0,7.462829176470591,.0466055556782719,-.0851232987247891,.5241648018700465,0,5.159190588235296,0,0,0,1,0])},kodachrome:()=>{v.colorMatrix([1.1285582396593525,-.3967382283601348,-.03992559172921793,0,63.72958762196502,-.16404339962244616,1.0835251566291304,-.05498805115633132,0,24.732407896706203,-.16786010706155763,-.5603416277695248,1.6014850761964943,0,35.62982807460946,0,0,0,1,0])},technicolor:()=>{v.colorMatrix([1.9125277891456083,-.8545344976951645,-.09155508482755585,0,11.793603434377337,-.3087833385928097,1.7658908555458428,-.10601743074722245,0,-70.35205161461398,-.231103377548616,-.7501899197440212,1.847597816108189,0,30.950940869491138,0,0,0,1,0])},polaroid:()=>{v.colorMatrix([1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0])},shiftToBGR:()=>{v.colorMatrix([0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,1,0])},convolution:T=>{let u=new Float32Array(T),h=1/c.width,w=1/c.height,k=b(t1);k&&(l.uniform1fv(k.uniform.m,u),l.uniform2f(k.uniform.px,h,w),m())},detectEdges:()=>{v.convolution.call(this,[0,1,0,1,-4,1,0,1,0])},sobelX:()=>{v.convolution.call(this,[-1,0,1,-2,0,2,-1,0,1])},sobelY:()=>{v.convolution.call(this,[-1,-2,-1,0,0,0,1,2,1])},sharpen:T=>{let u=T||1;v.convolution.call(this,[0,-1*u,0,-1*u,1+4*u,-1*u,0,-1*u,0])},emboss:T=>{let u=T||1;v.convolution.call(this,[-2*u,-1*u,0,-1*u,1,1*u,0,1*u,2*u])},blur:T=>{let u=T/7/c.width,h=T/7/c.height,w=b(e1);w&&(l.uniform2f(w.uniform.px,0,h),m(x.INTERMEDIATE),l.uniform2f(w.uniform.px,u,0),m())},pixelate:T=>{let u=T/c.width,h=T/c.height,w=b($5);w&&(l.uniform2f(w.uniform.size,u,h),m())}};this.add=function(T){let u=Array.prototype.slice.call(arguments,1),h=v[T];A.push({func:h,args:u})},this.reset=function(){A=[]},this.get=function(){return A},this.apply=function(T){f(T.width,T.height),e=0,t||(t=l.createTexture()),l.bindTexture(l.TEXTURE_2D,t),l.texParameteri(l.TEXTURE_2D,l.TEXTURE_WRAP_S,l.CLAMP_TO_EDGE),l.texParameteri(l.TEXTURE_2D,l.TEXTURE_WRAP_T,l.CLAMP_TO_EDGE),l.texParameteri(l.TEXTURE_2D,l.TEXTURE_MIN_FILTER,l.NEAREST),l.texParameteri(l.TEXTURE_2D,l.TEXTURE_MAG_FILTER,l.NEAREST),l.texImage2D(l.TEXTURE_2D,0,l.RGBA,l.RGBA,l.UNSIGNED_BYTE,T);for(let u=0;u<A.length;u++){n=u===A.length-1;let h=A[u];h.func.apply(this,h.args||[])}return c},this.draw=function(T){return this.add("brightness",0),this.apply(T)}}async function d2(e){let t=e.shape.length===4?r.squeeze(e):e,n=r.split(t,3,2),o=[r.min(n[0]),r.min(n[1]),r.min(n[2])],s=[r.max(n[0]),r.max(n[1]),r.max(n[2])],A=await Promise.all(s.map(x=>x.data())),a=Math.max(A[0][0],A[1][0],A[2][0]),c=(a>1?255:1)/a,d;if(c>1){let x=[r.sub(n[0],o[0]),r.sub(n[1],o[1]),r.sub(n[2],o[2])],l=[r.sub(s[0],o[0]),r.sub(s[1],o[1]),r.sub(s[2],o[2])],f=[r.mul(x[0],c),r.mul(x[1],c),r.mul(x[2],c)],y=r.stack([f[0],f[1],f[2]],2);d=r.reshape(y,[1,t.shape[0]||0,t.shape[1]||0,3]),r.dispose([...x,...l,...f,y])}else d=r.expandDims(t,0);return r.dispose([...n,...o,...s,n,t,e]),d}var x2=3840,c0=null,d0=null,We=null,q,L0={inputSum:0,cacheDiff:1,sumMethod:0,inputTensor:void 0};function xt(){L0.inputSum=0,L0.cacheDiff=1,L0.sumMethod=0,L0.inputTensor=void 0}function D0(e,t){let n;if(M.browser)if(M.worker){if(typeof OffscreenCanvas=="undefined")throw new Error("canvas error: attempted to run in web worker but OffscreenCanvas is not supported");n=new OffscreenCanvas(e,t)}else if(typeof document!="undefined")n=document.createElement("canvas"),n.width=e,n.height=t;else if(typeof navigator!="undefined"&&navigator.product==="ReactNative")if(typeof M.Canvas!="undefined")n=new M.Canvas(e,t);else if(typeof globalThis.Canvas!="undefined")n=new globalThis.Canvas(e,t);else throw new Error("canvas error: attempted to use canvas in react-native without canvas support installed");else throw new Error("canvas error: attempted to run in browser but DOM is not defined");else typeof M.Canvas!="undefined"?n=new M.Canvas(e,t):typeof globalThis.Canvas!="undefined"&&(n=new globalThis.Canvas(e,t));return n}function y2(e,t){let n=t||D0(e.width,e.height);return n.getContext("2d").drawImage(e,0,0),n}async function f2(e,t,n=!0){var f,y,p;if(!e)return t.debug&&g("input error: input is missing"),{tensor:null,canvas:null};if(!(e instanceof r.Tensor)&&!(typeof Image!="undefined"&&e instanceof Image)&&!(typeof globalThis.Canvas!="undefined"&&e instanceof globalThis.Canvas)&&!(typeof ImageData!="undefined"&&e instanceof ImageData)&&!(typeof ImageBitmap!="undefined"&&e instanceof ImageBitmap)&&!(typeof HTMLImageElement!="undefined"&&e instanceof HTMLImageElement)&&!(typeof HTMLMediaElement!="undefined"&&e instanceof HTMLMediaElement)&&!(typeof HTMLVideoElement!="undefined"&&e instanceof HTMLVideoElement)&&!(typeof HTMLCanvasElement!="undefined"&&e instanceof HTMLCanvasElement)&&!(typeof OffscreenCanvas!="undefined"&&e instanceof OffscreenCanvas))throw new Error("input error: type not recognized");if(e instanceof r.Tensor){let m=null;if(e.isDisposedInternal)throw new Error("input error: attempted to use tensor but it is disposed");if(!e.shape)throw new Error("input error: attempted to use tensor without a shape");if(e.shape.length===3){if(e.shape[2]===3)m=r.expandDims(e,0);else if(e.shape[2]===4){let b=r.slice3d(e,[0,0,0],[-1,-1,3]);m=r.expandDims(b,0),r.dispose(b)}}else e.shape.length===4&&(e.shape[3]===3?m=r.clone(e):e.shape[3]===4&&(m=r.slice4d(e,[0,0,0,0],[-1,-1,-1,3])));if(m==null||m.shape.length!==4||m.shape[0]!==1||m.shape[3]!==3)throw new Error(`input error: attempted to use tensor with unrecognized shape: ${e.shape.toString()}`);if(m.dtype==="int32"){let b=r.cast(m,"float32");r.dispose(m),m=b}return{tensor:m,canvas:t.filter.return?d0:null}}if(typeof e.readyState!="undefined"&&e.readyState<=2)return t.debug&&g("input stream is not ready"),{tensor:null,canvas:c0};let o=e.naturalWidth||e.videoWidth||e.width||e.shape&&e.shape[1]>0,s=e.naturalHeight||e.videoHeight||e.height||e.shape&&e.shape[2]>0;if(!o||!s)return t.debug&&g("cannot determine input dimensions"),{tensor:null,canvas:c0};let A=o,a=s;if(A>x2&&(A=x2,a=Math.trunc(A*s/o)),a>x2&&(a=x2,A=Math.trunc(a*o/s)),(((f=t.filter)==null?void 0:f.width)||0)>0?A=t.filter.width:(((y=t.filter)==null?void 0:y.height)||0)>0&&(A=o*((t.filter.height||0)/s)),(t.filter.height||0)>0?a=t.filter.height:(t.filter.width||0)>0&&(a=s*((t.filter.width||0)/o)),!A||!a)throw new Error("input error: cannot determine dimension");(!c0||c0.width!==A||c0.height!==a)&&(c0=D0(A,a));let i=c0.getContext("2d");if(typeof ImageData!="undefined"&&e instanceof ImageData?i.putImageData(e,0,0):t.filter.flip&&typeof i.translate!="undefined"?(i.translate(o,0),i.scale(-1,1),i.drawImage(e,0,0,o,s,0,0,c0.width,c0.height),i.setTransform(1,0,0,1,0,0)):i.drawImage(e,0,0,o,s,0,0,c0.width,c0.height),(!d0||c0.width!==d0.width||c0.height!==d0.height)&&(d0=D0(c0.width,c0.height)),t.filter.enabled&&M.webgl.supported?(q||(q=M.browser?new n1:null),M.filter=!!q,q!=null&&q.add?(q.reset(),t.filter.brightness!==0&&q.add("brightness",t.filter.brightness),t.filter.contrast!==0&&q.add("contrast",t.filter.contrast),t.filter.sharpness!==0&&q.add("sharpen",t.filter.sharpness),t.filter.blur!==0&&q.add("blur",t.filter.blur),t.filter.saturation!==0&&q.add("saturation",t.filter.saturation),t.filter.hue!==0&&q.add("hue",t.filter.hue),t.filter.negative&&q.add("negative"),t.filter.sepia&&q.add("sepia"),t.filter.vintage&&q.add("brownie"),t.filter.sepia&&q.add("sepia"),t.filter.kodachrome&&q.add("kodachrome"),t.filter.technicolor&&q.add("technicolor"),t.filter.polaroid&&q.add("polaroid"),t.filter.pixelate!==0&&q.add("pixelate",t.filter.pixelate),((p=q.get())==null?void 0:p.length)>1?d0=q.apply(c0):d0=q.draw(c0)):(t.debug&&g("input process error: cannot initialize filters"),M.webgl.supported=!1,t.filter.enabled=!1,y2(c0,d0))):(y2(c0,d0),q&&(q=null),M.filter=!!q),!n)return{tensor:null,canvas:d0};if(!d0)throw new Error("canvas error: cannot create output");let c,d=3;if(typeof ImageData!="undefined"&&e instanceof ImageData||e.data&&e.width&&e.height)if(M.browser&&r.browser)c=r.browser?r.browser.fromPixels(e):null;else{d=e.data.length/e.height/e.width;let m=new Uint8Array(e.data.buffer);c=r.tensor(m,[e.height,e.width,d],"int32")}else if((!We||d0.width!==We.width||d0.height!==We.height)&&(We=D0(d0.width,d0.height)),r.browser&&M.browser)t.backend==="webgl"||t.backend==="humangl"||t.backend==="webgpu"?c=r.browser.fromPixels(d0):(We=y2(d0),c=r.browser.fromPixels(We));else{let v=y2(d0).getContext("2d").getImageData(0,0,A,a);d=v.data.length/A/a;let T=new Uint8Array(v.data.buffer);c=r.tensor(T,[A,a,d])}if(d===4){let m=r.slice3d(c,[0,0,0],[-1,-1,3]);r.dispose(c),c=m}if(!c)throw new Error("input error: cannot create tensor");let x=r.cast(c,"float32"),l=t.filter.equalization?await d2(x):r.expandDims(x,0);if(r.dispose([c,x]),t.filter.autoBrightness){let m=r.max(l),b=await m.data();t.filter.brightness=b[0]>1?1-b[0]/255:1-b[0],r.dispose(m)}return{tensor:l,canvas:t.filter.return?d0:null}}async function o1(e,t){let n=!1;if(e.cacheSensitivity===0||!t.shape||t.shape.length!==4||t.shape[1]>3840||t.shape[2]>2160)return n;if(!L0.inputTensor)L0.inputTensor=r.clone(t);else if(L0.inputTensor.shape[1]!==t.shape[1]||L0.inputTensor.shape[2]!==t.shape[2])r.dispose(L0.inputTensor),L0.inputTensor=r.clone(t);else{let o={};o.diff=r.sub(t,L0.inputTensor),o.squared=r.mul(o.diff,o.diff),o.sum=r.sum(o.squared);let A=(await o.sum.data())[0]/(t.shape[1]||1)/(t.shape[2]||1)/255/3;r.dispose([L0.inputTensor,o.diff,o.squared,o.sum]),L0.inputTensor=r.clone(t),n=A<=(e.cacheSensitivity||0)}return n}async function r1(e,t,n){let o={};if(!t||!n||t.shape.length!==4||t.shape.length!==n.shape.length)return e.debug||g("invalid input tensor or tensor shapes do not match:",t.shape,n.shape),0;if(t.shape[0]!==1||n.shape[0]!==1||t.shape[3]!==3||n.shape[3]!==3)return e.debug||g("input tensors must be of shape [1, height, width, 3]:",t.shape,n.shape),0;o.input1=r.clone(t),o.input2=t.shape[1]!==n.shape[1]||t.shape[2]!==n.shape[2]?r.image.resizeBilinear(n,[t.shape[1],t.shape[2]]):r.clone(n),o.diff=r.sub(o.input1,o.input2),o.squared=r.mul(o.diff,o.diff),o.sum=r.sum(o.squared);let A=(await o.sum.data())[0]/(t.shape[1]||1)/(t.shape[2]||1)/255/3;return r.dispose([o.input1,o.input2,o.diff,o.squared,o.sum]),A}var Qe,_e,$e,p2=class{constructor(){z(this,"browser");z(this,"node");z(this,"worker");z(this,"platform","");z(this,"agent","");z(this,"backends",[]);z(this,"initial");z(this,"filter");z(this,"tfjs");z(this,"offscreen");z(this,"perfadd",!1);z(this,"tensorflow",{version:void 0,gpu:void 0});z(this,"wasm",{supported:void 0,backend:void 0,simd:void 0,multithread:void 0});z(this,"webgl",{supported:void 0,backend:void 0,version:void 0,renderer:void 0,shader:void 0,vendor:void 0});z(this,"webgpu",{supported:void 0,backend:void 0,adapter:void 0});z(this,"cpu",{model:void 0,flags:[]});z(this,"kernels",[]);q0(this,Qe);q0(this,_e);q0(this,$e);if(this.browser=typeof navigator!="undefined"&&typeof navigator.appVersion!="undefined",this.node=typeof process!="undefined"&&typeof process.versions!="undefined"&&typeof process.versions.node!="undefined",this.tfjs={version:Je["tfjs-core"]},this.offscreen=typeof OffscreenCanvas!="undefined",this.initial=!0,this.worker=this.browser&&this.offscreen?typeof WorkerGlobalScope!="undefined":void 0,typeof navigator!="undefined"&&typeof navigator.userAgent!="undefined"){let t=navigator.userAgent||"",n=t.match(/\(([^()]+)\)/g);if(n!=null&&n[0]){let o=n[0].match(/\(([^()]+)\)/g);this.platform=o!=null&&o[0]?o[0].replace(/\(|\)/g,""):"",this.agent=t.replace(n[0],""),this.platform[1]&&(this.agent=this.agent.replace(n[1],"")),this.agent=this.agent.replace(/  /g," ")}}else typeof process!="undefined"&&(this.platform=`${process.platform} ${process.arch}`,this.agent=`NodeJS ${process.version}`)}get Canvas(){return E0(this,Qe)}set Canvas(t){_0(this,Qe,t),globalThis.Canvas=t}get Image(){return E0(this,_e)}set Image(t){_0(this,_e,t),globalThis.Image=t}get ImageData(){return E0(this,$e)}set ImageData(t){_0(this,$e,t),globalThis.ImageData=t}async updateBackend(){this.backends=Object.keys(r.engine().registryFactory);try{this.tensorflow={version:r.backend().binding?r.backend().binding.TF_Version:void 0,gpu:r.backend().binding?r.backend().binding.isUsingGpuDevice():void 0}}catch(o){}this.wasm.supported=typeof WebAssembly!="undefined",this.wasm.backend=this.backends.includes("wasm"),this.wasm.supported&&this.wasm.backend&&(this.wasm.simd=await r.env().getAsync("WASM_HAS_SIMD_SUPPORT"),this.wasm.multithread=await r.env().getAsync("WASM_HAS_MULTITHREAD_SUPPORT"));let t=D0(100,100),n=t?t.getContext("webgl2"):void 0;this.webgl.supported=typeof n!="undefined",this.webgl.backend=this.backends.includes("webgl"),this.webgl.supported&&this.webgl.backend&&n&&(this.webgl.version=n.getParameter(n.VERSION),this.webgl.vendor=n.getParameter(n.VENDOR),this.webgl.renderer=n.getParameter(n.RENDERER),this.webgl.shader=n.getParameter(n.SHADING_LANGUAGE_VERSION)),this.webgpu.supported=this.browser&&typeof navigator!="undefined"&&typeof navigator.gpu!="undefined",this.webgpu.backend=this.backends.includes("webgpu");try{if(this.webgpu.supported){let o=await navigator.gpu.requestAdapter();o&&("requestAdapterInfo"in o?this.webgpu.adapter=await o.requestAdapterInfo():this.webgpu.adapter=await o.info)}}catch(o){this.webgpu.supported=!1}try{this.kernels=r.getKernelsForBackend(r.getBackend()).map(o=>o.kernelName.toLowerCase())}catch(o){}}updateCPU(){let t={model:"",flags:[]};this.node&&this.platform.startsWith("linux"),this.cpu?this.cpu=t:Object.defineProperty(this,"cpu",{value:t})}};Qe=new WeakMap,_e=new WeakMap,$e=new WeakMap;var M=new p2;var u2=class{constructor(){z(this,"config");z(this,"element");z(this,"stream");z(this,"devices",[]);z(this,"enumerate",async()=>{try{let t=await navigator.mediaDevices.enumerateDevices();this.devices=t.filter(n=>n.kind==="videoinput")}catch(t){this.devices=[]}return this.devices});z(this,"start",async t=>{var s,A;if(t!=null&&t.debug&&(this.config.debug=t==null?void 0:t.debug),t!=null&&t.crop&&(this.config.crop=t==null?void 0:t.crop),t!=null&&t.mode&&(this.config.mode=t==null?void 0:t.mode),t!=null&&t.width&&(this.config.width=t==null?void 0:t.width),t!=null&&t.height&&(this.config.height=t==null?void 0:t.height),t!=null&&t.id&&(this.config.id=t==null?void 0:t.id),t!=null&&t.element)if(typeof t.element=="string"){let a=document.getElementById(t.element);if(a&&a instanceof HTMLVideoElement)this.element=a;else return this.config.debug&&g("webcam","cannot get dom element",t.element),`webcam error: cannot get dom element: ${t.element}`}else if(t.element instanceof HTMLVideoElement)this.element=t.element;else return this.config.debug&&g("webcam","unknown dom element",t.element),`webcam error: unknown dom element: ${t.element}`;else this.element=document.createElement("video");let n={audio:!1,video:{facingMode:this.config.mode==="front"?"user":"environment",resizeMode:this.config.crop?"crop-and-scale":"none"}};if(((s=this.config)==null?void 0:s.width)>0&&(n.video.width={ideal:this.config.width}),((A=this.config)==null?void 0:A.height)>0&&(n.video.height={ideal:this.config.height}),this.config.id&&(n.video.deviceId=this.config.id),this.element.addEventListener("play",()=>{this.config.debug&&g("webcam","play")}),this.element.addEventListener("pause",()=>{this.config.debug&&g("webcam","pause")}),this.element.addEventListener("click",async()=>{!this.element||!this.stream||(this.element.paused?await this.element.play():this.element.pause())}),!(navigator!=null&&navigator.mediaDevices))return this.config.debug&&g("webcam error","no devices"),"webcam error: no devices";try{this.stream=await navigator.mediaDevices.getUserMedia(n)}catch(a){return g("webcam",a),`webcam error: ${a}`}return this.stream?(this.element.srcObject=this.stream,await new Promise(a=>{this.element?this.element.onloadeddata=()=>a(!0):a(!1)}),await this.element.play(),this.config.debug&&g("webcam",{width:this.width,height:this.height,label:this.label,stream:this.stream,track:this.track,settings:this.settings,constraints:this.constraints,capabilities:this.capabilities}),`webcam: ${this.label}`):(this.config.debug&&g("webcam error","no stream"),"webcam error no stream")});z(this,"pause",()=>{this.element&&this.element.pause()});z(this,"play",async()=>{this.element&&await this.element.play()});z(this,"stop",()=>{this.config.debug&&g("webcam","stop"),this.track&&this.track.stop()});this.config={element:void 0,debug:!0,mode:"front",crop:!1,width:0,height:0}}get track(){if(this.stream)return this.stream.getVideoTracks()[0]}get capabilities(){if(this.track)return this.track.getCapabilities?this.track.getCapabilities():void 0}get constraints(){if(this.track)return this.track.getConstraints?this.track.getConstraints():void 0}get settings(){if(!this.stream)return;let t=this.stream.getVideoTracks()[0];return t.getSettings?t.getSettings():void 0}get label(){return this.track?this.track.label:""}get paused(){var t;return((t=this.element)==null?void 0:t.paused)||!1}get width(){var t;return((t=this.element)==null?void 0:t.videoWidth)||0}get height(){var t;return((t=this.element)==null?void 0:t.videoHeight)||0}};var yt={};re(yt,{"affectnet-mobilenet":()=>Ro,age:()=>Mo,"anti-spoofing":()=>nr,antispoof:()=>co,blazeface:()=>xo,"blazeface-back":()=>Po,"blazeface-front":()=>ko,"blazepose-detector":()=>wo,"blazepose-full":()=>Eo,"blazepose-heavy":()=>zo,"blazepose-lite":()=>So,centernet:()=>yo,default:()=>mr,efficientpose:()=>jo,"efficientpose-i-lite":()=>or,"efficientpose-ii-lite":()=>rr,"efficientpose-iv":()=>sr,emotion:()=>fo,faceboxes:()=>No,facemesh:()=>mo,"facemesh-attention":()=>Lo,"facemesh-attention-pinto":()=>Io,"facemesh-detection-full":()=>Oo,"facemesh-detection-short":()=>Co,faceres:()=>po,"faceres-deep":()=>Wo,gear:()=>Bo,"gear-e1":()=>Do,"gear-e2":()=>Fo,gender:()=>Go,"gender-ssrnet-imdb":()=>Ho,handdetect:()=>Vo,"handlandmark-full":()=>Zo,"handlandmark-lite":()=>uo,"handlandmark-sparse":()=>Xo,handskeleton:()=>qo,handtrack:()=>ho,"insightface-efficientnet-b0":()=>Ar,"insightface-ghostnet-strides1":()=>ar,"insightface-ghostnet-strides2":()=>ir,"insightface-mobilenet-emore":()=>lr,"insightface-mobilenet-swish":()=>cr,iris:()=>bo,liveness:()=>go,meet:()=>Uo,mobileface:()=>Yo,mobilefacenet:()=>Ko,models:()=>To,"movenet-lightning":()=>vo,"movenet-multipose":()=>Jo,"movenet-thunder":()=>Qo,nanodet:()=>_o,"nanodet-e":()=>dr,"nanodet-g":()=>xr,"nanodet-m":()=>yr,"nanodet-t":()=>fr,posenet:()=>$o,rvm:()=>er,selfie:()=>tr});var co=853098,xo=538928,yo=4030290,fo=820516,mo=1477958,po=6978814,uo=2023432,ho=2964837,bo=2599092,go=592976,To=0,vo=4650216,Ro=6920630,Mo=161240,Po=538928,ko=402048,wo=5928856,Eo=6339202,zo=27502466,So=2726402,jo=5651240,No=2013002,Io=2387598,Lo=2382414,Oo=1026192,Co=201268,Wo=13957620,Do=112438,Fo=112438,Bo=1498916,Ho=161236,Go=201808,Vo=3515612,Zo=5431368,Xo=5286322,qo=5502280,Uo=372228,Yo=2183192,Ko=5171976,Jo=9448838,Qo=12477112,_o=7574558,$o=5032780,er=3739355,tr=212886,nr=853098,or=2269064,rr=5651240,sr=25643252,Ar=13013224,ar=8093408,ir=8049584,lr=6938536,cr=12168584,dr=12319156,xr=7574558,yr=1887474,fr=5294216,mr={antispoof:co,blazeface:xo,centernet:yo,emotion:fo,facemesh:mo,faceres:po,"handlandmark-lite":uo,handtrack:ho,iris:bo,liveness:go,models:To,"movenet-lightning":vo,"affectnet-mobilenet":Ro,age:Mo,"blazeface-back":Po,"blazeface-front":ko,"blazepose-detector":wo,"blazepose-full":Eo,"blazepose-heavy":zo,"blazepose-lite":So,efficientpose:jo,faceboxes:No,"facemesh-attention-pinto":Io,"facemesh-attention":Lo,"facemesh-detection-full":Oo,"facemesh-detection-short":Co,"faceres-deep":Wo,"gear-e1":Do,"gear-e2":Fo,gear:Bo,"gender-ssrnet-imdb":Ho,gender:Go,handdetect:Vo,"handlandmark-full":Zo,"handlandmark-sparse":Xo,handskeleton:qo,meet:Uo,mobileface:Yo,mobilefacenet:Ko,"movenet-multipose":Jo,"movenet-thunder":Qo,nanodet:_o,posenet:$o,rvm:er,selfie:tr,"anti-spoofing":nr,"efficientpose-i-lite":or,"efficientpose-ii-lite":rr,"efficientpose-iv":sr,"insightface-efficientnet-b0":Ar,"insightface-ghostnet-strides1":ar,"insightface-ghostnet-strides2":ir,"insightface-mobilenet-emore":lr,"insightface-mobilenet-swish":cr,"nanodet-e":dr,"nanodet-g":xr,"nanodet-m":yr,"nanodet-t":fr};var T0={cacheModels:!0,cacheSupported:!0,verbose:!0,debug:!1,modelBasePath:""},l0={};async function pr(e,t){return T0.debug&&g("load model fetch:",e,t),fetch(e,t)}function s1(e){T0.cacheModels=e.cacheModels,T0.verbose=e.debug,T0.modelBasePath=e.modelBasePath}async function L(e){var c,d,x,l,f,y;let t=K5(T0.modelBasePath,e||"");t.toLowerCase().endsWith(".json")||(t+=".json");let n=t.includes("/")?t.split("/"):t.split("\\"),o=n[n.length-1].replace(".json",""),s="indexeddb://"+o;l0[o]={name:o,loaded:!1,sizeFromManifest:0,sizeLoadedWeights:0,sizeDesired:yt[o],inCache:!1,url:""},T0.cacheSupported=typeof indexedDB!="undefined";let A={};try{A=T0.cacheSupported&&T0.cacheModels?await r.io.listModels():{}}catch(p){T0.cacheSupported=!1}l0[o].inCache=T0.cacheSupported&&T0.cacheModels&&Object.keys(A).includes(s),l0[o].url=l0[o].inCache?s:t;let a=typeof fetch=="undefined"?{}:{fetchFunc:(p,m)=>pr(p,m)},i=new r.GraphModel(l0[o].url,a);l0[o].loaded=!1;try{i.findIOHandler(),T0.debug&&g("model load handler:",i.handler)}catch(p){g("error finding model i/o handler:",t,p)}try{let p=await((c=i.handler)==null?void 0:c.load())||null;l0[o].sizeFromManifest=((d=p==null?void 0:p.weightData)==null?void 0:d.byteLength)||0,p?i.loadSync(p):i=await r.loadGraphModel(l0[o].inCache?s:t,a),l0[o].sizeLoadedWeights=((l=(x=i.artifacts)==null?void 0:x.weightData)==null?void 0:l.byteLength)||((y=(f=i.artifacts)==null?void 0:f.weightData)==null?void 0:y[0].byteLength)||0,T0.verbose&&g("load:",{model:o,url:i.modelUrl,bytes:l0[o].sizeLoadedWeights}),l0[o].loaded=!0}catch(p){g("error loading model:",t,p)}if(l0[o].loaded&&T0.cacheModels&&T0.cacheSupported&&!l0[o].inCache)try{let p=await i.save(s);T0.debug&&g("model saved:",s,p)}catch(p){g("error saving model:",t,p)}return i}var ft="3.3.5";var K={name:"humangl",priority:999,canvas:null,gl:null,extensions:[],webGLattr:{alpha:!1,antialias:!1,premultipliedAlpha:!1,preserveDrawingBuffer:!1,depth:!1,stencil:!1,failIfMajorPerformanceCaveat:!1,desynchronized:!0}};function br(){let e=K.gl;e&&(K.extensions=e.getSupportedExtensions())}function A1(e){var t;if(e.config.backend==="humangl"&&(K.name in r.engine().registry&&!((t=K==null?void 0:K.gl)!=null&&t.getParameter(K.gl.VERSION))&&(g("humangl error: backend invalid context"),e.models.reset()),!r.findBackend(K.name))){try{K.canvas=D0(100,100)}catch(s){g("humangl error: cannot create canvas:",s);return}try{if(K.gl=K.canvas.getContext("webgl2",K.webGLattr),!K.gl){g("humangl error: cannot get webgl context");return}if(!K.gl.getParameter(K.gl.VERSION).includes("2.0")){g("backend override: using fallback webgl backend as webgl 2.0 is not detected"),e.config.backend="webgl";return}K.canvas&&(K.canvas.addEventListener("webglcontextlost",A=>{throw g("humangl error:",A.type),g("possible browser memory leak using webgl or conflict with multiple backend registrations"),e.emit("error"),new Error("backend error: webgl context lost")}),K.canvas.addEventListener("webglcontextrestored",A=>{g("humangl error: context restored:",A)}),K.canvas.addEventListener("webglcontextcreationerror",A=>{g("humangl error: context create:",A)}))}catch(s){g("humangl error: cannot get webgl context:",s);return}try{r.setWebGLContext(2,K.gl)}catch(s){g("humangl error: cannot set webgl context:",s);return}try{let s=new r.GPGPUContext(K.gl);r.registerBackend(K.name,()=>new r.MathBackendWebGL(s),K.priority)}catch(s){g("humangl error: cannot register webgl backend:",s);return}try{r.getKernelsForBackend("webgl").forEach(A=>{let a={...A,backendName:K.name};r.registerKernel(a)})}catch(s){g("humangl error: cannot update webgl backend registration:",s);return}try{r.env().flagRegistry.WEBGL_VERSION&&r.env().set("WEBGL_VERSION",2)}catch(s){g("humangl error: cannot set WebGL backend flags:",s);return}br();let n=r.backend(),o=typeof n.gpgpu!="undefined"?n.getGPGPUContext().gl:null;o?e.config.debug&&g("humangl backend registered:",{webgl:o.getParameter(o.VERSION),renderer:o.getParameter(o.RENDERER)}):g("humangl error: no current gl context:",o,K.gl)}}var O={tf255:255,tf1:1,tf2:2,tf05:.5,tf127:127.5,rgb:[.2989,.587,.114]};function a1(){O.tf255=r.scalar(255,"float32"),O.tf1=r.scalar(1,"float32"),O.tf2=r.scalar(2,"float32"),O.tf05=r.scalar(.5,"float32"),O.tf127=r.scalar(127.5,"float32"),O.rgb=r.tensor1d([.2989,.587,.114],"float32")}async function vr(){var e;return await M.updateBackend(),(e=M.tensorflow)!=null&&e.version?"tensorflow":M.webgpu.supported&&M.webgpu.backend?"webgpu":M.webgl.supported&&M.webgl.backend?"webgl":M.wasm.supported&&M.wasm.backend?"wasm":"cpu"}function Rr(e){let t=[];if(!M.kernels.includes("mod")){let n={kernelName:"Mod",backendName:r.getBackend(),kernelFunc:o=>r.tidy(()=>r.sub(o.inputs.a,r.mul(r.div(o.inputs.a,o.inputs.b),o.inputs.b)))};r.registerKernel(n),M.kernels.push("mod"),t.push("mod")}if(!M.kernels.includes("floormod")){let n={kernelName:"FloorMod",backendName:r.getBackend(),kernelFunc:o=>r.tidy(()=>r.add(r.mul(r.floorDiv(o.inputs.a,o.inputs.b),o.inputs.b),r.mod(o.inputs.a,o.inputs.b)))};r.registerKernel(n),M.kernels.push("floormod"),t.push("floormod")}if(!M.kernels.includes("rotatewithoffset")&&e.softwareKernels){let n={kernelName:"RotateWithOffset",backendName:r.getBackend(),kernelFunc:o=>r.tidy(()=>{let s=r.getBackend();r.setBackend("cpu");let A=r.image.rotateWithOffset(o.inputs.image,o.attrs.radians,o.attrs.fillValue,o.attrs.center);return r.setBackend(s),A})};r.registerKernel(n),M.kernels.push("rotatewithoffset"),t.push("rotatewithoffset")}t.length>0&&e.debug&&g("registered kernels:",t)}var i1={};async function e2(e,t=!1){var n,o;if(e.state="backend",((n=e.config.backend)==null?void 0:n.length)===0&&(e.config.backend=await vr()),t||M.initial||e.config.backend&&e.config.backend.length>0&&r.getBackend()!==e.config.backend){let s=R();if(e.config.backend&&e.config.backend.length>0){typeof window=="undefined"&&typeof WorkerGlobalScope!="undefined"&&e.config.debug&&e.config.debug&&g("running inside web worker"),typeof navigator!="undefined"&&((o=navigator==null?void 0:navigator.userAgent)!=null&&o.toLowerCase().includes("electron"))&&e.config.debug&&g("running inside electron");let A=Object.keys(r.engine().registryFactory);if(e.config.backend==="humangl"&&!A.includes("humangl")&&(A1(e),A=Object.keys(r.engine().registryFactory)),e.config.debug&&g("available backends:",A),M.browser&&!M.node&&e.config.backend==="tensorflow"&&A.includes("webgl")&&(e.config.debug&&g("override: backend set to tensorflow while running in browser"),e.config.backend="webgl"),M.node&&!M.browser&&(e.config.backend==="webgl"||e.config.backend==="humangl")&&A.includes("tensorflow")&&(e.config.debug&&g(`override: backend set to ${e.config.backend} while running in nodejs`),e.config.backend="tensorflow"),M.browser&&e.config.backend==="webgpu")if(typeof navigator=="undefined"||typeof navigator.gpu=="undefined")g("override: backend set to webgpu but browser does not support webgpu"),e.config.backend="webgl";else{let a=await navigator.gpu.requestAdapter();if(e.config.debug&&g("enumerated webgpu adapter:",a),!a)g("override: backend set to webgpu but browser reports no available gpu"),e.config.backend="webgl";else{let i;"requestAdapterInfo"in a?i=await(a==null?void 0:a.requestAdapterInfo()):i=a.info,g("webgpu adapter info:",i)}}if(A.includes(e.config.backend)||(g(`error: backend ${e.config.backend} not found in registry`),e.config.backend=M.node?"tensorflow":"webgl",e.config.debug&&g(`override: setting backend ${e.config.backend}`)),e.config.debug&&g("setting backend:",[e.config.backend]),e.config.backend==="wasm"){if(r.env().flagRegistry.CANVAS2D_WILL_READ_FREQUENTLY&&r.env().set("CANVAS2D_WILL_READ_FREQUENTLY",!0),e.config.debug&&g("wasm path:",e.config.wasmPath),typeof r.setWasmPaths!="undefined")r.setWasmPaths(e.config.wasmPath,e.config.wasmPlatformFetch);else throw new Error("backend error: attempting to use wasm backend but wasm path is not set");let a=!1,i=!1;try{a=await r.env().getAsync("WASM_HAS_MULTITHREAD_SUPPORT"),i=await r.env().getAsync("WASM_HAS_SIMD_SUPPORT"),e.config.debug&&g(`wasm execution: ${i?"simd":"no simd"} ${a?"multithreaded":"singlethreaded"}`),e.config.debug&&!i&&g("warning: wasm simd support is not enabled")}catch(c){g("wasm detection failed")}}try{await r.setBackend(e.config.backend),await r.ready()}catch(a){return g("error: cannot set backend:",e.config.backend,a),!1}e.config.debug&&(i1=JSON.parse(JSON.stringify(r.env().flags)))}if((r.getBackend()==="humangl"||r.getBackend()==="webgl")&&(r.env().flagRegistry.WEBGL_USE_SHAPES_UNIFORMS&&r.env().set("WEBGL_USE_SHAPES_UNIFORMS",!0),r.env().flagRegistry.WEBGL_EXP_CONV&&r.env().set("WEBGL_EXP_CONV",!0),e.config.debug&&typeof e.config.deallocate!="undefined"&&e.config.deallocate&&(g("changing webgl: WEBGL_DELETE_TEXTURE_THRESHOLD:",!0),r.env().set("WEBGL_DELETE_TEXTURE_THRESHOLD",0))),r.getBackend(),e.config.debug){let A=r.env().flags,a={};for(let i of Object.keys(A))i1[i]!==A[i]&&(a[i]=A[i]);e.config.debug&&Object.keys(a).length>0&&g("backend:",r.getBackend(),"flags:",a)}if(e.config.flags&&Object.keys(e.config.flags).length>0){e.config.debug&&g("flags:",e.config.flags);for(let[A,a]of Object.entries(e.config.flags))r.env().set(A,a)}r.enableProdMode(),a1(),e.performance.initBackend=Math.trunc(R()-s),e.config.backend=r.getBackend(),await M.updateBackend(),Rr(e.config)}return!0}function h2(e,t){for(let n of e){let o={kernelName:n,backendName:t.backend,kernelFunc:s=>{var A;return t.debug&&g("kernelFunc",n,t.backend,s),(A=s==null?void 0:s.inputs)==null?void 0:A.info}};r.registerKernel(o)}M.kernels=r.getKernelsForBackend(r.getBackend()).map(n=>n.kernelName.toLowerCase())}var vt={};re(vt,{all:()=>ts,body:()=>g2,canvas:()=>$r,face:()=>b2,gesture:()=>R2,hand:()=>T2,init:()=>Tt,object:()=>v2,options:()=>n0,person:()=>_r,tensor:()=>es});var O0=e=>{if(!e)g("draw error: invalid canvas");else if(!e.getContext)g("draw error: canvas context not defined");else{let t=e.getContext("2d",{willReadFrequently:!0});if(!t)g("draw error: cannot get canvas context");else return t}return null},ge=e=>Math.round(e*180/Math.PI),H=(e,t,n)=>e.replace(t,typeof n=="number"?n.toFixed(1):n),Te=(e,t)=>{if(!t.useDepth||typeof e=="undefined")return t.color;let n=Uint8ClampedArray.from([127+2*e,127-2*e,255]);return`rgba(${n[0]}, ${n[1]}, ${n[2]}, ${t.alpha})`};function C0(e,t,n,o,s){let A=t.replace(/\[.*\]/g,"").split(`
`).map(i=>i.trim()),a=Math.max(0,n);for(let i=A.length-1;i>=0;i--){let c=i*s.lineHeight+o;s.shadowColor&&s.shadowColor!==""&&(e.fillStyle=s.shadowColor,e.fillText(A[i],a+5,c+16)),e.fillStyle=s.labelColor,e.fillText(A[i],a+4,c+15)}}function U0(e,t,n,o,s){e.fillStyle=Te(o,s),e.beginPath(),e.arc(t,n,s.pointSize,0,2*Math.PI),e.fill()}function Y0(e,t,n,o,s,A){if(e.beginPath(),e.lineWidth=A.lineWidth,A.useCurves){let a=(t+t+o)/2,i=(n+n+s)/2;e.ellipse(a,i,o/2,s/2,0,0,2*Math.PI)}else e.moveTo(t+A.roundRect,n),e.lineTo(t+o-A.roundRect,n),e.quadraticCurveTo(t+o,n,t+o,n+A.roundRect),e.lineTo(t+o,n+s-A.roundRect),e.quadraticCurveTo(t+o,n+s,t+o-A.roundRect,n+s),e.lineTo(t+A.roundRect,n+s),e.quadraticCurveTo(t,n+s,t,n+s-A.roundRect),e.lineTo(t,n+A.roundRect),e.quadraticCurveTo(t,n,t+A.roundRect,n),e.closePath();e.stroke()}function mt(e,t,n){if(!(t.length<2)){e.beginPath(),e.moveTo(t[0][0],t[0][1]);for(let o of t)e.strokeStyle=Te(o[2]||0,n),e.lineTo(Math.trunc(o[0]),Math.trunc(o[1]));e.stroke(),n.fillPolygons&&(e.closePath(),e.fill())}}function c1(e,t,n){if(!(t.length<2)){if(e.lineWidth=n.lineWidth,!n.useCurves||t.length<=2){mt(e,t,n);return}e.moveTo(t[0][0],t[0][1]);for(let o=0;o<t.length-2;o++){let s=(t[o][0]+t[o+1][0])/2,A=(t[o][1]+t[o+1][1])/2;e.quadraticCurveTo(t[o][0],t[o][1],s,A)}e.quadraticCurveTo(t[t.length-2][0],t[t.length-2][1],t[t.length-1][0],t[t.length-1][1]),e.stroke(),n.fillPolygons&&(e.closePath(),e.fill())}}function pt(e,t,n,o=5){let s,A,a;e.beginPath(),e.moveTo(t[0],t[1]),e.lineTo(n[0],n[1]),s=Math.atan2(n[1]-t[1],n[0]-t[0]),A=o*Math.cos(s)+n[0],a=o*Math.sin(s)+n[1],e.moveTo(A,a),s+=1/3*(2*Math.PI),A=o*Math.cos(s)+n[0],a=o*Math.sin(s)+n[1],e.lineTo(A,a),s+=1/3*(2*Math.PI),A=o*Math.cos(s)+n[0],a=o*Math.sin(s)+n[1],e.lineTo(A,a),e.closePath(),e.stroke(),e.fill()}var n0={color:"rgba(173, 216, 230, 0.6)",labelColor:"rgba(173, 216, 230, 1)",shadowColor:"black",alpha:.5,font:'small-caps 16px "Segoe UI"',lineHeight:18,lineWidth:4,pointSize:2,roundRect:8,drawPoints:!1,drawLabels:!0,drawBoxes:!0,drawAttention:!0,drawGestures:!0,drawPolygons:!0,drawGaze:!0,fillPolygons:!1,useDepth:!0,useCurves:!1,faceLabels:"",bodyLabels:"",bodyPartLabels:"",objectLabels:"",handLabels:"",fingerLabels:"",gestureLabels:""};var F0={silhouette:[10,338,297,332,284,251,389,356,454,323,361,288,397,365,379,378,400,377,152,148,176,149,150,136,172,58,132,93,234,127,162,21,54,103,67,109],lipsUpperOuter:[185,40,39,37,0,267,269,270,409],lipsLowerOuter:[61,146,91,181,84,17,314,405,321,375,291],lipsUpperInner:[191,80,81,82,13,312,311,310,415],lipsLowerInner:[78,95,88,178,87,14,317,402,318,324,308],lipsLowerSemiOuter:[76,77,90,180,85,16,315,404,320,307,306],lipsUpperSemiOuter:[184,74,73,72,11,302,303,304,408],lipsLowerSemiInner:[62,96,89,179,86,15,316,403,319,325,292],lipsUpperSemiInner:[183,42,41,38,12,268,271,272,407],rightEyeUpper0:[246,161,160,159,158,157,173],rightEyeLower0:[33,7,163,144,145,153,154,155,133],rightEyeUpper1:[247,30,29,27,28,56,190],rightEyeLower1:[130,25,110,24,23,22,26,112,243],rightEyeUpper2:[113,225,224,223,222,221,189],rightEyeLower2:[226,31,228,229,230,231,232,233,244],rightEyeLower3:[143,111,117,118,119,120,121,128,245],rightEyebrowUpper:[156,70,63,105,66,107,55,193],rightEyebrowLower:[35,124,46,53,52,65],rightEyeIris:[473,474,475,476,477],leftEyeUpper0:[466,388,387,386,385,384,398],leftEyeLower0:[263,249,390,373,374,380,381,382,362],leftEyeUpper1:[467,260,259,257,258,286,414],leftEyeLower1:[359,255,339,254,253,252,256,341,463],leftEyeUpper2:[342,445,444,443,442,441,413],leftEyeLower2:[446,261,448,449,450,451,452,453,464],leftEyeLower3:[372,340,346,347,348,349,350,357,465],leftEyebrowUpper:[383,300,293,334,296,336,285,417],leftEyebrowLower:[265,353,276,283,282,295],leftEyeIris:[468,469,470,471,472],midwayBetweenEyes:[168],noseTip:[1],noseBottom:[2],noseRightCorner:[98],noseLeftCorner:[327],rightCheek:[205],leftCheek:[425]},ut={count:468,mouth:13,symmetryLine:[13,F0.midwayBetweenEyes[0]]},ve={leftEye:0,rightEye:1,nose:2,mouth:3,leftEar:4,rightEar:5,symmetryLine:[3,2]},ht=[{key:"EyeUpper0",indices:[9,10,11,12,13,14,15]},{key:"EyeUpper1",indices:[25,26,27,28,29,30,31]},{key:"EyeUpper2",indices:[41,42,43,44,45,46,47]},{key:"EyeLower0",indices:[0,1,2,3,4,5,6,7,8]},{key:"EyeLower1",indices:[16,17,18,19,20,21,22,23,24]},{key:"EyeLower2",indices:[32,33,34,35,36,37,38,39,40]},{key:"EyeLower3",indices:[54,55,56,57,58,59,60,61,62]},{key:"EyebrowUpper",indices:[63,64,65,66,67,68,69,70]},{key:"EyebrowLower",indices:[48,49,50,51,52,53]}],t2=[[.499976992607117,.652534008026123],[.500025987625122,.547487020492554],[.499974012374878,.602371990680695],[.482113003730774,.471979022026062],[.500150978565216,.527155995368958],[.499909996986389,.498252987861633],[.499523013830185,.40106201171875],[.289712011814117,.380764007568359],[.499954998493195,.312398016452789],[.499987006187439,.269918978214264],[.500023007392883,.107050001621246],[.500023007392883,.666234016418457],[.5000159740448,.679224014282227],[.500023007392883,.692348003387451],[.499976992607117,.695277988910675],[.499976992607117,.70593398809433],[.499976992607117,.719385027885437],[.499976992607117,.737019002437592],[.499967992305756,.781370997428894],[.499816000461578,.562981009483337],[.473773002624512,.573909997940063],[.104906998574734,.254140973091125],[.365929991006851,.409575998783112],[.338757991790771,.41302502155304],[.311120003461838,.409460008144379],[.274657994508743,.389131009578705],[.393361985683441,.403706014156342],[.345234006643295,.344011008739471],[.370094001293182,.346076011657715],[.319321990013123,.347265005111694],[.297903001308441,.353591024875641],[.24779200553894,.410809993743896],[.396889001131058,.842755019664764],[.280097991228104,.375599980354309],[.106310002505779,.399955987930298],[.2099249958992,.391353011131287],[.355807989835739,.534406006336212],[.471751004457474,.65040397644043],[.474155008792877,.680191993713379],[.439785003662109,.657229006290436],[.414617002010345,.66654098033905],[.450374007225037,.680860996246338],[.428770989179611,.682690978050232],[.374971002340317,.727805018424988],[.486716985702515,.547628998756409],[.485300987958908,.527395009994507],[.257764995098114,.314490020275116],[.401223003864288,.455172002315521],[.429818987846375,.548614978790283],[.421351999044418,.533740997314453],[.276895999908447,.532056987285614],[.483370006084442,.499586999416351],[.33721199631691,.282882988452911],[.296391993761063,.293242990970612],[.169294998049736,.193813979625702],[.447580009698868,.302609980106354],[.392390012741089,.353887975215912],[.354490011930466,.696784019470215],[.067304998636246,.730105042457581],[.442739009857178,.572826027870178],[.457098007202148,.584792017936707],[.381974011659622,.694710969924927],[.392388999462128,.694203019142151],[.277076005935669,.271932005882263],[.422551989555359,.563233017921448],[.385919004678726,.281364023685455],[.383103013038635,.255840003490448],[.331431001424789,.119714021682739],[.229923993349075,.232002973556519],[.364500999450684,.189113974571228],[.229622006416321,.299540996551514],[.173287004232407,.278747975826263],[.472878992557526,.666198015213013],[.446828007698059,.668527007102966],[.422762006521225,.673889994621277],[.445307999849319,.580065965652466],[.388103008270264,.693961024284363],[.403039008378983,.706539988517761],[.403629004955292,.693953037261963],[.460041999816895,.557139039039612],[.431158006191254,.692366003990173],[.452181994915009,.692366003990173],[.475387006998062,.692366003990173],[.465828001499176,.779190003871918],[.472328990697861,.736225962638855],[.473087012767792,.717857003211975],[.473122000694275,.704625964164734],[.473033010959625,.695277988910675],[.427942007780075,.695277988910675],[.426479011774063,.703539967536926],[.423162013292313,.711845993995667],[.4183090031147,.720062971115112],[.390094995498657,.639572978019714],[.013953999616206,.560034036636353],[.499913990497589,.58014702796936],[.413199990987778,.69539999961853],[.409626007080078,.701822996139526],[.468080013990402,.601534962654114],[.422728985548019,.585985004901886],[.463079988956451,.593783974647522],[.37211999297142,.47341400384903],[.334562003612518,.496073007583618],[.411671012639999,.546965003013611],[.242175996303558,.14767599105835],[.290776997804642,.201445996761322],[.327338010072708,.256527006626129],[.399509996175766,.748921036720276],[.441727995872498,.261676013469696],[.429764986038208,.187834024429321],[.412198007106781,.108901023864746],[.288955003023148,.398952007293701],[.218936994671822,.435410976409912],[.41278201341629,.398970007896423],[.257135003805161,.355440020561218],[.427684992551804,.437960982322693],[.448339998722076,.536936044692993],[.178560003638268,.45755398273468],[.247308000922203,.457193970680237],[.286267012357712,.467674970626831],[.332827985286713,.460712015628815],[.368755996227264,.447206974029541],[.398963987827301,.432654976844788],[.476410001516342,.405806005001068],[.189241006970406,.523923993110657],[.228962004184723,.348950982093811],[.490725994110107,.562400996685028],[.404670000076294,.485132992267609],[.019469000399113,.401564002037048],[.426243007183075,.420431017875671],[.396993011236191,.548797011375427],[.266469985246658,.376977026462555],[.439121007919312,.51895797252655],[.032313998788595,.644356966018677],[.419054001569748,.387154996395111],[.462783008813858,.505746960639954],[.238978996872902,.779744982719421],[.198220998048782,.831938028335571],[.107550002634525,.540755033493042],[.183610007166862,.740257024765015],[.134409993886948,.333683013916016],[.385764002799988,.883153975009918],[.490967005491257,.579378008842468],[.382384985685349,.508572995662689],[.174399003386497,.397670984268188],[.318785011768341,.39623498916626],[.343364000320435,.400596976280212],[.396100014448166,.710216999053955],[.187885001301765,.588537991046906],[.430987000465393,.944064974784851],[.318993002176285,.898285031318665],[.266247987747192,.869701027870178],[.500023007392883,.190576016902924],[.499976992607117,.954452991485596],[.366169989109039,.398822009563446],[.393207013607025,.39553701877594],[.410373002290726,.391080021858215],[.194993004202843,.342101991176605],[.388664990663528,.362284004688263],[.365961998701096,.355970978736877],[.343364000320435,.355356991291046],[.318785011768341,.35834002494812],[.301414996385574,.363156020641327],[.058132998645306,.319076001644135],[.301414996385574,.387449026107788],[.499987989664078,.618434011936188],[.415838003158569,.624195992946625],[.445681989192963,.566076993942261],[.465844005346298,.620640993118286],[.49992299079895,.351523995399475],[.288718998432159,.819945991039276],[.335278987884521,.852819979190826],[.440512001514435,.902418971061707],[.128294005990028,.791940987110138],[.408771991729736,.373893976211548],[.455606997013092,.451801002025604],[.499877005815506,.908990025520325],[.375436991453171,.924192011356354],[.11421000212431,.615022003650665],[.448662012815475,.695277988910675],[.4480200111866,.704632043838501],[.447111994028091,.715808033943176],[.444831997156143,.730794012546539],[.430011987686157,.766808986663818],[.406787008047104,.685672998428345],[.400738000869751,.681069016456604],[.392399996519089,.677703022956848],[.367855995893478,.663918972015381],[.247923001646996,.601333022117615],[.452769994735718,.420849978923798],[.43639200925827,.359887003898621],[.416164010763168,.368713974952698],[.413385987281799,.692366003990173],[.228018000721931,.683571994304657],[.468268007040024,.352671027183533],[.411361992359161,.804327011108398],[.499989002943039,.469825029373169],[.479153990745544,.442654013633728],[.499974012374878,.439637005329132],[.432112008333206,.493588984012604],[.499886006116867,.866917014122009],[.49991300702095,.821729004383087],[.456548988819122,.819200992584229],[.344549000263214,.745438992977142],[.37890899181366,.574010014533997],[.374292999505997,.780184984207153],[.319687992334366,.570737957954407],[.357154995203018,.604269981384277],[.295284003019333,.621580958366394],[.447750002145767,.862477004528046],[.410986006259918,.508723020553589],[.31395098567009,.775308012962341],[.354128003120422,.812552988529205],[.324548006057739,.703992962837219],[.189096003770828,.646299958229065],[.279776990413666,.71465802192688],[.1338230073452,.682700991630554],[.336768001317978,.644733011722565],[.429883986711502,.466521978378296],[.455527991056442,.548622965812683],[.437114000320435,.558896005153656],[.467287987470627,.529924988746643],[.414712011814117,.335219979286194],[.37704598903656,.322777986526489],[.344107985496521,.320150971412659],[.312875986099243,.32233202457428],[.283526003360748,.333190023899078],[.241245999932289,.382785975933075],[.102986000478268,.468762993812561],[.267612010240555,.424560010433197],[.297879010438919,.433175981044769],[.333433985710144,.433878004550934],[.366427004337311,.426115989685059],[.396012008190155,.416696012020111],[.420121014118195,.41022801399231],[.007561000064015,.480777025222778],[.432949006557465,.569517970085144],[.458638995885849,.479089021682739],[.473466008901596,.545744001865387],[.476087987422943,.563830018043518],[.468472003936768,.555056989192963],[.433990985155106,.582361996173859],[.483518004417419,.562983989715576],[.482482999563217,.57784903049469],[.42645001411438,.389798998832703],[.438998997211456,.39649498462677],[.450067013502121,.400434017181396],[.289712011814117,.368252992630005],[.276670008897781,.363372981548309],[.517862021923065,.471948027610779],[.710287988185883,.380764007568359],[.526226997375488,.573909997940063],[.895093023777008,.254140973091125],[.634069979190826,.409575998783112],[.661242008209229,.41302502155304],[.688880026340485,.409460008144379],[.725341975688934,.389131009578705],[.606630027294159,.40370500087738],[.654766023159027,.344011008739471],[.629905998706818,.346076011657715],[.680678009986877,.347265005111694],[.702096998691559,.353591024875641],[.75221198797226,.410804986953735],[.602918028831482,.842862963676453],[.719901978969574,.375599980354309],[.893692970275879,.399959981441498],[.790081977844238,.391354024410248],[.643998026847839,.534487962722778],[.528249025344849,.65040397644043],[.525849997997284,.680191040039062],[.560214996337891,.657229006290436],[.585384011268616,.66654098033905],[.549625992774963,.680860996246338],[.57122802734375,.682691991329193],[.624852001667023,.72809898853302],[.513050019741058,.547281980514526],[.51509702205658,.527251958847046],[.742246985435486,.314507007598877],[.598631024360657,.454979002475739],[.570338010787964,.548575043678284],[.578631997108459,.533622980117798],[.723087012767792,.532054007053375],[.516445994377136,.499638974666595],[.662801027297974,.282917976379395],[.70362401008606,.293271005153656],[.830704987049103,.193813979625702],[.552385985851288,.302568018436432],[.607609987258911,.353887975215912],[.645429015159607,.696707010269165],[.932694971561432,.730105042457581],[.557260990142822,.572826027870178],[.542901992797852,.584792017936707],[.6180260181427,.694710969924927],[.607590973377228,.694203019142151],[.722943007946014,.271963000297546],[.577413976192474,.563166975975037],[.614082992076874,.281386971473694],[.616907000541687,.255886018276215],[.668509006500244,.119913995265961],[.770092010498047,.232020974159241],[.635536015033722,.189248979091644],[.77039098739624,.299556016921997],[.826722025871277,.278755009174347],[.527121007442474,.666198015213013],[.553171992301941,.668527007102966],[.577238023281097,.673889994621277],[.554691970348358,.580065965652466],[.611896991729736,.693961024284363],[.59696102142334,.706539988517761],[.596370995044708,.693953037261963],[.539958000183105,.557139039039612],[.568841993808746,.692366003990173],[.547818005084991,.692366003990173],[.52461302280426,.692366003990173],[.534089982509613,.779141008853912],[.527670979499817,.736225962638855],[.526912987232208,.717857003211975],[.526877999305725,.704625964164734],[.526966989040375,.695277988910675],[.572058022022247,.695277988910675],[.573521018028259,.703539967536926],[.57683801651001,.711845993995667],[.581691026687622,.720062971115112],[.609944999217987,.639909982681274],[.986046016216278,.560034036636353],[.5867999792099,.69539999961853],[.590372025966644,.701822996139526],[.531915009021759,.601536989212036],[.577268004417419,.585934996604919],[.536915004253387,.593786001205444],[.627542972564697,.473352015018463],[.665585994720459,.495950996875763],[.588353991508484,.546862006187439],[.757824003696442,.14767599105835],[.709249973297119,.201507985591888],[.672684013843536,.256581008434296],[.600408971309662,.74900496006012],[.55826598405838,.261672019958496],[.570303976535797,.187870979309082],[.588165998458862,.109044015407562],[.711045026779175,.398952007293701],[.781069993972778,.435405015945435],[.587247014045715,.398931980133057],[.742869973182678,.355445981025696],[.572156012058258,.437651991844177],[.55186802148819,.536570012569427],[.821442008018494,.457556009292603],[.752701997756958,.457181990146637],[.71375697851181,.467626988887787],[.66711300611496,.460672974586487],[.631101012229919,.447153985500336],[.6008620262146,.432473003864288],[.523481011390686,.405627012252808],[.810747981071472,.523926019668579],[.771045982837677,.348959028720856],[.509127020835876,.562718033790588],[.595292985439301,.485023975372314],[.980530977249146,.401564002037048],[.573499977588654,.420000016689301],[.602994978427887,.548687994480133],[.733529984951019,.376977026462555],[.560611009597778,.519016981124878],[.967685997486115,.644356966018677],[.580985009670258,.387160003185272],[.537728011608124,.505385041236877],[.760966002941132,.779752969741821],[.801778972148895,.831938028335571],[.892440974712372,.54076099395752],[.816350996494293,.740260004997253],[.865594983100891,.333687007427216],[.614073991775513,.883246004581451],[.508952975273132,.579437971115112],[.617941975593567,.508316040039062],[.825608015060425,.397674977779388],[.681214988231659,.39623498916626],[.656635999679565,.400596976280212],[.603900015354156,.710216999053955],[.81208598613739,.588539004325867],[.56801301240921,.944564998149872],[.681007981300354,.898285031318665],[.733752012252808,.869701027870178],[.633830010890961,.398822009563446],[.606792986392975,.39553701877594],[.589659988880157,.391062021255493],[.805015981197357,.342108011245728],[.611334979534149,.362284004688263],[.634037971496582,.355970978736877],[.656635999679565,.355356991291046],[.681214988231659,.35834002494812],[.698584973812103,.363156020641327],[.941866993904114,.319076001644135],[.698584973812103,.387449026107788],[.584177017211914,.624107003211975],[.554318010807037,.566076993942261],[.534153997898102,.62064003944397],[.711217999458313,.819975018501282],[.664629995822906,.852871000766754],[.559099972248077,.902631998062134],[.871706008911133,.791940987110138],[.591234028339386,.373893976211548],[.544341027736664,.451583981513977],[.624562978744507,.924192011356354],[.88577002286911,.615028977394104],[.551338016986847,.695277988910675],[.551980018615723,.704632043838501],[.552887976169586,.715808033943176],[.555167973041534,.730794012546539],[.569944024085999,.767035007476807],[.593203008174896,.685675978660583],[.599261999130249,.681069016456604],[.607599973678589,.677703022956848],[.631937980651855,.663500010967255],[.752032995223999,.601315021514893],[.547226011753082,.420395016670227],[.563543975353241,.359827995300293],[.583841025829315,.368713974952698],[.586614012718201,.692366003990173],[.771915018558502,.683578014373779],[.531597018241882,.352482974529266],[.588370978832245,.804440975189209],[.52079701423645,.442565023899078],[.567984998226166,.493479013442993],[.543282985687256,.819254994392395],[.655317008495331,.745514988899231],[.621008992195129,.574018001556396],[.625559985637665,.78031200170517],[.680198013782501,.570719003677368],[.64276397228241,.604337990283966],[.704662978649139,.621529996395111],[.552012026309967,.862591981887817],[.589071989059448,.508637011051178],[.685944974422455,.775357007980347],[.645735025405884,.812640011310577],[.675342977046967,.703978002071381],[.810858011245728,.646304965019226],[.72012197971344,.714666962623596],[.866151988506317,.682704985141754],[.663187026977539,.644596993923187],[.570082008838654,.466325998306274],[.544561982154846,.548375964164734],[.562758982181549,.558784961700439],[.531987011432648,.530140042304993],[.585271000862122,.335177004337311],[.622952997684479,.32277899980545],[.655896008014679,.320163011550903],[.687132000923157,.322345972061157],[.716481983661652,.333200991153717],[.758756995201111,.382786989212036],[.897013008594513,.468769013881683],[.732392013072968,.424547016620636],[.70211398601532,.433162987232208],[.66652500629425,.433866024017334],[.633504986763,.426087975502014],[.603875994682312,.416586995124817],[.579657971858978,.409945011138916],[.992439985275269,.480777025222778],[.567192018032074,.569419980049133],[.54136598110199,.478899002075195],[.526564002037048,.546118021011353],[.523913025856018,.563830018043518],[.531529009342194,.555056989192963],[.566035985946655,.582329034805298],[.51631098985672,.563053965568542],[.5174720287323,.577877044677734],[.573594987392426,.389806985855103],[.560697972774506,.395331978797913],[.549755990505219,.399751007556915],[.710287988185883,.368252992630005],[.723330020904541,.363372981548309]],Re=[127,34,139,11,0,37,232,231,120,72,37,39,128,121,47,232,121,128,104,69,67,175,171,148,157,154,155,118,50,101,73,39,40,9,151,108,48,115,131,194,204,211,74,40,185,80,42,183,40,92,186,230,229,118,202,212,214,83,18,17,76,61,146,160,29,30,56,157,173,106,204,194,135,214,192,203,165,98,21,71,68,51,45,4,144,24,23,77,146,91,205,50,187,201,200,18,91,106,182,90,91,181,85,84,17,206,203,36,148,171,140,92,40,39,193,189,244,159,158,28,247,246,161,236,3,196,54,68,104,193,168,8,117,228,31,189,193,55,98,97,99,126,47,100,166,79,218,155,154,26,209,49,131,135,136,150,47,126,217,223,52,53,45,51,134,211,170,140,67,69,108,43,106,91,230,119,120,226,130,247,63,53,52,238,20,242,46,70,156,78,62,96,46,53,63,143,34,227,173,155,133,123,117,111,44,125,19,236,134,51,216,206,205,154,153,22,39,37,167,200,201,208,36,142,100,57,212,202,20,60,99,28,158,157,35,226,113,160,159,27,204,202,210,113,225,46,43,202,204,62,76,77,137,123,116,41,38,72,203,129,142,64,98,240,49,102,64,41,73,74,212,216,207,42,74,184,169,170,211,170,149,176,105,66,69,122,6,168,123,147,187,96,77,90,65,55,107,89,90,180,101,100,120,63,105,104,93,137,227,15,86,85,129,102,49,14,87,86,55,8,9,100,47,121,145,23,22,88,89,179,6,122,196,88,95,96,138,172,136,215,58,172,115,48,219,42,80,81,195,3,51,43,146,61,171,175,199,81,82,38,53,46,225,144,163,110,246,33,7,52,65,66,229,228,117,34,127,234,107,108,69,109,108,151,48,64,235,62,78,191,129,209,126,111,35,143,163,161,246,117,123,50,222,65,52,19,125,141,221,55,65,3,195,197,25,7,33,220,237,44,70,71,139,122,193,245,247,130,33,71,21,162,153,158,159,170,169,150,188,174,196,216,186,92,144,160,161,2,97,167,141,125,241,164,167,37,72,38,12,145,159,160,38,82,13,63,68,71,226,35,111,158,153,154,101,50,205,206,92,165,209,198,217,165,167,97,220,115,218,133,112,243,239,238,241,214,135,169,190,173,133,171,208,32,125,44,237,86,87,178,85,86,179,84,85,180,83,84,181,201,83,182,137,93,132,76,62,183,61,76,184,57,61,185,212,57,186,214,207,187,34,143,156,79,239,237,123,137,177,44,1,4,201,194,32,64,102,129,213,215,138,59,166,219,242,99,97,2,94,141,75,59,235,24,110,228,25,130,226,23,24,229,22,23,230,26,22,231,112,26,232,189,190,243,221,56,190,28,56,221,27,28,222,29,27,223,30,29,224,247,30,225,238,79,20,166,59,75,60,75,240,147,177,215,20,79,166,187,147,213,112,233,244,233,128,245,128,114,188,114,217,174,131,115,220,217,198,236,198,131,134,177,132,58,143,35,124,110,163,7,228,110,25,356,389,368,11,302,267,452,350,349,302,303,269,357,343,277,452,453,357,333,332,297,175,152,377,384,398,382,347,348,330,303,304,270,9,336,337,278,279,360,418,262,431,304,408,409,310,415,407,270,409,410,450,348,347,422,430,434,313,314,17,306,307,375,387,388,260,286,414,398,335,406,418,364,367,416,423,358,327,251,284,298,281,5,4,373,374,253,307,320,321,425,427,411,421,313,18,321,405,406,320,404,405,315,16,17,426,425,266,377,400,369,322,391,269,417,465,464,386,257,258,466,260,388,456,399,419,284,332,333,417,285,8,346,340,261,413,441,285,327,460,328,355,371,329,392,439,438,382,341,256,429,420,360,364,394,379,277,343,437,443,444,283,275,440,363,431,262,369,297,338,337,273,375,321,450,451,349,446,342,467,293,334,282,458,461,462,276,353,383,308,324,325,276,300,293,372,345,447,382,398,362,352,345,340,274,1,19,456,248,281,436,427,425,381,256,252,269,391,393,200,199,428,266,330,329,287,273,422,250,462,328,258,286,384,265,353,342,387,259,257,424,431,430,342,353,276,273,335,424,292,325,307,366,447,345,271,303,302,423,266,371,294,455,460,279,278,294,271,272,304,432,434,427,272,407,408,394,430,431,395,369,400,334,333,299,351,417,168,352,280,411,325,319,320,295,296,336,319,403,404,330,348,349,293,298,333,323,454,447,15,16,315,358,429,279,14,15,316,285,336,9,329,349,350,374,380,252,318,402,403,6,197,419,318,319,325,367,364,365,435,367,397,344,438,439,272,271,311,195,5,281,273,287,291,396,428,199,311,271,268,283,444,445,373,254,339,263,466,249,282,334,296,449,347,346,264,447,454,336,296,299,338,10,151,278,439,455,292,407,415,358,371,355,340,345,372,390,249,466,346,347,280,442,443,282,19,94,370,441,442,295,248,419,197,263,255,359,440,275,274,300,383,368,351,412,465,263,467,466,301,368,389,380,374,386,395,378,379,412,351,419,436,426,322,373,390,388,2,164,393,370,462,461,164,0,267,302,11,12,374,373,387,268,12,13,293,300,301,446,261,340,385,384,381,330,266,425,426,423,391,429,355,437,391,327,326,440,457,438,341,382,362,459,457,461,434,430,394,414,463,362,396,369,262,354,461,457,316,403,402,315,404,403,314,405,404,313,406,405,421,418,406,366,401,361,306,408,407,291,409,408,287,410,409,432,436,410,434,416,411,264,368,383,309,438,457,352,376,401,274,275,4,421,428,262,294,327,358,433,416,367,289,455,439,462,370,326,2,326,370,305,460,455,254,449,448,255,261,446,253,450,449,252,451,450,256,452,451,341,453,452,413,464,463,441,413,414,258,442,441,257,443,442,259,444,443,260,445,444,467,342,445,459,458,250,289,392,290,290,328,460,376,433,435,250,290,392,411,416,433,341,463,464,453,464,465,357,465,412,343,412,399,360,363,440,437,399,456,420,456,363,401,435,288,372,383,353,339,255,249,448,261,255,133,243,190,133,155,112,33,246,247,33,130,25,398,384,286,362,398,414,362,463,341,263,359,467,263,249,255,466,467,260,75,60,166,238,239,79,162,127,139,72,11,37,121,232,120,73,72,39,114,128,47,233,232,128,103,104,67,152,175,148,173,157,155,119,118,101,74,73,40,107,9,108,49,48,131,32,194,211,184,74,185,191,80,183,185,40,186,119,230,118,210,202,214,84,83,17,77,76,146,161,160,30,190,56,173,182,106,194,138,135,192,129,203,98,54,21,68,5,51,4,145,144,23,90,77,91,207,205,187,83,201,18,181,91,182,180,90,181,16,85,17,205,206,36,176,148,140,165,92,39,245,193,244,27,159,28,30,247,161,174,236,196,103,54,104,55,193,8,111,117,31,221,189,55,240,98,99,142,126,100,219,166,218,112,155,26,198,209,131,169,135,150,114,47,217,224,223,53,220,45,134,32,211,140,109,67,108,146,43,91,231,230,120,113,226,247,105,63,52,241,238,242,124,46,156,95,78,96,70,46,63,116,143,227,116,123,111,1,44,19,3,236,51,207,216,205,26,154,22,165,39,167,199,200,208,101,36,100,43,57,202,242,20,99,56,28,157,124,35,113,29,160,27,211,204,210,124,113,46,106,43,204,96,62,77,227,137,116,73,41,72,36,203,142,235,64,240,48,49,64,42,41,74,214,212,207,183,42,184,210,169,211,140,170,176,104,105,69,193,122,168,50,123,187,89,96,90,66,65,107,179,89,180,119,101,120,68,63,104,234,93,227,16,15,85,209,129,49,15,14,86,107,55,9,120,100,121,153,145,22,178,88,179,197,6,196,89,88,96,135,138,136,138,215,172,218,115,219,41,42,81,5,195,51,57,43,61,208,171,199,41,81,38,224,53,225,24,144,110,105,52,66,118,229,117,227,34,234,66,107,69,10,109,151,219,48,235,183,62,191,142,129,126,116,111,143,7,163,246,118,117,50,223,222,52,94,19,141,222,221,65,196,3,197,45,220,44,156,70,139,188,122,245,139,71,162,145,153,159,149,170,150,122,188,196,206,216,92,163,144,161,164,2,167,242,141,241,0,164,37,11,72,12,144,145,160,12,38,13,70,63,71,31,226,111,157,158,154,36,101,205,203,206,165,126,209,217,98,165,97,237,220,218,237,239,241,210,214,169,140,171,32,241,125,237,179,86,178,180,85,179,181,84,180,182,83,181,194,201,182,177,137,132,184,76,183,185,61,184,186,57,185,216,212,186,192,214,187,139,34,156,218,79,237,147,123,177,45,44,4,208,201,32,98,64,129,192,213,138,235,59,219,141,242,97,97,2,141,240,75,235,229,24,228,31,25,226,230,23,229,231,22,230,232,26,231,233,112,232,244,189,243,189,221,190,222,28,221,223,27,222,224,29,223,225,30,224,113,247,225,99,60,240,213,147,215,60,20,166,192,187,213,243,112,244,244,233,245,245,128,188,188,114,174,134,131,220,174,217,236,236,198,134,215,177,58,156,143,124,25,110,7,31,228,25,264,356,368,0,11,267,451,452,349,267,302,269,350,357,277,350,452,357,299,333,297,396,175,377,381,384,382,280,347,330,269,303,270,151,9,337,344,278,360,424,418,431,270,304,409,272,310,407,322,270,410,449,450,347,432,422,434,18,313,17,291,306,375,259,387,260,424,335,418,434,364,416,391,423,327,301,251,298,275,281,4,254,373,253,375,307,321,280,425,411,200,421,18,335,321,406,321,320,405,314,315,17,423,426,266,396,377,369,270,322,269,413,417,464,385,386,258,248,456,419,298,284,333,168,417,8,448,346,261,417,413,285,326,327,328,277,355,329,309,392,438,381,382,256,279,429,360,365,364,379,355,277,437,282,443,283,281,275,363,395,431,369,299,297,337,335,273,321,348,450,349,359,446,467,283,293,282,250,458,462,300,276,383,292,308,325,283,276,293,264,372,447,346,352,340,354,274,19,363,456,281,426,436,425,380,381,252,267,269,393,421,200,428,371,266,329,432,287,422,290,250,328,385,258,384,446,265,342,386,387,257,422,424,430,445,342,276,422,273,424,306,292,307,352,366,345,268,271,302,358,423,371,327,294,460,331,279,294,303,271,304,436,432,427,304,272,408,395,394,431,378,395,400,296,334,299,6,351,168,376,352,411,307,325,320,285,295,336,320,319,404,329,330,349,334,293,333,366,323,447,316,15,315,331,358,279,317,14,316,8,285,9,277,329,350,253,374,252,319,318,403,351,6,419,324,318,325,397,367,365,288,435,397,278,344,439,310,272,311,248,195,281,375,273,291,175,396,199,312,311,268,276,283,445,390,373,339,295,282,296,448,449,346,356,264,454,337,336,299,337,338,151,294,278,455,308,292,415,429,358,355,265,340,372,388,390,466,352,346,280,295,442,282,354,19,370,285,441,295,195,248,197,457,440,274,301,300,368,417,351,465,251,301,389,385,380,386,394,395,379,399,412,419,410,436,322,387,373,388,326,2,393,354,370,461,393,164,267,268,302,12,386,374,387,312,268,13,298,293,301,265,446,340,380,385,381,280,330,425,322,426,391,420,429,437,393,391,326,344,440,438,458,459,461,364,434,394,428,396,262,274,354,457,317,316,402,316,315,403,315,314,404,314,313,405,313,421,406,323,366,361,292,306,407,306,291,408,291,287,409,287,432,410,427,434,411,372,264,383,459,309,457,366,352,401,1,274,4,418,421,262,331,294,358,435,433,367,392,289,439,328,462,326,94,2,370,289,305,455,339,254,448,359,255,446,254,253,449,253,252,450,252,256,451,256,341,452,414,413,463,286,441,414,286,258,441,258,257,442,257,259,443,259,260,444,260,467,445,309,459,250,305,289,290,305,290,460,401,376,435,309,250,392,376,411,433,453,341,464,357,453,465,343,357,412,437,343,399,344,360,440,420,437,456,360,420,363,361,401,288,265,372,353,390,339,249,339,448,255];var Mr=[127,234,132,58,172,150,149,148,152,377,378,379,397,288,361,454,356,70,63,105,66,107,336,296,334,293,300,168,6,195,4,98,97,2,326,327,33,160,158,133,153,144,362,385,387,263,373,380,57,40,37,0,267,270,287,321,314,17,84,91,78,81,13,311,308,402,14,178],Pr=[33,133,362,263,1,62,308,159,145,386,374,6,102,331,2,13,14,70,105,107,336,334,300,54,10,284,50,280,234,454,58,288,152],kr=[33,133,362,263,1,78,308],JA=Mr.map(e=>t2[e]),QA=Pr.map(e=>t2[e]),_A=kr.map(e=>t2[e]);function se(e){let t=e.map(n=>n[0]);return t.push(e[e.length-1][1]),t}var wr=[[61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]],Er=[[263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]],zr=[[276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]],Sr=[[474,475],[475,476],[476,477],[477,474]],jr=[[33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]],Nr=[[46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]],Ir=[[469,470],[470,471],[471,472],[472,469]],Lr=[[10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]],$A={lips:se(wr),leftEye:se(Er),leftEyebrow:se(zr),leftIris:se(Sr),rightEye:se(jr),rightEyebrow:se(Nr),rightIris:se(Ir),faceOval:se(Lr)};var Or=[[61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]],Cr=[[263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]],Wr=[[276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]],Dr=[[474,475],[475,476],[476,477],[477,474]],Fr=[[33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]],Br=[[46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]],Hr=[[469,470],[470,471],[471,472],[472,469]],Gr=[[10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]];function Ae(e){let t=e.map(n=>n[0]);return t.push(e[e.length-1][1]),t}var Vr={lips:Ae(Or),leftEye:Ae(Cr),leftEyebrow:Ae(Wr),leftIris:Ae(Dr),rightEye:Ae(Fr),rightEyebrow:Ae(Br),rightIris:Ae(Hr),faceOval:Ae(Gr)},Zr=Object.entries(Vr).map(([e,t])=>t.map(n=>[n,e])).flat(),ea=new Map(Zr),n2=[61,146,91,181,84,17,314,405,321,375,291,185,40,39,37,0,267,269,270,409,78,95,88,178,87,14,317,402,318,324,308,191,80,81,82,13,312,311,310,415,76,77,90,180,85,16,315,404,320,307,306,184,74,73,72,11,302,303,304,408,62,96,89,179,86,15,316,403,319,325,292,183,42,41,38,12,268,271,272,407],Me=[33,7,163,144,145,153,154,155,133,246,161,160,159,158,157,173,130,25,110,24,23,22,26,112,243,247,30,29,27,28,56,190,226,31,228,229,230,231,232,233,244,113,225,224,223,222,221,189,35,124,46,53,52,65,143,111,117,118,119,120,121,128,245,156,70,63,105,66,107,55,193],Pe=[263,249,390,373,374,380,381,382,362,466,388,387,386,385,384,398,359,255,339,254,253,252,256,341,463,467,260,259,257,258,286,414,446,261,448,449,450,451,452,453,464,342,445,444,443,442,441,413,265,353,276,283,282,295,372,340,346,347,348,349,350,357,465,383,300,293,334,296,336,285,417];var B;function Xr(e,t){var o,s,A,a,i,c,d,x,l;if(!B.drawLabels||((o=B.faceLabels)==null?void 0:o.length)===0)return;let n=B.faceLabels.slice();if(n=H(n,"[id]",e.id.toFixed(0)),e.score&&(n=H(n,"[score]",100*e.score)),e.gender&&(n=H(n,"[gender]",e.gender)),e.genderScore&&(n=H(n,"[genderScore]",100*e.genderScore)),e.age&&(n=H(n,"[age]",e.age)),e.distance&&(n=H(n,"[distance]",100*e.distance)),e.real&&(n=H(n,"[real]",100*e.real)),e.live&&(n=H(n,"[live]",100*e.live)),e.emotion&&e.emotion.length>0){let f=e.emotion.map(y=>`${Math.trunc(100*y.score)}% ${y.emotion}`);f.length>3&&(f.length=3),n=H(n,"[emotions]",f.join(" "))}(A=(s=e.rotation)==null?void 0:s.angle)!=null&&A.roll&&(n=H(n,"[roll]",ge(e.rotation.angle.roll))),(i=(a=e.rotation)==null?void 0:a.angle)!=null&&i.yaw&&(n=H(n,"[yaw]",ge(e.rotation.angle.yaw))),(d=(c=e.rotation)==null?void 0:c.angle)!=null&&d.pitch&&(n=H(n,"[pitch]",ge(e.rotation.angle.pitch))),(l=(x=e.rotation)==null?void 0:x.gaze)!=null&&l.bearing&&(n=H(n,"[gaze]",ge(e.rotation.gaze.bearing))),C0(t,n,e.box[0],e.box[1],B)}function qr(e,t){var n,o,s,A;if((n=e.annotations)!=null&&n.leftEyeIris&&((o=e.annotations)!=null&&o.leftEyeIris[0])){t.strokeStyle=B.useDepth?"rgba(255, 200, 255, 0.3)":B.color,t.beginPath();let a=Math.abs(e.annotations.leftEyeIris[3][0]-e.annotations.leftEyeIris[1][0])/2,i=Math.abs(e.annotations.leftEyeIris[4][1]-e.annotations.leftEyeIris[2][1])/2;t.ellipse(e.annotations.leftEyeIris[0][0],e.annotations.leftEyeIris[0][1],a,i,0,0,2*Math.PI),t.stroke(),B.fillPolygons&&(t.fillStyle=B.useDepth?"rgba(255, 255, 200, 0.3)":B.color,t.fill())}if((s=e.annotations)!=null&&s.rightEyeIris&&((A=e.annotations)!=null&&A.rightEyeIris[0])){t.strokeStyle=B.useDepth?"rgba(255, 200, 255, 0.3)":B.color,t.beginPath();let a=Math.abs(e.annotations.rightEyeIris[3][0]-e.annotations.rightEyeIris[1][0])/2,i=Math.abs(e.annotations.rightEyeIris[4][1]-e.annotations.rightEyeIris[2][1])/2;t.ellipse(e.annotations.rightEyeIris[0][0],e.annotations.rightEyeIris[0][1],a,i,0,0,2*Math.PI),t.stroke(),B.fillPolygons&&(t.fillStyle=B.useDepth?"rgba(255, 255, 200, 0.3)":B.color,t.fill())}}function Ur(e,t){var n;if(B.drawGaze&&((n=e.rotation)!=null&&n.angle)&&typeof Path2D!="undefined"){t.strokeStyle="pink";let o=e.box[0]+e.box[2]/2-e.box[3]*ge(e.rotation.angle.yaw)/90,s=e.box[1]+e.box[3]/2+e.box[2]*ge(e.rotation.angle.pitch)/90,A=new Path2D(`
      M ${e.box[0]+e.box[2]/2} ${e.box[1]}
      C
        ${o} ${e.box[1]},
        ${o} ${e.box[1]+e.box[3]},
        ${e.box[0]+e.box[2]/2} ${e.box[1]+e.box[3]}
    `),a=new Path2D(`
      M ${e.box[0]} ${e.box[1]+e.box[3]/2}
      C 
        ${e.box[0]} ${s},
        ${e.box[0]+e.box[2]} ${s},
        ${e.box[0]+e.box[2]} ${e.box[1]+e.box[3]/2}
    `);t.stroke(a),t.stroke(A)}}function Yr(e,t){var n;if(B.drawGaze&&((n=e.rotation)!=null&&n.gaze.strength)&&e.rotation.gaze.bearing&&e.annotations.leftEyeIris&&e.annotations.rightEyeIris&&e.annotations.leftEyeIris[0]&&e.annotations.rightEyeIris[0]){t.strokeStyle="pink",t.fillStyle="pink";let o=[e.annotations.leftEyeIris[0][0]+Math.sin(e.rotation.gaze.bearing)*e.rotation.gaze.strength*e.box[3],e.annotations.leftEyeIris[0][1]+Math.cos(e.rotation.gaze.bearing)*e.rotation.gaze.strength*e.box[2]];pt(t,[e.annotations.leftEyeIris[0][0],e.annotations.leftEyeIris[0][1]],[o[0],o[1]],4);let s=[e.annotations.rightEyeIris[0][0]+Math.sin(e.rotation.gaze.bearing)*e.rotation.gaze.strength*e.box[3],e.annotations.rightEyeIris[0][1]+Math.cos(e.rotation.gaze.bearing)*e.rotation.gaze.strength*e.box[2]];pt(t,[e.annotations.rightEyeIris[0][0],e.annotations.rightEyeIris[0][1]],[s[0],s[1]],4)}}function Kr(e,t){if(B.drawPolygons&&e.mesh.length>=468){t.lineWidth=1;for(let n=0;n<Re.length/3;n++){let o=[Re[n*3+0],Re[n*3+1],Re[n*3+2]].map(s=>e.mesh[s]);mt(t,o,B)}qr(e,t)}}function Jr(e,t){if(B.drawPoints)if((e==null?void 0:e.mesh.length)>=468)for(let n=0;n<e.mesh.length;n++)U0(t,e.mesh[n][0],e.mesh[n][1],e.mesh[n][2],B),B.drawAttention&&(n2.includes(n)&&U0(t,e.mesh[n][0],e.mesh[n][1],e.mesh[n][2]+127,B),Me.includes(n)&&U0(t,e.mesh[n][0],e.mesh[n][1],e.mesh[n][2]-127,B),Pe.includes(n)&&U0(t,e.mesh[n][0],e.mesh[n][1],e.mesh[n][2]-127,B));else for(let[n,o]of Object.entries((e==null?void 0:e.annotations)||{})){if(!(o!=null&&o[0]))continue;let s=o[0];U0(t,s[0],s[1],0,B),B.drawLabels&&C0(t,n,s[0],s[1],B)}}function Qr(e,t){B.drawBoxes&&Y0(t,e.box[0],e.box[1],e.box[2],e.box[3],B)}function b2(e,t,n){if(B=Q(n0,n),!t||!e)return;let o=O0(e);if(o){o.font=B.font,o.strokeStyle=B.color,o.fillStyle=B.color;for(let s of t)Qr(s,o),Xr(s,o),s.mesh&&s.mesh.length>0&&(Jr(s,o),Kr(s,o),Ur(s,o),Yr(s,o))}}function g2(e,t,n){var A,a;let o=Q(n0,n);if(!t||!e)return;let s=O0(e);if(s){s.lineJoin="round";for(let i=0;i<t.length;i++){if(s.strokeStyle=o.color,s.fillStyle=o.color,s.lineWidth=o.lineWidth,s.font=o.font,o.drawBoxes&&t[i].box&&t[i].box.length===4&&(Y0(s,t[i].box[0],t[i].box[1],t[i].box[2],t[i].box[3],o),o.drawLabels&&((A=o.bodyLabels)==null?void 0:A.length)>0)){let c=o.bodyLabels.slice();c=H(c,"[id]",t[i].id.toFixed(0)),c=H(c,"[score]",100*t[i].score),C0(s,c,t[i].box[0],t[i].box[1],o)}if(o.drawPoints&&t[i].keypoints)for(let c=0;c<t[i].keypoints.length;c++)!t[i].keypoints[c].score||t[i].keypoints[c].score===0||(s.fillStyle=Te(t[i].keypoints[c].position[2],o),U0(s,t[i].keypoints[c].position[0],t[i].keypoints[c].position[1],0,o));if(o.drawLabels&&((a=o.bodyPartLabels)==null?void 0:a.length)>0&&t[i].keypoints){s.font=o.font;for(let c of t[i].keypoints){if(!c.score||c.score===0)continue;let d=o.bodyPartLabels.slice();d=H(d,"[label]",c.part),d=H(d,"[score]",100*c.score),C0(s,d,c.position[0],c.position[1],o)}}if(o.drawPolygons&&t[i].keypoints&&t[i].annotations)for(let c of Object.values(t[i].annotations))for(let d of c)c1(s,d,o)}}}function T2(e,t,n){var A,a;let o=Q(n0,n);if(!t||!e)return;let s=O0(e);if(s){s.lineJoin="round",s.font=o.font;for(let i of t){if(o.drawBoxes){if(s.strokeStyle=o.color,s.fillStyle=o.color,Y0(s,i.box[0],i.box[1],i.box[2],i.box[3],o),o.drawLabels&&((A=o.handLabels)==null?void 0:A.length)>0){let c=o.handLabels.slice();c=H(c,"[id]",i.id.toFixed(0)),c=H(c,"[label]",i.label),c=H(c,"[score]",100*i.score),C0(s,c,i.box[0],i.box[1],o)}s.stroke()}if(o.drawPoints&&i.keypoints&&i.keypoints.length>0)for(let c of i.keypoints)s.fillStyle=Te(c[2],o),U0(s,c[0],c[1],0,o);if(o.drawLabels&&i.annotations&&((a=o.fingerLabels)==null?void 0:a.length)>0)for(let[c,d]of Object.entries(i.annotations)){let x=o.fingerLabels.slice();x=H(x,"[label]",c),C0(s,x,d[d.length-1][0],d[d.length-1][1],o)}if(o.drawPolygons&&i.annotations){let c=d=>{if(!(!d||d.length===0||!d[0]))for(let x=0;x<d.length;x++){s.beginPath();let l=d[x][2]||0;s.strokeStyle=Te(x*l,o),s.moveTo(d[x>0?x-1:0][0],d[x>0?x-1:0][1]),s.lineTo(d[x][0],d[x][1]),s.stroke()}};s.lineWidth=o.lineWidth,c(i.annotations.index),c(i.annotations.middle),c(i.annotations.ring),c(i.annotations.pinky),c(i.annotations.thumb)}}}}function v2(e,t,n){var A;let o=Q(n0,n);if(!t||!e)return;let s=O0(e);if(s){s.lineJoin="round",s.font=o.font;for(let a of t)if(o.drawBoxes){if(s.strokeStyle=o.color,s.fillStyle=o.color,Y0(s,a.box[0],a.box[1],a.box[2],a.box[3],o),o.drawLabels&&((A=o.objectLabels)==null?void 0:A.length)>0){let i=o.objectLabels.slice();i=H(i,"[id]",a.id.toFixed(0)),i=H(i,"[label]",a.label),i=H(i,"[score]",100*a.score),C0(s,i,a.box[0],a.box[1],o)}s.stroke()}}}function R2(e,t,n){var s;let o=Q(n0,n);if(!(!t||!e)&&o.drawGestures&&((s=o.gestureLabels)==null?void 0:s.length)>0){let A=O0(e);if(!A)return;A.font=o.font,A.fillStyle=o.color;let a=1;for(let i=0;i<t.length;i++){let[c,d]=Object.entries(t[i]);if(d.length>1&&d[1].length>0){let x=c[1]>0?`#${c[1]}`:"",l=o.gestureLabels.slice();l=H(l,"[where]",c[0]),l=H(l,"[who]",x),l=H(l,"[what]",d[1]),C0(A,l,8,2+a*o.lineHeight,o),a+=1}}}}var ae={face:`face
    confidence: [score]%
    [gender] [genderScore]%
    age: [age] years
    distance: [distance]cm
    real: [real]%
    live: [live]%
    [emotions]
    roll: [roll]\xB0 yaw:[yaw]\xB0 pitch:[pitch]\xB0
    gaze: [gaze]\xB0`,body:"body [score]%",bodyPart:"[label] [score]%",object:"[label] [score]%",hand:"[label] [score]%",finger:"[label]",gesture:"[where] [who]: [what]"};var gt=0;function _r(e,t,n){let o=Q(n0,n);if(!t||!e)return;let s=O0(e);if(s){s.lineJoin="round",s.font=o.font;for(let A=0;A<t.length;A++)if(o.drawBoxes){if(s.strokeStyle=o.color,s.fillStyle=o.color,Y0(s,t[A].box[0],t[A].box[1],t[A].box[2],t[A].box[3],o),o.drawLabels){let a=`person #${A}`;o.shadowColor&&o.shadowColor!==""&&(s.fillStyle=o.shadowColor,s.fillText(a,t[A].box[0]+3,1+t[A].box[1]+o.lineHeight,t[A].box[2])),s.fillStyle=o.labelColor,s.fillText(a,t[A].box[0]+2,0+t[A].box[1]+o.lineHeight,t[A].box[2])}s.stroke()}}}function $r(e,t){if(!e||!t)return;let n=O0(t);n&&n.drawImage(e,0,0)}async function es(e,t){!e||!t||M.browser&&await r.browser.toPixels(e,t)}async function ts(e,t,n){if(!(t!=null&&t.performance)||!e)return null;let o=R(),s=Q(n0,n),A=Promise.all([b2(e,t.face,s),g2(e,t.body,s),T2(e,t.hand,s),v2(e,t.object,s),R2(e,t.gesture,s)]);return gt=M.perfadd?gt+Math.round(R()-o):Math.round(R()-o),t.performance.draw=gt,A}function Tt(){n0.faceLabels=ae.face,n0.bodyLabels=ae.body,n0.bodyPartLabels=ae.bodyPart,n0.handLabels=ae.hand,n0.fingerLabels=ae.finger,n0.objectLabels=ae.object,n0.gestureLabels=ae.gesture}var M2={};re(M2,{connected:()=>Mt,kpt:()=>Rt});var Rt=["nose","leftEyeInside","leftEye","leftEyeOutside","rightEyeInside","rightEye","rightEyeOutside","leftEar","rightEar","leftMouth","rightMouth","leftShoulder","rightShoulder","leftElbow","rightElbow","leftWrist","rightWrist","leftPinky","rightPinky","leftIndex","rightIndex","leftThumb","rightThumb","leftHip","rightHip","leftKnee","rightKnee","leftAnkle","rightAnkle","leftHeel","rightHeel","leftFoot","rightFoot","bodyCenter","bodyTop","leftPalm","leftHand","rightPalm","rightHand"],Mt={shoulders:["leftShoulder","rightShoulder"],hips:["rightHip","leftHip"],mouth:["leftMouth","rightMouth"],leftLegUpper:["leftHip","leftKnee"],leftLegLower:["leftKnee","leftAnkle"],leftFoot:["leftAnkle","leftHeel","leftFoot"],leftTorso:["leftShoulder","leftHip"],leftArmUpper:["leftShoulder","leftElbow"],leftArmLower:["leftElbow","leftWrist"],leftHand:["leftWrist","leftPalm"],leftHandPinky:["leftPalm","leftPinky"],leftHandIndex:["leftPalm","leftIndex"],leftHandThumb:["leftPalm","leftThumb"],leftEyeOutline:["leftEyeInside","leftEyeOutside"],rightLegUpper:["rightHip","rightKnee"],rightLegLower:["rightKnee","rightAnkle"],rightFoot:["rightAnkle","rightHeel","rightFoot"],rightTorso:["rightShoulder","rightHip"],rightArmUpper:["rightShoulder","rightElbow"],rightArmLower:["rightElbow","rightWrist"],rightHand:["rightWrist","rightPalm"],rightHandPinky:["rightPalm","rightPinky"],rightHandIndex:["rightPalm","rightIndex"],rightHandThumb:["rightPalm","rightThumb"],rightEyeOutline:["rightEyeInside","rightEyeOutside"]};var W0,ke=224,y1,ns=5,P2=[8,16,32,32,32];function os(){let e=[],t=0;for(;t<ns;){let n=0,o=t;for(;o<P2.length&&P2[o]===P2[t];)n+=2,o++;let s=P2[t],A=Math.ceil(ke/s),a=Math.ceil(ke/s);for(let i=0;i<A;++i)for(let c=0;c<a;++c)for(let d=0;d<n;++d)e.push({x:(c+.5)/a,y:(i+.5)/A});t=o}y1={x:r.tensor1d(e.map(n=>n.x)),y:r.tensor1d(e.map(n=>n.y))}}async function f1(e){if(M.initial&&(W0=null),!W0&&e.body.detector&&e.body.detector.modelPath){W0=await L(e.body.detector.modelPath);let t=W0!=null&&W0.executor?Object.values(W0.modelSignature.inputs):void 0;ke=Array.isArray(t)?parseInt(t[0].tensorShape.dim[1].size):0}else e.debug&&W0&&g("cached model:",W0.modelUrl);return os(),W0}var x1=[5,5];function rs(e,t){return r.tidy(()=>{let n=r.split(e,12,1),o=r.squeeze(n[0]),s=r.squeeze(n[1]),A=r.squeeze(n[2]),a=r.squeeze(n[3]);o=r.add(r.div(o,ke),t.x),s=r.add(r.div(s,ke),t.y),A=r.mul(r.div(A,ke),x1[0]),a=r.mul(r.div(a,ke),x1[1]);let i=r.sub(o,r.div(A,2)),c=r.sub(s,r.div(a,2)),d=r.add(i,A),x=r.add(c,a);return r.stack([i,c,d,x],1)})}async function ss(e,t,n,o){var d,x;let s=[],A={};A.boxes=rs(e,y1),A.scores=r.sigmoid(t),A.nms=await r.image.nonMaxSuppressionAsync(A.boxes,A.scores,1,((d=n.body.detector)==null?void 0:d.minConfidence)||.1,((x=n.body.detector)==null?void 0:x.iouThreshold)||.1);let a=await A.nms.data(),i=await A.scores.data(),c=await A.boxes.array();for(let l of Array.from(a)){let f=i[l],y=c[l],p=[Math.round(y[0]*o[0]),Math.round(y[1]*o[1]),Math.round(y[2]*o[0]),Math.round(y[3]*o[1])],m={score:f,boxRaw:y,box:p};s.push(m)}return Object.keys(A).forEach(l=>r.dispose(A[l])),s}async function m1(e,t,n){let o={};o.res=W0==null?void 0:W0.execute(e,["Identity"]),o.logitsRaw=r.slice(o.res,[0,0,0],[1,-1,1]),o.boxesRaw=r.slice(o.res,[0,0,1],[1,-1,-1]),o.logits=r.squeeze(o.logitsRaw),o.boxes=r.squeeze(o.boxesRaw);let s=await ss(o.boxes,o.logits,t,n);return Object.keys(o).forEach(A=>r.dispose(o[A])),s}function ie(e,t=[1,1]){let n=[e.map(i=>i[0]),e.map(i=>i[1])],o=[Math.min(...n[0]),Math.min(...n[1])],s=[Math.max(...n[0]),Math.max(...n[1])],A=[o[0],o[1],s[0]-o[0],s[1]-o[1]],a=[A[0]/t[0],A[1]/t[1],A[2]/t[0],A[3]/t[1]];return{box:A,boxRaw:a}}function p1(e,t=[1,1]){let n=[e.map(d=>d[0]),e.map(d=>d[1])],o=[Math.min(...n[0]),Math.min(...n[1])],s=[Math.max(...n[0]),Math.max(...n[1])],A=[(o[0]+s[0])/2,(o[1]+s[1])/2],a=Math.max(A[0]-o[0],A[1]-o[1],-A[0]+s[0],-A[1]+s[1]),i=[Math.trunc(A[0]-a),Math.trunc(A[1]-a),Math.trunc(2*a),Math.trunc(2*a)],c=[i[0]/t[0],i[1]/t[1],i[2]/t[0],i[3]/t[1]];return{box:i,boxRaw:c}}function k2(e,t){let n=[e[2]*t,e[3]*t];return[e[0]-(n[0]-e[2])/2,e[1]-(n[1]-e[3])/2,n[0],n[1]]}var P0,kt=256,Pt=Number.MAX_SAFE_INTEGER,As={landmarks:["ld_3d","activation_segmentation","activation_heatmap","world_3d","output_poseflag"],detector:[]},E2=[],le=[[0,0],[0,0],[0,0],[0,0]],u1=0,h1=e=>1-1/(1+Math.exp(e)),g1=e=>f1(e);async function T1(e){if(M.initial&&(P0=null),P0)e.debug&&g("cached model:",P0.modelUrl);else{P0=await L(e.body.modelPath);let t=P0!=null&&P0.executor?Object.values(P0.modelSignature.inputs):void 0;kt=Array.isArray(t)?parseInt(t[0].tensorShape.dim[1].size):0}return P0}function b1(e,t,n){var A,a;let o={};if(!((A=e==null?void 0:e.shape)!=null&&A[1])||!((a=e==null?void 0:e.shape)!=null&&a[2]))return e;let s;if(n&&(o.cropped=r.image.cropAndResize(e,[n],[0],[e.shape[1],e.shape[2]])),e.shape[1]!==e.shape[2]){let i=[e.shape[2]>e.shape[1]?Math.trunc((e.shape[2]-e.shape[1])/2):0,e.shape[2]>e.shape[1]?Math.trunc((e.shape[2]-e.shape[1])/2):0],c=[e.shape[1]>e.shape[2]?Math.trunc((e.shape[1]-e.shape[2])/2):0,e.shape[1]>e.shape[2]?Math.trunc((e.shape[1]-e.shape[2])/2):0];le=[[0,0],i,c,[0,0]],o.pad=r.pad(o.cropped||e,le),o.resize=r.image.resizeBilinear(o.pad,[t,t]),s=r.div(o.resize,O.tf255)}else e.shape[1]!==t?(o.resize=r.image.resizeBilinear(o.cropped||e,[t,t]),s=r.div(o.resize,O.tf255)):s=r.div(o.cropped||e,O.tf255);return Object.keys(o).forEach(i=>r.dispose(o[i])),s}function as(e,t,n){for(let o of e)o.position=[Math.trunc(o.position[0]*(t[0]+le[2][0]+le[2][1])/t[0]-le[2][0]),Math.trunc(o.position[1]*(t[1]+le[1][0]+le[1][1])/t[1]-le[1][0]),o.position[2]],o.positionRaw=[o.position[0]/t[0],o.position[1]/t[1],2*o.position[2]/(t[0]+t[1])];if(n){let o=n[2]-n[0],s=n[3]-n[1];for(let A of e)A.positionRaw=[A.positionRaw[0]/s+n[1],A.positionRaw[1]/o+n[0],A.positionRaw[2]],A.position=[Math.trunc(A.positionRaw[0]*t[0]),Math.trunc(A.positionRaw[1]*t[1]),A.positionRaw[2]]}return e}function is(e){let t=e.find(i=>i.part==="leftPalm"),n=e.find(i=>i.part==="leftWrist"),o=e.find(i=>i.part==="leftIndex");t.position[2]=((n.position[2]||0)+(o.position[2]||0))/2;let s=e.find(i=>i.part==="rightPalm"),A=e.find(i=>i.part==="rightWrist"),a=e.find(i=>i.part==="rightIndex");s.position[2]=((A.position[2]||0)+(a.position[2]||0))/2}async function ls(e,t,n){if(!(P0!=null&&P0.executor))return null;let o={};[o.ld,o.segmentation,o.heatmap,o.world,o.poseflag]=P0==null?void 0:P0.execute(e,As.landmarks);let s=(await o.poseflag.data())[0],A=await o.ld.data(),a=await o.world.data();Object.keys(o).forEach(p=>r.dispose(o[p]));let i=[],c=5;for(let p=0;p<A.length/c;p++){let m=h1(A[c*p+3]),b=h1(A[c*p+4]),v=Math.trunc(100*m*b*s)/100,T=[A[c*p+0]/kt,A[c*p+1]/kt,A[c*p+2]+0],u=[Math.trunc(n[0]*T[0]),Math.trunc(n[1]*T[1]),T[2]],h=[a[c*p+0],a[c*p+1],a[c*p+2]+0];i.push({part:Rt[p],positionRaw:T,position:u,distance:h,score:v})}if(s<(t.body.minConfidence||0))return null;is(i);let d=as(i,n),x=d.map(p=>p.position),l=ie(x,[n[0],n[1]]),f={};for(let[p,m]of Object.entries(Mt)){let b=[];for(let v=0;v<m.length-1;v++){let T=d.find(h=>h.part===m[v]),u=d.find(h=>h.part===m[v+1]);T&&u&&b.push([T.position,u.position])}f[p]=b}return{id:0,score:Math.trunc(100*s)/100,box:l.box,boxRaw:l.boxRaw,keypoints:d,annotations:f}}async function wt(e,t){var A,a,i;let n=[e.shape[2]||0,e.shape[1]||0],o=(t.body.skipTime||0)>R()-u1,s=Pt<(t.body.skipFrames||0);if(t.skipAllowed&&o&&s&&E2!==null)Pt++;else{let c=[];if((a=(A=t.body)==null?void 0:A.detector)!=null&&a.enabled){let d=b1(e,224);c=await m1(d,t,n),r.dispose(d)}else c=[{box:[0,0,0,0],boxRaw:[0,0,1,1],score:0}];for(let d=0;d<c.length;d++){let x=b1(e,256,(i=c[d])==null?void 0:i.boxRaw);E2.length=0;let l=await ls(x,t,n);r.dispose(x),l&&(l.id=d,E2.push(l))}u1=R(),Pt=0}return E2}var De=[{class:1,label:"person"},{class:2,label:"bicycle"},{class:3,label:"car"},{class:4,label:"motorcycle"},{class:5,label:"airplane"},{class:6,label:"bus"},{class:7,label:"train"},{class:8,label:"truck"},{class:9,label:"boat"},{class:10,label:"traffic light"},{class:11,label:"fire hydrant"},{class:12,label:"stop sign"},{class:13,label:"parking meter"},{class:14,label:"bench"},{class:15,label:"bird"},{class:16,label:"cat"},{class:17,label:"dog"},{class:18,label:"horse"},{class:19,label:"sheep"},{class:20,label:"cow"},{class:21,label:"elephant"},{class:22,label:"bear"},{class:23,label:"zebra"},{class:24,label:"giraffe"},{class:25,label:"backpack"},{class:26,label:"umbrella"},{class:27,label:"handbag"},{class:28,label:"tie"},{class:29,label:"suitcase"},{class:30,label:"frisbee"},{class:31,label:"skis"},{class:32,label:"snowboard"},{class:33,label:"sports ball"},{class:34,label:"kite"},{class:35,label:"baseball bat"},{class:36,label:"baseball glove"},{class:37,label:"skateboard"},{class:38,label:"surfboard"},{class:39,label:"tennis racket"},{class:40,label:"bottle"},{class:41,label:"wine glass"},{class:42,label:"cup"},{class:43,label:"fork"},{class:44,label:"knife"},{class:45,label:"spoon"},{class:46,label:"bowl"},{class:47,label:"banana"},{class:48,label:"apple"},{class:49,label:"sandwich"},{class:50,label:"orange"},{class:51,label:"broccoli"},{class:52,label:"carrot"},{class:53,label:"hot dog"},{class:54,label:"pizza"},{class:55,label:"donut"},{class:56,label:"cake"},{class:57,label:"chair"},{class:58,label:"couch"},{class:59,label:"potted plant"},{class:60,label:"bed"},{class:61,label:"dining table"},{class:62,label:"toilet"},{class:63,label:"tv"},{class:64,label:"laptop"},{class:65,label:"mouse"},{class:66,label:"remote"},{class:67,label:"keyboard"},{class:68,label:"cell phone"},{class:69,label:"microwave"},{class:70,label:"oven"},{class:71,label:"toaster"},{class:72,label:"sink"},{class:73,label:"refrigerator"},{class:74,label:"book"},{class:75,label:"clock"},{class:76,label:"vase"},{class:77,label:"scissors"},{class:78,label:"teddy bear"},{class:79,label:"hair drier"},{class:80,label:"toothbrush"}];var k0,we=0,Et=[],R1=0,zt=Number.MAX_SAFE_INTEGER;async function M1(e){if(M.initial&&(k0=null),k0)e.debug&&g("cached model:",k0.modelUrl);else{k0=await L(e.object.modelPath);let t=k0!=null&&k0.executor?Object.values(k0.modelSignature.inputs):void 0;we=Array.isArray(t)?parseInt(t[0].tensorShape.dim[2].size):0}return k0}async function cs(e,t,n){if(!e)return[];let o={},s=[],A=await e.array();o.squeeze=r.squeeze(e);let a=r.split(o.squeeze,6,1);o.stack=r.stack([a[1],a[0],a[3],a[2]],1),o.boxes=r.squeeze(o.stack),o.scores=r.squeeze(a[4]),o.classes=r.squeeze(a[5]),r.dispose([e,...a]),o.nms=await r.image.nonMaxSuppressionAsync(o.boxes,o.scores,n.object.maxDetected||0,n.object.iouThreshold,n.object.minConfidence||0);let i=await o.nms.data(),c=0;for(let d of Array.from(i)){let x=Math.trunc(100*A[0][d][4])/100,l=A[0][d][5];if(Number.isNaN(l))continue;let f=De[l].label,[y,p]=[A[0][d][0]/we,A[0][d][1]/we],m=[y,p,A[0][d][2]/we-y,A[0][d][3]/we-p],b=[Math.trunc(m[0]*t[0]),Math.trunc(m[1]*t[1]),Math.trunc(m[2]*t[0]),Math.trunc(m[3]*t[1])];s.push({id:c++,score:x,class:l,label:f,box:b,boxRaw:m})}return Object.keys(o).forEach(d=>r.dispose(o[d])),s}async function St(e,t){if(!(k0!=null&&k0.executor))return[];let n=(t.object.skipTime||0)>R()-R1,o=zt<(t.object.skipFrames||0);return t.skipAllowed&&n&&o&&Et.length>0?(zt++,Et):(zt=0,new Promise(async s=>{let A=[e.shape[2]||0,e.shape[1]||0],a=r.image.resizeBilinear(e,[we,we]),i=t.object.enabled?k0==null?void 0:k0.execute(a,["tower_0/detections"]):null;R1=R(),r.dispose(a);let c=await cs(i,A,t);Et=c,s(c)}))}var z2={};re(z2,{connected:()=>Nt,kpt:()=>jt});var jt=["head","neck","rightShoulder","rightElbow","rightWrist","chest","leftShoulder","leftElbow","leftWrist","bodyCenter","rightHip","rightKnee","rightAnkle","leftHip","leftKnee","leftAnkle"],Nt={leftLeg:["leftHip","leftKnee","leftAnkle"],rightLeg:["rightHip","rightKnee","rightAnkle"],torso:["leftShoulder","rightShoulder","rightHip","leftHip","leftShoulder"],leftArm:["leftShoulder","leftElbow","leftWrist"],rightArm:["rightShoulder","rightElbow","rightWrist"],head:[]};var _,k1=0,v0={id:0,keypoints:[],box:[0,0,0,0],boxRaw:[0,0,0,0],score:0,annotations:{}},It=Number.MAX_SAFE_INTEGER;async function w1(e){return M.initial&&(_=null),_?e.debug&&g("cached model:",_.modelUrl):_=await L(e.body.modelPath),_}async function ds(e,t){let[n,o]=e.shape,s=r.reshape(e,[o*n]),A=r.max(s,0),a=(await A.data())[0];if(a>t){let i=r.argMax(s,0),c=r.mod(i,n),d=(await c.data())[0],x=r.div(i,n),l=(await x.data())[0];return r.dispose([s,A,i,c,x]),[d,l,a]}return r.dispose([s,A]),[0,0,a]}async function Lt(e,t){if(!(_!=null&&_.executor)||!(_!=null&&_.inputs[0].shape))return[];let n=(t.body.skipTime||0)>R()-k1,o=It<(t.body.skipFrames||0);return t.skipAllowed&&n&&o&&Object.keys(v0.keypoints).length>0?(It++,[v0]):(It=0,new Promise(async s=>{let A=r.tidy(()=>{var p,m;let l=r.image.resizeBilinear(e,[((p=_==null?void 0:_.inputs[0].shape)==null?void 0:p[2])||0,((m=_==null?void 0:_.inputs[0].shape)==null?void 0:m[1])||0],!1),f=r.mul(l,O.tf2);return r.sub(f,O.tf1)}),a;if(t.body.enabled&&(a=_==null?void 0:_.execute(A)),k1=R(),r.dispose(A),a){v0.keypoints.length=0;let l=r.squeeze(a);r.dispose(a);let f=r.unstack(l,2);r.dispose(l);for(let y=0;y<f.length;y++){let[p,m,b]=await ds(f[y],t.body.minConfidence);b>(t.body.minConfidence||0)&&v0.keypoints.push({score:Math.round(100*b)/100,part:jt[y],positionRaw:[p/_.inputs[0].shape[2],m/_.inputs[0].shape[1]],position:[Math.round(e.shape[2]*p/_.inputs[0].shape[2]),Math.round(e.shape[1]*m/_.inputs[0].shape[1])]})}f.forEach(y=>r.dispose(y))}v0.score=v0.keypoints.reduce((l,f)=>f.score>l?f.score:l,0);let i=v0.keypoints.map(l=>l.position[0]),c=v0.keypoints.map(l=>l.position[1]);v0.box=[Math.min(...i),Math.min(...c),Math.max(...i)-Math.min(...i),Math.max(...c)-Math.min(...c)];let d=v0.keypoints.map(l=>l.positionRaw[0]),x=v0.keypoints.map(l=>l.positionRaw[1]);v0.boxRaw=[Math.min(...d),Math.min(...x),Math.max(...d)-Math.min(...d),Math.max(...x)-Math.min(...x)];for(let[l,f]of Object.entries(Nt)){let y=[];for(let p=0;p<f.length-1;p++){let m=v0.keypoints.find(v=>v.part===f[p]),b=v0.keypoints.find(v=>v.part===f[p+1]);m&&b&&m.score>(t.body.minConfidence||0)&&b.score>(t.body.minConfidence||0)&&y.push([m.position,b.position])}v0.annotations[l]=y}s([v0])}))}var Fe=e=>[Math.abs(e.endPoint[0]-e.startPoint[0]),Math.abs(e.endPoint[1]-e.startPoint[1])],S2=e=>[e.startPoint[0]+(e.endPoint[0]-e.startPoint[0])/2,e.startPoint[1]+(e.endPoint[1]-e.startPoint[1])/2,1],j2=(e,t)=>e?[Math.trunc(Math.max(0,e.startPoint[0])),Math.trunc(Math.max(0,e.startPoint[1])),Math.trunc(Math.min(t.shape[2]||0,e.endPoint[0])-Math.max(0,e.startPoint[0])),Math.trunc(Math.min(t.shape[1]||0,e.endPoint[1])-Math.max(0,e.startPoint[1]))]:[0,0,0,0],N2=(e,t)=>e?[e.startPoint[0]/(t.shape[2]||0),e.startPoint[1]/(t.shape[1]||0),(e.endPoint[0]-e.startPoint[0])/(t.shape[2]||0),(e.endPoint[1]-e.startPoint[1])/(t.shape[1]||0)]:[0,0,0,0],j1=(e,t,n)=>{let o=[e.startPoint[0]*t[0],e.startPoint[1]*t[1]],s=[e.endPoint[0]*t[0],e.endPoint[1]*t[1]],A=e.landmarks.map(a=>[(a[0]+n[0])*t[0],(a[1]+n[1])*t[1]]);return{startPoint:o,endPoint:s,landmarks:A,confidence:e.confidence}},Ot=(e,t,n)=>{let o=t.shape[1],s=t.shape[2],A=[e.startPoint[1]/o,e.startPoint[0]/s,e.endPoint[1]/o,e.endPoint[0]/s],a=r.image.cropAndResize(t,[A],[0],n),i=r.div(a,O.tf255);return r.dispose(a),i},I2=(e,t)=>{let n=S2(e),o=Fe(e),s=[t*o[0]/2,t*o[1]/2];return{startPoint:[n[0]-s[0],n[1]-s[1]],endPoint:[n[0]+s[0],n[1]+s[1]],landmarks:e.landmarks,confidence:e.confidence,size:o}},L2=e=>{let t=S2(e),n=Fe(e),o=Math.max(...n)/2;return{startPoint:[Math.round(t[0]-o),Math.round(t[1]-o)],endPoint:[Math.round(t[0]+o),Math.round(t[1]+o)],landmarks:e.landmarks,confidence:e.confidence,size:[Math.round(n[0]),Math.round(n[1])]}},N1=e=>{let t=e.map(o=>o[0]),n=e.map(o=>o[1]);return{startPoint:[Math.min(...t),Math.min(...n)],endPoint:[Math.max(...t),Math.max(...n)],landmarks:e}},Ct=[[1,0,0],[0,1,0],[0,0,1]],xs=e=>e-2*Math.PI*Math.floor((e+Math.PI)/(2*Math.PI)),ys=(e,t)=>xs(Math.PI/2-Math.atan2(-(t[1]-e[1]),t[0]-e[0]));var z1=(e,t)=>[[1,0,e],[0,1,t],[0,0,1]],Ee=(e,t)=>{let n=0;for(let o=0;o<e.length;o++)n+=e[o]*t[o];return n},fs=(e,t)=>{let n=[];for(let o=0;o<e.length;o++)n.push(e[o][t]);return n},S1=(e,t)=>{let n=[],o=e.length;for(let s=0;s<o;s++){n.push([]);for(let A=0;A<o;A++)n[s].push(Ee(e[s],fs(t,A)))}return n},I1=(e,t)=>{let n=Math.cos(e),o=Math.sin(e),s=[[n,-o,0],[o,n,0],[0,0,1]],A=z1(t[0],t[1]),a=S1(A,s),i=z1(-t[0],-t[1]);return S1(a,i)},ms=e=>{let t=[[e[0][0],e[1][0]],[e[0][1],e[1][1]]],n=[e[0][2],e[1][2]],o=[-Ee(t[0],n),-Ee(t[1],n)];return[t[0].concat(o[0]),t[1].concat(o[1]),[0,0,1]]},ps=(e,t)=>[Ee(e,t[0]),Ee(e,t[1])];function L1(e){let t=e===192?{strides:[4],anchors:[1]}:{strides:[e/16,e/8],anchors:[2,6]},n=[];for(let o=0;o<t.strides.length;o++){let s=t.strides[o],A=Math.floor((e+s-1)/s),a=Math.floor((e+s-1)/s),i=t.anchors[o];for(let c=0;c<A;c++){let d=s*(c+.5);for(let x=0;x<a;x++){let l=s*(x+.5);for(let f=0;f<i;f++)n.push([l,d])}}}return n}function O1(e,t,n,o,s){let A=Fe(t),a=e.map(y=>[A[0]/s*(y[0]-s/2),A[1]/s*(y[1]-s/2),y[2]||0]),i=n&&n!==0&&Math.abs(n)>.2,c=i?I1(n,[0,0]):Ct,d=i?a.map(y=>[...ps(y,c),y[2]]):a,x=i?ms(o):Ct,l=S2(t),f=[Ee(l,x[0]),Ee(l,x[1])];return d.map(y=>[Math.trunc(y[0]+f[0]),Math.trunc(y[1]+f[1]),Math.trunc(y[2]||0)])}function C1(e,t,n,o){let s=t.landmarks.length>=ut.count?ut.symmetryLine:ve.symmetryLine,A=0,a=Ct,i;if(e&&M.kernels.includes("rotatewithoffset"))if(A=ys(t.landmarks[s[0]],t.landmarks[s[1]]),A&&A!==0&&Math.abs(A)>.2){let d=S2(t),x=[d[0]/n.shape[2],d[1]/n.shape[1]],l=r.image.rotateWithOffset(n,A,0,[x[0],x[1]]);a=I1(-A,d),i=Ot(t,l,[o,o]),r.dispose(l)}else i=Ot(t,n,[o,o]);else i=Ot(t,n,[o,o]);return[A,a,i]}var us=e=>{let t=e.map(o=>o[0]),n=e.map(o=>o[1]);return[Math.min(...t)+(Math.max(...t)-Math.min(...t))/2,Math.min(...n)+(Math.max(...n)-Math.min(...n))/2]},W1=(e,t)=>{let n=us(e),o=Fe(t);return{startPoint:[n[0]-o[0]/2,n[1]-o[1]/2],endPoint:[n[0]+o[0]/2,n[1]+o[1]/2]}};var D1=6,G0,O2=null,K0=0,Be=null,F1=()=>K0;async function B1(e){var t;return M.initial&&(G0=null),G0?e.debug&&g("cached model:",G0.modelUrl):G0=await L((t=e.face.detector)==null?void 0:t.modelPath),K0=G0.executor&&G0.inputs[0].shape?G0.inputs[0].shape[2]:256,Be=r.scalar(K0,"int32"),O2=r.tensor2d(L1(K0)),G0}function hs(e){if(!O2||!Be)return r.zeros([0,0]);let t={};t.boxStarts=r.slice(e,[0,1],[-1,2]),t.centers=r.add(t.boxStarts,O2),t.boxSizes=r.slice(e,[0,3],[-1,2]),t.boxSizesNormalized=r.div(t.boxSizes,Be),t.centersNormalized=r.div(t.centers,Be),t.halfBoxSize=r.div(t.boxSizesNormalized,O.tf2),t.starts=r.sub(t.centersNormalized,t.halfBoxSize),t.ends=r.add(t.centersNormalized,t.halfBoxSize),t.startNormalized=r.mul(t.starts,Be),t.endNormalized=r.mul(t.ends,Be);let n=r.concat2d([t.startNormalized,t.endNormalized],1);return Object.keys(t).forEach(o=>r.dispose(t[o])),n}async function H1(e,t){var d,x,l,f,y,p,m,b,v;if(!e||e.isDisposedInternal||e.shape.length!==4||e.shape[1]<1||e.shape[2]<1)return[];let n={},o=[0,0],s=[1,1];if((x=(d=t==null?void 0:t.face)==null?void 0:d.detector)!=null&&x.square){let T=Math.max(e.shape[2],e.shape[1]);o=[Math.floor((T-e.shape[2])/2),Math.floor((T-e.shape[1])/2)],n.padded=r.pad(e,[[0,0],[o[1],o[1]],[o[0],o[0]],[0,0]]),s=[e.shape[2]/T,e.shape[1]/T],o=[o[0]/K0,o[1]/K0]}else n.padded=e.clone();n.resized=r.image.resizeBilinear(n.padded,[K0,K0]),n.div=r.div(n.resized,O.tf127),n.normalized=r.sub(n.div,O.tf1);let A=G0==null?void 0:G0.execute(n.normalized);if(Array.isArray(A)&&A.length>2){let T=A.sort((u,h)=>u.size-h.size);n.concat384=r.concat([T[0],T[2]],2),n.concat512=r.concat([T[1],T[3]],2),n.concat=r.concat([n.concat512,n.concat384],1),n.batch=r.squeeze(n.concat,[0])}else Array.isArray(A)?n.batch=r.squeeze(A[0]):n.batch=r.squeeze(A);r.dispose(A),n.boxes=hs(n.batch),n.logits=r.slice(n.batch,[0,0],[-1,1]),n.sigmoid=r.sigmoid(n.logits),n.scores=r.squeeze(n.sigmoid),n.nms=await r.image.nonMaxSuppressionAsync(n.boxes,n.scores,((l=t.face.detector)==null?void 0:l.maxDetected)||0,((f=t.face.detector)==null?void 0:f.iouThreshold)||0,((y=t.face.detector)==null?void 0:y.minConfidence)||0);let a=await n.nms.array(),i=[],c=await n.scores.data();for(let T=0;T<a.length;T++){let u=c[a[T]];if(u>(((p=t.face.detector)==null?void 0:p.minConfidence)||0)){let h={};h.bbox=r.slice(n.boxes,[a[T],0],[1,-1]),h.slice=r.slice(n.batch,[a[T],D1-1],[1,-1]),h.squeeze=r.squeeze(h.slice),h.landmarks=r.reshape(h.squeeze,[D1,-1]);let w=await h.bbox.data(),k=[w[0]*s[0]-o[0],w[1]*s[1]-o[1],w[2]*s[0]-o[0],w[3]*s[1]-o[1]],I={startPoint:[k[0],k[1]],endPoint:[k[2],k[3]],landmarks:await h.landmarks.array(),confidence:u};h.anchor=r.slice(O2,[a[T],0],[1,2]);let C=await h.anchor.data(),V=j1(I,[(e.shape[2]||0)/K0,(e.shape[1]||0)/K0],C),W=I2(V,((m=t.face.detector)==null?void 0:m.scale)||1.4),G=L2(W);G.size[0]>(((b=t.face.detector)==null?void 0:b.minSize)||0)&&G.size[1]>(((v=t.face.detector)==null?void 0:v.minSize)||0)&&i.push(G),Object.keys(h).forEach(X=>r.dispose(h[X]))}}return Object.keys(n).forEach(T=>r.dispose(n[T])),i}var z0,ce=0,Dt=F0.leftEyeLower0,Ft=F0.rightEyeLower0,He={leftBounds:[Dt[0],Dt[Dt.length-1]],rightBounds:[Ft[0],Ft[Ft.length-1]]},Ge={upperCenter:3,lowerCenter:4,index:71,numCoordinates:76};async function q1(e){var t,n;return M.initial&&(z0=null),z0?e.debug&&g("cached model:",z0.modelUrl):z0=await L((t=e.face.iris)==null?void 0:t.modelPath),ce=z0!=null&&z0.executor&&((n=z0.inputs)!=null&&n[0].shape)?z0.inputs[0].shape[2]:0,ce===-1&&(ce=64),z0}function C2(e,t,n,o){for(let s=0;s<ht.length;s++){let{key:A,indices:a}=ht[s],i=F0[`${n}${A}`];if(!o||o.includes(A))for(let c=0;c<a.length;c++){let d=a[c];e[i[c]]=[t[d][0],t[d][1],(t[d][2]+e[i[c]][2])/2]}}}var bs=e=>{let t=e[He.leftBounds[0]][2],n=e[He.rightBounds[0]][2];return t-n},V1=(e,t,n,o,s,A=!1,a=2.3)=>{let i=L2(I2(N1([e[n],e[o]]),a)),c=Fe(i),d=r.image.cropAndResize(t,[[i.startPoint[1]/s,i.startPoint[0]/s,i.endPoint[1]/s,i.endPoint[0]/s]],[0],[ce,ce]);if(A&&M.kernels.includes("flipleftright")){let x=r.image.flipLeftRight(d);r.dispose(d),d=x}return{box:i,boxSize:c,crop:d}},Z1=(e,t,n,o=!1)=>{let s=[];for(let A=0;A<Ge.numCoordinates;A++){let a=e[A*3],i=e[A*3+1],c=e[A*3+2];s.push([(o?1-a/ce:a/ce)*n[0]+t.startPoint[0],i/ce*n[1]+t.startPoint[1],c])}return{rawCoords:s,iris:s.slice(Ge.index)}},X1=(e,t,n)=>{let o=e[F0[`${n}EyeUpper0`][Ge.upperCenter]][2],s=e[F0[`${n}EyeLower0`][Ge.lowerCenter]][2],A=(o+s)/2;return t.map((a,i)=>{let c=A;return i===2?c=o:i===4&&(c=s),[a[0],a[1],c]})};async function U1(e,t,n,o){var I,C;if(!(z0!=null&&z0.executor))return e;let{box:s,boxSize:A,crop:a}=V1(e,t,He.leftBounds[0],He.leftBounds[1],n,!0,((I=o.face.iris)==null?void 0:I.scale)||2.3),{box:i,boxSize:c,crop:d}=V1(e,t,He.rightBounds[0],He.rightBounds[1],n,!0,((C=o.face.iris)==null?void 0:C.scale)||2.3),x=r.concat([a,d]);r.dispose(a),r.dispose(d);let l=z0.execute(x);r.dispose(x);let f=await l.data();r.dispose(l);let y=f.slice(0,Ge.numCoordinates*3),{rawCoords:p,iris:m}=Z1(y,s,A,!0),b=f.slice(Ge.numCoordinates*3),{rawCoords:v,iris:T}=Z1(b,i,c,!1),u=bs(e);Math.abs(u)<30?(C2(e,p,"left",null),C2(e,v,"right",null)):u<1?C2(e,p,"left",["EyeUpper0","EyeLower0"]):C2(e,v,"right",["EyeUpper0","EyeLower0"]);let h=X1(e,m,"left"),w=X1(e,T,"right");return e.concat(h).concat(w)}async function K1(e,t){var A,a,i,c,d,x,l,f,y,p;let n={lips:await((a=(A=t.filter(m=>m.size===160))==null?void 0:A[0])==null?void 0:a.data()),irisL:await((c=(i=t.filter(m=>m.size===10))==null?void 0:i[0])==null?void 0:c.data()),eyeL:await((x=(d=t.filter(m=>m.size===142))==null?void 0:d[0])==null?void 0:x.data()),irisR:await((f=(l=t.filter(m=>m.size===10))==null?void 0:l[1])==null?void 0:f.data()),eyeR:await((p=(y=t.filter(m=>m.size===142))==null?void 0:y[1])==null?void 0:p.data())};for(let m of Object.values(n))if(!m)return e;let o=Me.reduce((m,b)=>m+=e[b][2],0)/Me.length;for(let m=0;m<n.irisL.length/2;m++)e.push([n.irisL[2*m+0],n.irisL[2*m+1],o]);let s=Pe.reduce((m,b)=>m+=e[b][2],0)/Pe.length;for(let m=0;m<n.irisR.length/2;m++)e.push([n.irisR[2*m+0],n.irisR[2*m+1],s]);for(let m=0;m<n.eyeL.length/2;m++)e[Me[m]]=[n.eyeL[2*m+0],n.eyeL[2*m+1],e[Me[m]][2]];for(let m=0;m<n.eyeR.length/2;m++)e[Pe[m]]=[n.eyeR[2*m+0],n.eyeR[2*m+1],e[Pe[m]][2]];for(let m=0;m<n.lips.length/2;m++)e[n2[m]]=[n.lips[2*m+0],n.lips[2*m+1],e[n2[m]][2]];return e}var J0={boxes:[],skipped:Number.MAX_SAFE_INTEGER,timestamp:0},J=null,o2=0;async function J1(e,t){var c,d,x,l,f,y,p,m,b,v;let n=(((c=t.face.detector)==null?void 0:c.skipTime)||0)>R()-J0.timestamp,o=J0.skipped<(((d=t.face.detector)==null?void 0:d.skipFrames)||0);!t.skipAllowed||!n||!o||J0.boxes.length===0?(J0.boxes=await H1(e,t),J0.timestamp=R(),J0.skipped=0):J0.skipped++;let s=[],A=[],a=0,i=o2;for(let T=0;T<J0.boxes.length;T++){let u=J0.boxes[T],h=0,w,k={id:a++,mesh:[],meshRaw:[],box:[0,0,0,0],boxRaw:[0,0,0,0],score:0,boxScore:0,faceScore:0,size:[0,0],annotations:{}};if([h,w,k.tensor]=C1((x=t.face.detector)==null?void 0:x.rotation,u,e,(l=t.face.mesh)!=null&&l.enabled?o2:F1()),t.filter.equalization){let I=k.tensor?await d2(k.tensor):void 0;r.dispose(k.tensor),I&&(k.tensor=I)}if(k.boxScore=Math.round(100*u.confidence)/100,!((f=t.face.mesh)!=null&&f.enabled)||!(J!=null&&J.executor)){k.box=j2(u,e),k.boxRaw=N2(u,e),k.score=k.boxScore,k.size=u.size,k.mesh=u.landmarks,k.meshRaw=k.mesh.map(I=>[I[0]/(e.shape[2]||0),I[1]/(e.shape[1]||0),(I[2]||0)/i]);for(let I of Object.keys(ve))k.annotations[I]=[k.mesh[ve[I]]]}else if(!J)t.debug&&g("face mesh detection requested, but model is not loaded");else{if((y=t.face.attention)!=null&&y.enabled&&!M.kernels.includes("atan2"))return t.face.attention.enabled=!1,r.dispose(k.tensor),s;let I=J.execute(k.tensor),V=await I.find(W=>W.shape[W.shape.length-1]===1).data();if(k.faceScore=Math.round(100*V[0])/100,k.faceScore<(((p=t.face.detector)==null?void 0:p.minConfidence)||1)){if(u.confidence=k.faceScore,t.face.mesh.keepInvalid){k.box=j2(u,e),k.boxRaw=N2(u,e),k.size=u.size,k.score=k.boxScore,k.mesh=u.landmarks,k.meshRaw=k.mesh.map(W=>[W[0]/(e.shape[2]||1),W[1]/(e.shape[1]||1),(W[2]||0)/i]);for(let W of Object.keys(ve))k.annotations[W]=[k.mesh[ve[W]]]}}else{let W=I.find(Y=>Y.shape[Y.shape.length-1]===1404),G=r.reshape(W,[-1,3]),X=await G.array();r.dispose(G),(m=t.face.attention)!=null&&m.enabled?X=await K1(X,I):(b=t.face.iris)!=null&&b.enabled&&(X=await U1(X,k.tensor,o2,t)),k.mesh=O1(X,u,h,w,o2),k.meshRaw=k.mesh.map(Y=>[Y[0]/(e.shape[2]||0),Y[1]/(e.shape[1]||0),(Y[2]||0)/i]);for(let Y of Object.keys(F0))k.annotations[Y]=F0[Y].map(p0=>k.mesh[p0]);k.score=k.faceScore;let U={...W1(k.mesh,u),confidence:u.confidence,landmarks:u.landmarks,size:u.size};k.box=j2(U,e),k.boxRaw=N2(U,e),k.size=U.size,A.push(U)}r.dispose(I)}k.score>(((v=t.face.detector)==null?void 0:v.minConfidence)||1)?s.push(k):r.dispose(k.tensor)}return J0.boxes=A,s}async function Q1(e){var t,n,o,s,A,a;return M.initial&&(J=null),(t=e.face.attention)!=null&&t.enabled&&(J!=null&&J.signature)&&Object.keys(((n=J==null?void 0:J.signature)==null?void 0:n.outputs)||{}).length<6&&(J=null),J?e.debug&&g("cached model:",J.modelUrl):(o=e.face.attention)!=null&&o.enabled?J=await L(e.face.attention.modelPath):J=await L((s=e.face.mesh)==null?void 0:s.modelPath),o2=J.executor&&((A=J==null?void 0:J.inputs)!=null&&A[0].shape)?(a=J==null?void 0:J.inputs)==null?void 0:a[0].shape[2]:256,J}var _1=Re,$1=t2;var Gt=[],x0,W2=[],e3=0,t3=0,Ht=Number.MAX_SAFE_INTEGER,Vt=!1;async function n3(e){var t,n,o;return M.initial&&(x0=null),x0?e.debug&&g("cached model:",x0.modelUrl):(x0=await L((t=e.face.emotion)==null?void 0:t.modelPath),Vt=((o=(n=x0==null?void 0:x0.inputs)==null?void 0:n[0].shape)==null?void 0:o[3])===3,Vt?Gt=["angry","disgust","fear","happy","neutral","sad","surprise"]:Gt=["angry","disgust","fear","happy","sad","surprise","neutral"]),x0}async function Zt(e,t,n,o){var a,i;if(!x0)return[];let s=Ht<(((a=t.face.emotion)==null?void 0:a.skipFrames)||0),A=(((i=t.face.emotion)==null?void 0:i.skipTime)||0)>R()-t3;return t.skipAllowed&&A&&s&&e3===o&&W2[n]&&W2[n].length>0?(Ht++,W2[n]):(Ht=0,new Promise(async c=>{var x,l,f;let d=[];if((x=t.face.emotion)!=null&&x.enabled){let y={},p=x0!=null&&x0.inputs[0].shape?x0.inputs[0].shape[2]:0;if(((l=t.face.emotion)==null?void 0:l.crop)>0){let b=(f=t.face.emotion)==null?void 0:f.crop,v=[[b,b,1-b,1-b]];y.resize=r.image.cropAndResize(e,v,[0],[p,p])}else y.resize=r.image.resizeBilinear(e,[p,p],!1);Vt?(y.mul=r.mul(y.resize,255),y.normalize=r.sub(y.mul,[103.939,116.779,123.68]),y.emotion=x0==null?void 0:x0.execute(y.normalize)):(y.channels=r.mul(y.resize,O.rgb),y.grayscale=r.sum(y.channels,3,!0),y.grayscaleSub=r.sub(y.grayscale,O.tf05),y.grayscaleMul=r.mul(y.grayscaleSub,O.tf2),y.emotion=x0==null?void 0:x0.execute(y.grayscaleMul)),t3=R();let m=await y.emotion.data();for(let b=0;b<m.length;b++)m[b]>(t.face.emotion.minConfidence||0)&&d.push({score:Math.min(.99,Math.trunc(100*m[b])/100),emotion:Gt[b]});d.sort((b,v)=>v.score-b.score),Object.keys(y).forEach(b=>r.dispose(y[b]))}W2[n]=d,e3=o,c(d)}))}var y0,de=[],r3=0,s3=0,Xt=Number.MAX_SAFE_INTEGER;async function A3(e){var t;return M.initial&&(y0=null),y0?e.debug&&g("cached model:",y0.modelUrl):y0=await L((t=e.face.description)==null?void 0:t.modelPath),y0}function Ts(e,t){var A,a;let n=e.image||e.tensor||e;if(!(y0!=null&&y0.inputs[0].shape))return n;let o;if(((A=t.face.description)==null?void 0:A.crop)>0){let i=(a=t.face.description)==null?void 0:a.crop,c=[[i,i,1-i,1-i]];o=r.image.cropAndResize(n,c,[0],[y0.inputs[0].shape[2],y0.inputs[0].shape[1]])}else o=r.image.resizeBilinear(n,[y0.inputs[0].shape[2],y0.inputs[0].shape[1]],!1);let s=r.mul(o,O.tf255);return r.dispose(o),s}async function qt(e,t,n,o){var i,c,d,x;let s={age:0,gender:"unknown",genderScore:0,descriptor:[]};if(!(y0!=null&&y0.executor))return s;let A=Xt<(((i=t.face.description)==null?void 0:i.skipFrames)||0),a=(((c=t.face.description)==null?void 0:c.skipTime)||0)>R()-r3;return t.skipAllowed&&A&&a&&s3===o&&((d=de==null?void 0:de[n])==null?void 0:d.age)>0&&((x=de==null?void 0:de[n])==null?void 0:x.genderScore)>0?(Xt++,de[n]):(Xt=0,new Promise(async l=>{var f;if((f=t.face.description)!=null&&f.enabled){let y=Ts(e,t),p=y0==null?void 0:y0.execute(y);r3=R(),r.dispose(y);let b=await p.find(C=>C.shape[1]===1).data(),v=Math.trunc(200*Math.abs(b[0]-.5))/100;v>(t.face.description.minConfidence||0)&&(s.gender=b[0]<=.5?"female":"male",s.genderScore=Math.min(.99,v));let T=r.argMax(p.find(C=>C.shape[1]===100),1),u=(await T.data())[0];r.dispose(T);let w=await p.find(C=>C.shape[1]===100).data();s.age=Math.round(w[u-1]>w[u+1]?10*u-100*w[u-1]:10*u+100*w[u+1])/10,(Number.isNaN(b[0])||Number.isNaN(w[0]))&&g("faceres error:",{model:y0,result:p});let k=p.find(C=>C.shape[1]===1024),I=k?await k.data():[];s.descriptor=Array.from(I),p.forEach(C=>r.dispose(C))}de[n]=s,s3=o,l(s)}))}var Ve=.1,Ut=.5;function vs(e,t,n){let o=!1,s=n.length-1;for(let A=0;A<n.length;s=A++)n[A].y>t!=n[s].y>t&&e<(n[s].x-n[A].x)*(t-n[A].y)/(n[s].y-n[A].y)+n[A].x&&(o=!o);return o}async function i3(e){if(!e.tensor||!e.mesh||e.mesh.length<100)return e.tensor;let t=e.tensor.shape[2]||0,n=e.tensor.shape[1]||0,o=await e.tensor.buffer(),s=[];for(let a of F0.silhouette)s.push({x:(e.mesh[a][0]-e.box[0])/e.box[2],y:(e.mesh[a][1]-e.box[1])/e.box[3]});Ve&&Ve>0&&(s=s.map(a=>({x:a.x>.5?a.x+Ve:a.x-Ve,y:a.y>.5?a.y+Ve:a.y-Ve})));for(let a=0;a<t;a++)for(let i=0;i<n;i++)vs(a/t,i/t,s)||(o.set(Ut*o.get(0,i,a,0),0,i,a,0),o.set(Ut*o.get(0,i,a,1),0,i,a,1),o.set(Ut*o.get(0,i,a,2),0,i,a,2));return o.toTensor()}var f0,D2=[],Yt=Number.MAX_SAFE_INTEGER,l3=0,c3=0;async function d3(e){var t;return M.initial&&(f0=null),f0?e.debug&&g("cached model:",f0.modelUrl):f0=await L((t=e.face.antispoof)==null?void 0:t.modelPath),f0}async function Kt(e,t,n,o){var a,i;if(!(f0!=null&&f0.executor))return 0;let s=(((a=t.face.antispoof)==null?void 0:a.skipTime)||0)>R()-c3,A=Yt<(((i=t.face.antispoof)==null?void 0:i.skipFrames)||0);return t.skipAllowed&&s&&A&&l3===o&&D2[n]?(Yt++,D2[n]):(Yt=0,new Promise(async c=>{let d=r.image.resizeBilinear(e,[f0!=null&&f0.inputs[0].shape?f0.inputs[0].shape[2]:0,f0!=null&&f0.inputs[0].shape?f0.inputs[0].shape[1]:0],!1),x=f0==null?void 0:f0.execute(d),l=(await x.data())[0];D2[n]=Math.round(100*l)/100,l3=o,c3=R(),r.dispose([d,x]),c(D2[n])}))}var m0,F2=[],Jt=Number.MAX_SAFE_INTEGER,y3=0,f3=0;async function m3(e){var t;return M.initial&&(m0=null),m0?e.debug&&g("cached model:",m0.modelUrl):m0=await L((t=e.face.liveness)==null?void 0:t.modelPath),m0}async function Qt(e,t,n,o){var a,i;if(!(m0!=null&&m0.executor))return 0;let s=(((a=t.face.liveness)==null?void 0:a.skipTime)||0)>R()-f3,A=Jt<(((i=t.face.liveness)==null?void 0:i.skipFrames)||0);return t.skipAllowed&&s&&A&&y3===o&&F2[n]?(Jt++,F2[n]):(Jt=0,new Promise(async c=>{let d=r.image.resizeBilinear(e,[m0!=null&&m0.inputs[0].shape?m0.inputs[0].shape[2]:0,m0!=null&&m0.inputs[0].shape?m0.inputs[0].shape[1]:0],!1),x=m0==null?void 0:m0.execute(d),l=(await x.data())[0];F2[n]=Math.round(100*l)/100,y3=o,f3=R(),r.dispose([d,x]),c(F2[n])}))}var B0,_t=[],Ms=["white","black","asian","indian","other"],Ps=[15,23,28,35.5,45.5,55.5,65],u3=0,h3=0,$t=Number.MAX_SAFE_INTEGER;async function b3(e){var t;return M.initial&&(B0=null),B0?e.debug&&g("cached model:",B0.modelUrl):B0=await L((t=e.face.gear)==null?void 0:t.modelPath),B0}async function e5(e,t,n,o){var a,i;if(!B0)return{age:0,gender:"unknown",genderScore:0,race:[]};let s=$t<(((a=t.face.gear)==null?void 0:a.skipFrames)||0),A=(((i=t.face.gear)==null?void 0:i.skipTime)||0)>R()-h3;return t.skipAllowed&&A&&s&&u3===o&&_t[n]?($t++,_t[n]):($t=0,new Promise(async c=>{var v,T,u,h;if(!(B0!=null&&B0.inputs[0].shape))return;let d={},x=[[0,.1,.9,.9]];if(((v=t.face.gear)==null?void 0:v.crop)>0){let w=(T=t.face.gear)==null?void 0:T.crop;x=[[w,w,1-w,1-w]]}d.resize=r.image.cropAndResize(e,x,[0],[B0.inputs[0].shape[2],B0.inputs[0].shape[1]]);let l={age:0,gender:"unknown",genderScore:0,race:[]};(u=t.face.gear)!=null&&u.enabled&&([d.age,d.gender,d.race]=B0.execute(d.resize,["age_output","gender_output","race_output"]));let f=await d.gender.data();l.gender=f[0]>f[1]?"male":"female",l.genderScore=Math.round(100*(f[0]>f[1]?f[0]:f[1]))/100;let y=await d.race.data();for(let w=0;w<y.length;w++)y[w]>(((h=t.face.gear)==null?void 0:h.minConfidence)||.2)&&l.race.push({score:Math.round(100*y[w])/100,race:Ms[w]});l.race.sort((w,k)=>k.score-w.score);let m=Array.from(await d.age.data()).map((w,k)=>[Ps[k],w]).sort((w,k)=>k[1]-w[1]),b=m[0][0];for(let w=1;w<m.length;w++)b+=m[w][1]*(m[w][0]-b);l.age=Math.round(10*b)/10,Object.keys(d).forEach(w=>r.dispose(d[w])),_t[n]=l,u3=o,h3=R(),c(l)}))}var R0,B2=[],T3=0,v3=0,t5=Number.MAX_SAFE_INTEGER;async function R3(e){return M.initial&&(R0=null),R0?e.debug&&g("cached model:",R0.modelUrl):R0=await L(e.face.ssrnet.modelPathAge),R0}async function n5(e,t,n,o){var a,i,c,d;if(!R0)return{age:0};let s=t5<(((a=t.face.ssrnet)==null?void 0:a.skipFrames)||0),A=(((i=t.face.ssrnet)==null?void 0:i.skipTime)||0)>R()-v3;return t.skipAllowed&&s&&A&&T3===o&&((c=B2[n])!=null&&c.age)&&((d=B2[n])==null?void 0:d.age)>0?(t5++,B2[n]):(t5=0,new Promise(async x=>{var y,p,m;if(!(R0!=null&&R0.inputs)||!R0.inputs[0]||!R0.inputs[0].shape)return;let l={};if(((y=t.face.ssrnet)==null?void 0:y.crop)>0){let b=(p=t.face.ssrnet)==null?void 0:p.crop,v=[[b,b,1-b,1-b]];l.resize=r.image.cropAndResize(e,v,[0],[R0.inputs[0].shape[2],R0.inputs[0].shape[1]])}else l.resize=r.image.resizeBilinear(e,[R0.inputs[0].shape[2],R0.inputs[0].shape[1]],!1);l.enhance=r.mul(l.resize,O.tf255);let f={age:0};if((m=t.face.ssrnet)!=null&&m.enabled&&(l.age=R0.execute(l.enhance)),l.age){let b=await l.age.data();f.age=Math.trunc(10*b[0])/10}Object.keys(l).forEach(b=>r.dispose(l[b])),B2[n]=f,T3=o,v3=R(),x(f)}))}var u0,H2=[],P3=0,k3=0,o5=Number.MAX_SAFE_INTEGER,r5=[.2989,.587,.114];async function w3(e){var t;return M.initial&&(u0=null),u0?e.debug&&g("cached model:",u0.modelUrl):u0=await L((t=e.face.ssrnet)==null?void 0:t.modelPathGender),u0}async function s5(e,t,n,o){var a,i,c,d;if(!u0)return{gender:"unknown",genderScore:0};let s=o5<(((a=t.face.ssrnet)==null?void 0:a.skipFrames)||0),A=(((i=t.face.ssrnet)==null?void 0:i.skipTime)||0)>R()-k3;return t.skipAllowed&&s&&A&&P3===o&&((c=H2[n])!=null&&c.gender)&&((d=H2[n])==null?void 0:d.genderScore)>0?(o5++,H2[n]):(o5=0,new Promise(async x=>{var p,m,b;if(!(u0!=null&&u0.inputs[0].shape))return;let l={};if(((p=t.face.ssrnet)==null?void 0:p.crop)>0){let v=(m=t.face.ssrnet)==null?void 0:m.crop,T=[[v,v,1-v,1-v]];l.resize=r.image.cropAndResize(e,T,[0],[u0.inputs[0].shape[2],u0.inputs[0].shape[1]])}else l.resize=r.image.resizeBilinear(e,[u0.inputs[0].shape[2],u0.inputs[0].shape[1]],!1);l.enhance=r.tidy(()=>{var T,u;let v;if(((u=(T=u0==null?void 0:u0.inputs)==null?void 0:T[0].shape)==null?void 0:u[3])===1){let[h,w,k]=r.split(l.resize,3,3),I=r.mul(h,r5[0]),C=r.mul(w,r5[1]),V=r.mul(k,r5[2]),W=r.addN([I,C,V]);v=r.mul(r.sub(W,O.tf05),2)}else v=r.mul(r.sub(l.resize,O.tf05),2);return v});let f={gender:"unknown",genderScore:0};(b=t.face.ssrnet)!=null&&b.enabled&&(l.gender=u0.execute(l.enhance));let y=await l.gender.data();f.gender=y[0]>y[1]?"female":"male",f.genderScore=y[0]>y[1]?Math.trunc(100*y[0])/100:Math.trunc(100*y[1])/100,Object.keys(l).forEach(v=>r.dispose(l[v])),H2[n]=f,P3=o,k3=R(),x(f)}))}var S0,A5=[],z3=0,S3=0,j3=Number.MAX_SAFE_INTEGER;async function N3(e){var t;return M.initial&&(S0=null),S0?e.debug&&g("cached model:",S0.modelUrl):S0=await L((t=e.face.mobilefacenet)==null?void 0:t.modelPath),S0}async function a5(e,t,n,o){var a,i;if(!(S0!=null&&S0.executor))return[];let s=j3<(((a=t.face.mobilefacenet)==null?void 0:a.skipFrames)||0),A=(((i=t.face.mobilefacenet)==null?void 0:i.skipTime)||0)>R()-S3;return t.skipAllowed&&A&&s&&z3===o&&A5[n]?(j3++,A5[n]):new Promise(async c=>{var x;let d=[];if((x=t.face.mobilefacenet)!=null&&x.enabled&&(S0!=null&&S0.inputs[0].shape)){let l={};l.crop=r.image.resizeBilinear(e,[S0.inputs[0].shape[2],S0.inputs[0].shape[1]],!1),l.data=S0.execute(l.crop);let f=await l.data.data();d=Array.from(f),Object.keys(l).forEach(y=>r.dispose(l[y]))}A5[n]=d,z3=o,S3=R(),c(d)})}var j0,i5=[],L3=0,O3=0,C3=Number.MAX_SAFE_INTEGER;async function W3(e){return M.initial&&(j0=null),j0?e.debug&&g("cached model:",j0.modelUrl):j0=await L(e.face.insightface.modelPath),j0}async function l5(e,t,n,o){var a,i;if(!(j0!=null&&j0.executor))return[];let s=C3<(((a=t.face.insightface)==null?void 0:a.skipFrames)||0),A=(((i=t.face.insightface)==null?void 0:i.skipTime)||0)>R()-O3;return t.skipAllowed&&A&&s&&L3===o&&i5[n]?(C3++,i5[n]):new Promise(async c=>{var x;let d=[];if((x=t.face.insightface)!=null&&x.enabled&&(j0!=null&&j0.inputs[0].shape)){let l={};l.crop=r.image.resizeBilinear(e,[j0.inputs[0].shape[2],j0.inputs[0].shape[1]],!1),l.data=j0.execute(l.crop);let f=await l.data.data();d=Array.from(f),Object.keys(l).forEach(y=>r.dispose(l[y]))}i5[n]=d,L3=o,O3=R(),c(d)})}var ks=e=>{let t=(l,f)=>Math.atan2(l[1]-f[1],l[0]-f[0]);if(!e.annotations.rightEyeIris||!e.annotations.leftEyeIris)return{bearing:0,strength:0};let n=[0,-.1],o=1,s=(e.mesh[33][2]||0)>(e.mesh[263][2]||0),A=s?e.mesh[473]:e.mesh[468],a=s?[(e.mesh[133][0]+e.mesh[33][0])/2,(e.mesh[133][1]+e.mesh[33][1])/2]:[(e.mesh[263][0]+e.mesh[362][0])/2,(e.mesh[263][1]+e.mesh[362][1])/2],i=s?[e.mesh[133][0]-e.mesh[33][0],e.mesh[23][1]-e.mesh[27][1]]:[e.mesh[263][0]-e.mesh[362][0],e.mesh[253][1]-e.mesh[257][1]],c=[(a[0]-A[0])/i[0]-n[0],o*(A[1]-a[1])/i[1]-n[1]],d=Math.sqrt(c[0]*c[0]+c[1]*c[1]);return d=Math.min(d,e.boxRaw[2]/2,e.boxRaw[3]/2),{bearing:(t([0,0],c)+Math.PI/2)%Math.PI,strength:d}},F3=(e,t)=>{let n=m=>{let b=Math.sqrt(m[0]*m[0]+m[1]*m[1]+m[2]*m[2]);return m[0]/=b,m[1]/=b,m[2]/=b,m},o=(m,b)=>{let v=m[0]-b[0],T=m[1]-b[1],u=m[2]-b[2];return[v,T,u]},s=(m,b)=>{let v=m[1]*b[2]-m[2]*b[1],T=m[2]*b[0]-m[0]*b[2],u=m[0]*b[1]-m[1]*b[0];return[v,T,u]},A=m=>{let[b,v,T,u,h,w,k,I,C]=m,V,W,G;if(u<1)if(u>-1){let X=Math.sqrt(b*b+k*k);G=Math.atan2(u,X),W=Math.atan2(-k,b),V=Math.atan2(-w,h)}else G=-Math.PI/2,W=-Math.atan2(I,C),V=0;else G=Math.PI/2,W=Math.atan2(I,C),V=0;return Number.isNaN(V)&&(V=0),Number.isNaN(W)&&(W=0),Number.isNaN(G)&&(G=0),{pitch:-V,yaw:-W,roll:-G}},a=e.meshRaw;if(!a||a.length<300)return{angle:{pitch:0,yaw:0,roll:0},matrix:[1,0,0,0,1,0,0,0,1],gaze:{bearing:0,strength:0}};let i=Math.max(e.boxRaw[2]*t[0],e.boxRaw[3]*t[1])/1.5,c=[a[10],a[152],a[234],a[454]].map(m=>[m[0]*t[0]/i,m[1]*t[1]/i,m[2]]),d=n(o(c[1],c[0])),x=n(o(c[3],c[2])),l=n(s(x,d));x=s(d,l);let f=[x[0],x[1],x[2],d[0],d[1],d[2],l[0],l[1],l[2]],y=A(f),p=a.length===478?ks(e):{bearing:0,strength:0};return{angle:y,matrix:f,gaze:p}};function B3(e,t){let n=e==null?void 0:e.annotations;if(!(n!=null&&n.leftEyeIris)||!(n!=null&&n.rightEyeIris))return 0;let o=Math.max(Math.abs(n.leftEyeIris[3][0]-n.leftEyeIris[1][0]),Math.abs(n.rightEyeIris[3][0]-n.rightEyeIris[1][0]))/t;return Math.round(1.17/o)/100}var c5=async(e,t)=>{var p,m,b,v,T,u,h,w,k,I,C,V,W,G,X,U,Y,p0,P,i0,g0,e0,Z;let n=R(),o,s,A,a,i,c,d,x,l,f=[];e.state="run:face";let y=await J1(t,e.config);if(e.performance.face=M.perfadd?(e.performance.face||0)+Math.trunc(R()-n):Math.trunc(R()-n),!t.shape||t.shape.length!==4)return[];if(!y)return[];for(let S=0;S<y.length;S++){if(e.analyze("Get Face"),!y[S].tensor||y[S].tensor.isDisposedInternal){g("Face object is disposed:",y[S].tensor);continue}if((p=e.config.face.detector)!=null&&p.mask){let oe=await i3(y[S]);r.dispose(y[S].tensor),oe&&(y[S].tensor=oe)}let D=y[S].mesh&&y[S].mesh.length>200?F3(y[S],[t.shape[2],t.shape[1]]):null;e.analyze("Start Emotion:"),e.config.async?a=(m=e.config.face.emotion)!=null&&m.enabled?Zt(y[S].tensor||r.tensor([]),e.config,S,y.length):[]:(e.state="run:emotion",n=R(),a=(b=e.config.face.emotion)!=null&&b.enabled?await Zt(y[S].tensor||r.tensor([]),e.config,S,y.length):[],e.performance.emotion=M.perfadd?(e.performance.emotion||0)+Math.trunc(R()-n):Math.trunc(R()-n)),e.analyze("End Emotion:"),e.analyze("Start AntiSpoof:"),e.config.async?d=(v=e.config.face.antispoof)!=null&&v.enabled?Kt(y[S].tensor||r.tensor([]),e.config,S,y.length):0:(e.state="run:antispoof",n=R(),d=(T=e.config.face.antispoof)!=null&&T.enabled?await Kt(y[S].tensor||r.tensor([]),e.config,S,y.length):0,e.performance.antispoof=M.perfadd?(e.performance.antispoof||0)+Math.trunc(R()-n):Math.trunc(R()-n)),e.analyze("End AntiSpoof:"),e.analyze("Start Liveness:"),e.config.async?x=(u=e.config.face.liveness)!=null&&u.enabled?Qt(y[S].tensor||r.tensor([]),e.config,S,y.length):0:(e.state="run:liveness",n=R(),x=(h=e.config.face.liveness)!=null&&h.enabled?await Qt(y[S].tensor||r.tensor([]),e.config,S,y.length):0,e.performance.liveness=M.perfadd?(e.performance.antispoof||0)+Math.trunc(R()-n):Math.trunc(R()-n)),e.analyze("End Liveness:"),e.analyze("Start GEAR:"),e.config.async?s=(w=e.config.face.gear)!=null&&w.enabled?e5(y[S].tensor||r.tensor([]),e.config,S,y.length):null:(e.state="run:gear",n=R(),s=(k=e.config.face.gear)!=null&&k.enabled?await e5(y[S].tensor||r.tensor([]),e.config,S,y.length):null,e.performance.gear=Math.trunc(R()-n)),e.analyze("End GEAR:"),e.analyze("Start SSRNet:"),e.config.async?(o=(I=e.config.face.ssrnet)!=null&&I.enabled?n5(y[S].tensor||r.tensor([]),e.config,S,y.length):null,A=(C=e.config.face.ssrnet)!=null&&C.enabled?s5(y[S].tensor||r.tensor([]),e.config,S,y.length):null):(e.state="run:ssrnet",n=R(),o=(V=e.config.face.ssrnet)!=null&&V.enabled?await n5(y[S].tensor||r.tensor([]),e.config,S,y.length):null,A=(W=e.config.face.ssrnet)!=null&&W.enabled?await s5(y[S].tensor||r.tensor([]),e.config,S,y.length):null,e.performance.ssrnet=Math.trunc(R()-n)),e.analyze("End SSRNet:"),e.analyze("Start MobileFaceNet:"),e.config.async?i=(G=e.config.face.mobilefacenet)!=null&&G.enabled?a5(y[S].tensor||r.tensor([]),e.config,S,y.length):null:(e.state="run:mobilefacenet",n=R(),i=(X=e.config.face.mobilefacenet)!=null&&X.enabled?await a5(y[S].tensor||r.tensor([]),e.config,S,y.length):null,e.performance.mobilefacenet=Math.trunc(R()-n)),e.analyze("End MobileFaceNet:"),e.analyze("Start InsightFace:"),e.config.async?c=(U=e.config.face.insightface)!=null&&U.enabled?l5(y[S].tensor||r.tensor([]),e.config,S,y.length):null:(e.state="run:mobilefacenet",n=R(),c=(Y=e.config.face.insightface)!=null&&Y.enabled?await l5(y[S].tensor||r.tensor([]),e.config,S,y.length):null,e.performance.mobilefacenet=Math.trunc(R()-n)),e.analyze("End InsightFace:"),e.analyze("Start Description:"),e.config.async?l=qt(y[S].tensor||r.tensor([]),e.config,S,y.length):(e.state="run:description",n=R(),l=await qt(y[S].tensor||r.tensor([]),e.config,S,y.length),e.performance.description=M.perfadd?(e.performance.description||0)+Math.trunc(R()-n):Math.trunc(R()-n)),e.analyze("End Description:"),e.config.async&&([o,A,a,i,c,l,s,d,x]=await Promise.all([o,A,a,i,c,l,s,d,x])),e.analyze("Finish Face:"),(p0=e.config.face.ssrnet)!=null&&p0.enabled&&o&&A&&(l={...l,age:o.age,gender:A.gender,genderScore:A.genderScore}),(P=e.config.face.gear)!=null&&P.enabled&&s&&(l={...l,age:s.age,gender:s.gender,genderScore:s.genderScore,race:s.race}),(i0=e.config.face.mobilefacenet)!=null&&i0.enabled&&i&&(l.descriptor=i),(g0=e.config.face.insightface)!=null&&g0.enabled&&c&&(l.descriptor=c);let F=(e0=e.config.face.iris)!=null&&e0.enabled?B3(y[S],t.shape[2]):0,w0=(Z=e.config.face.detector)!=null&&Z.return?r.squeeze(y[S].tensor):null;r.dispose(y[S].tensor),y[S].tensor&&delete y[S].tensor;let t0={...y[S],id:S};l.age&&(t0.age=l.age),l.gender&&(t0.gender=l.gender),l.genderScore&&(t0.genderScore=l.genderScore),l.descriptor&&(t0.embedding=l.descriptor),l.race&&(t0.race=l.race),a&&(t0.emotion=a),d&&(t0.real=d),x&&(t0.live=x),F>0&&(t0.distance=F),D&&(t0.rotation=D),w0&&(t0.tensor=w0),f.push(t0),e.analyze("End Face")}return e.analyze("End FaceMesh:"),e.config.async&&(e.performance.face&&delete e.performance.face,e.performance.age&&delete e.performance.age,e.performance.gender&&delete e.performance.gender,e.performance.emotion&&delete e.performance.emotion),f};var M0={thumb:0,index:1,middle:2,ring:3,pinky:4,all:[0,1,2,3,4],nameMapping:{0:"thumb",1:"index",2:"middle",3:"ring",4:"pinky"},pointsMapping:{0:[[0,1],[1,2],[2,3],[3,4]],1:[[0,5],[5,6],[6,7],[7,8]],2:[[0,9],[9,10],[10,11],[11,12]],3:[[0,13],[13,14],[14,15],[15,16]],4:[[0,17],[17,18],[18,19],[19,20]]},getName:e=>M0.nameMapping[e],getPoints:e=>M0.pointsMapping[e]},ye={none:0,half:1,full:2,nameMapping:{0:"none",1:"half",2:"full"},getName:e=>ye.nameMapping[e]},$={verticalUp:0,verticalDown:1,horizontalLeft:2,horizontalRight:3,diagonalUpRight:4,diagonalUpLeft:5,diagonalDownRight:6,diagonalDownLeft:7,nameMapping:{0:"verticalUp",1:"verticalDown",2:"horizontalLeft",3:"horizontalRight",4:"diagonalUpRight",5:"diagonalUpLeft",6:"diagonalDownRight",7:"diagonalDownLeft"},getName:e=>$.nameMapping[e]},xe=class{constructor(t){z(this,"name");z(this,"curls");z(this,"directions");z(this,"weights");z(this,"weightsRelative");this.name=t,this.curls={},this.directions={},this.weights=[1,1,1,1,1],this.weightsRelative=[1,1,1,1,1]}curl(t,n,o){typeof this.curls[t]=="undefined"&&(this.curls[t]=[]),this.curls[t].push([n,o])}direction(t,n,o){this.directions[t]||(this.directions[t]=[]),this.directions[t].push([n,o])}weight(t,n){this.weights[t]=n;let o=this.weights.reduce((s,A)=>s+A,0);this.weightsRelative=this.weights.map(s=>s*5/o)}matchAgainst(t,n){let o=0;for(let s in t){let A=t[s],a=this.curls[s];if(typeof a=="undefined"){o+=this.weightsRelative[s];continue}for(let[i,c]of a)if(A===i){o+=c*this.weightsRelative[s];break}}for(let s in n){let A=n[s],a=this.directions[s];if(typeof a=="undefined"){o+=this.weightsRelative[s];continue}for(let[i,c]of a)if(A===i){o+=c*this.weightsRelative[s];break}}return o/10}};var{thumb:V0,index:$0,middle:ee,ring:ze,pinky:Se}=M0,{none:Z0,half:Es,full:X0}=ye,{verticalUp:Ze,verticalDown:ri,horizontalLeft:d5,horizontalRight:zs,diagonalUpRight:Ss,diagonalUpLeft:Xe,diagonalDownRight:si,diagonalDownLeft:Ai}=$,fe=new xe("thumbs up");fe.curl(V0,Z0,1);fe.direction(V0,Ze,1);fe.direction(V0,Xe,.25);fe.direction(V0,Ss,.25);for(let e of[M0.index,M0.middle,M0.ring,M0.pinky])fe.curl(e,X0,1),fe.direction(e,d5,1),fe.direction(e,zs,1);var s0=new xe("victory");s0.curl(V0,Es,.5);s0.curl(V0,Z0,.5);s0.direction(V0,Ze,1);s0.direction(V0,Xe,1);s0.curl($0,Z0,1);s0.direction($0,Ze,.75);s0.direction($0,Xe,1);s0.curl(ee,Z0,1);s0.direction(ee,Ze,1);s0.direction(ee,Xe,.75);s0.curl(ze,X0,1);s0.direction(ze,Ze,.2);s0.direction(ze,Xe,1);s0.direction(ze,d5,.2);s0.curl(Se,X0,1);s0.direction(Se,Ze,.2);s0.direction(Se,Xe,1);s0.direction(Se,d5,.2);s0.weight($0,2);s0.weight(ee,2);var me=new xe("point");me.curl(V0,X0,1);me.curl($0,Z0,.5);me.curl(ee,X0,.5);me.curl(ze,X0,.5);me.curl(Se,X0,.5);me.weight($0,2);me.weight(ee,2);var pe=new xe("middle finger");pe.curl(V0,Z0,1);pe.curl($0,X0,.5);pe.curl(ee,X0,.5);pe.curl(ze,X0,.5);pe.curl(Se,X0,.5);pe.weight($0,2);pe.weight(ee,2);var qe=new xe("open palm");qe.curl(V0,Z0,.75);qe.curl($0,Z0,.75);qe.curl(ee,Z0,.75);qe.curl(ze,Z0,.75);qe.curl(Se,Z0,.75);var H3=[fe,s0,me,pe,qe];var js=.7,je={HALF_CURL_START_LIMIT:60,NO_CURL_START_LIMIT:130,DISTANCE_VOTE_POWER:1.1,SINGLE_ANGLE_VOTE_POWER:.9,TOTAL_ANGLE_VOTE_POWER:1.6};function G3(e,t,n,o){let s=(t-o)/(e-n),A=Math.atan(s)*180/Math.PI;return A<=0?A=-A:A>0&&(A=180-A),A}function Z3(e,t){if(!e||!t)return[0,0];let n=G3(e[0],e[1],t[0],t[1]);if(e.length===2)return n;let o=G3(e[1],e[2],t[1],t[2]);return[n,o]}function V3(e,t=1){let n=0,o=0,s=0;return e>=75&&e<=105?n=1*t:e>=25&&e<=155?o=1*t:s=1*t,[n,o,s]}function Ns(e,t,n){let o=e[0]-t[0],s=e[0]-n[0],A=t[0]-n[0],a=e[1]-t[1],i=e[1]-n[1],c=t[1]-n[1],d=e[2]-t[2],x=e[2]-n[2],l=t[2]-n[2],f=Math.sqrt(o*o+a*a+d*d),y=Math.sqrt(s*s+i*i+x*x),p=Math.sqrt(A*A+c*c+l*l),m=(p*p+f*f-y*y)/(2*p*f);m>1?m=1:m<-1&&(m=-1);let b=Math.acos(m);b=57.2958*b%180;let v;return b>je.NO_CURL_START_LIMIT?v=ye.none:b>je.HALF_CURL_START_LIMIT?v=ye.half:v=ye.full,v}function X3(e,t,n,o){let s;return o===Math.abs(e)?e>0?s=$.horizontalLeft:s=$.horizontalRight:o===Math.abs(t)?t>0?s=$.horizontalLeft:s=$.horizontalRight:n>0?s=$.horizontalLeft:s=$.horizontalRight,s}function q3(e,t,n,o){let s;return o===Math.abs(e)?e<0?s=$.verticalDown:s=$.verticalUp:o===Math.abs(t)?t<0?s=$.verticalDown:s=$.verticalUp:n<0?s=$.verticalDown:s=$.verticalUp,s}function Is(e,t,n,o,s,A,a,i){let c,d=q3(e,t,n,o),x=X3(s,A,a,i);return d===$.verticalUp?x===$.horizontalLeft?c=$.diagonalUpLeft:c=$.diagonalUpRight:x===$.horizontalLeft?c=$.diagonalDownLeft:c=$.diagonalDownRight,c}function Ls(e,t,n,o){let s=e[0]-t[0],A=e[0]-n[0],a=t[0]-n[0],i=e[1]-t[1],c=e[1]-n[1],d=t[1]-n[1],x=Math.max(Math.abs(s),Math.abs(A),Math.abs(a)),l=Math.max(Math.abs(i),Math.abs(c),Math.abs(d)),f=0,y=0,p=0,m=l/(x+1e-5);m>1.5?f+=je.DISTANCE_VOTE_POWER:m>.66?y+=je.DISTANCE_VOTE_POWER:p+=je.DISTANCE_VOTE_POWER;let b=Math.sqrt(s*s+i*i),v=Math.sqrt(A*A+c*c),T=Math.sqrt(a*a+d*d),u=Math.max(b,v,T),h=e[0],w=e[1],k=n[0],I=n[1];u===b?(k=n[0],I=n[1]):u===T&&(h=t[0],w=t[1]);let W=Z3([h,w],[k,I]),G=V3(W,je.TOTAL_ANGLE_VOTE_POWER);f+=G[0],y+=G[1],p+=G[2];for(let U of o){let Y=V3(U,je.SINGLE_ANGLE_VOTE_POWER);f+=Y[0],y+=Y[1],p+=Y[2]}let X;return f===Math.max(f,y,p)?X=q3(c,i,d,l):p===Math.max(y,p)?X=X3(A,s,a,x):X=Is(c,i,d,l,A,s,a,x),X}function U3(e){let t=[],n=[],o=[],s=[];if(!e)return{curls:o,directions:s};for(let A of M0.all){let a=M0.getPoints(A),i=[],c=[];for(let d of a){let x=e[d[0]],l=e[d[1]],f=Z3(x,l),y=f[0],p=f[1];i.push(y),c.push(p)}t.push(i),n.push(c)}for(let A of M0.all){let a=A===M0.thumb?1:0,i=M0.getPoints(A),c=e[i[a][0]],d=e[i[a+1][1]],x=e[i[3][1]],l=Ns(c,d,x),f=Ls(c,d,x,t[A].slice(a));o[A]=l,s[A]=f}return{curls:o,directions:s}}function G2(e){if(!e||e.length===0)return null;let t=U3(e),n={};for(let o of M0.all)n[M0.getName(o)]={curl:ye.getName(t.curls[o]),direction:$.getName(t.directions[o])};return n}function Y3(e){let t=[];if(!e||e.length===0)return t;let n=U3(e);for(let o of H3){let s=o.matchAgainst(n.curls,n.directions);s>=js&&t.push({name:o.name,confidence:s})}return t}var K3=e=>{if(!e)return[];let t=[];for(let n=0;n<e.length;n++){let o=e[n].keypoints.find(c=>c.part==="leftWrist"),s=e[n].keypoints.find(c=>c.part==="rightWrist"),A=e[n].keypoints.find(c=>c.part==="nose");A&&o&&s&&o.position[1]<A.position[1]&&s.position[1]<A.position[1]?t.push({body:n,gesture:"i give up"}):A&&o&&o.position[1]<A.position[1]?t.push({body:n,gesture:"raise left hand"}):A&&s&&s.position[1]<A.position[1]&&t.push({body:n,gesture:"raise right hand"});let a=e[n].keypoints.find(c=>c.part==="leftShoulder"),i=e[n].keypoints.find(c=>c.part==="rightShoulder");a&&i&&Math.abs(a.positionRaw[1]-i.positionRaw[1])>.1&&t.push({body:n,gesture:`leaning ${a.position[1]>i.position[1]?"left":"right"}`})}return t},J3=e=>{if(!e)return[];let t=[];for(let n=0;n<e.length;n++)if(e[n].mesh&&e[n].mesh.length>450){let o=(e[n].mesh[33][2]||0)-(e[n].mesh[263][2]||0),s=e[n].mesh[33][0]-e[n].mesh[263][0];Math.abs(o/s)<=.15?t.push({face:n,gesture:"facing center"}):t.push({face:n,gesture:`facing ${o<0?"left":"right"}`}),Math.abs(e[n].mesh[374][1]-e[n].mesh[386][1])/Math.abs(e[n].mesh[443][1]-e[n].mesh[450][1])<.2&&t.push({face:n,gesture:"blink left eye"}),Math.abs(e[n].mesh[145][1]-e[n].mesh[159][1])/Math.abs(e[n].mesh[223][1]-e[n].mesh[230][1])<.2&&t.push({face:n,gesture:"blink right eye"});let i=Math.min(100,500*Math.abs(e[n].mesh[13][1]-e[n].mesh[14][1])/Math.abs(e[n].mesh[10][1]-e[n].mesh[152][1]));i>10&&t.push({face:n,gesture:`mouth ${Math.trunc(i)}% open`});let c=e[n].mesh[152][2]||0;Math.abs(c)>10&&t.push({face:n,gesture:`head ${c<0?"up":"down"}`})}return t},Q3=e=>{var n,o,s,A;if(!e)return[];let t=[];for(let a=0;a<e.length;a++){if(!((o=(n=e[a].annotations)==null?void 0:n.leftEyeIris)!=null&&o[0])||!((A=(s=e[a].annotations)==null?void 0:s.rightEyeIris)!=null&&A[0]))continue;let i=e[a].annotations.leftEyeIris[3][0]-e[a].annotations.leftEyeIris[1][0],c=e[a].annotations.leftEyeIris[4][1]-e[a].annotations.leftEyeIris[2][1],d=Math.abs(i*c),x=e[a].annotations.rightEyeIris[3][0]-e[a].annotations.rightEyeIris[1][0],l=e[a].annotations.rightEyeIris[4][1]-e[a].annotations.rightEyeIris[2][1],f=Math.abs(x*l),y=!1;Math.abs(d-f)/Math.max(d,f)<.25&&(y=!0,t.push({iris:a,gesture:"facing center"}));let m=Math.abs(e[a].mesh[263][0]-e[a].annotations.leftEyeIris[0][0])/e[a].box[2],b=Math.abs(e[a].mesh[33][0]-e[a].annotations.rightEyeIris[0][0])/e[a].box[2];(m>.06||b>.06)&&(y=!1),m>b?b>.04&&t.push({iris:a,gesture:"looking right"}):m>.04&&t.push({iris:a,gesture:"looking left"});let v=Math.abs(e[a].mesh[145][1]-e[a].annotations.rightEyeIris[0][1])/e[a].box[3],T=Math.abs(e[a].mesh[374][1]-e[a].annotations.leftEyeIris[0][1])/e[a].box[3];(T<.01||v<.01||T>.022||v>.022)&&(y=!1),(T<.01||v<.01)&&t.push({iris:a,gesture:"looking down"}),(T>.022||v>.022)&&t.push({iris:a,gesture:"looking up"}),y&&t.push({iris:a,gesture:"looking center"})}return t},_3=e=>{if(!e)return[];let t=[];for(let n=0;n<e.length;n++){let o=[];if(e[n].annotations)for(let[s,A]of Object.entries(e[n].annotations))s!=="palmBase"&&Array.isArray(A)&&A[0]&&o.push({name:s.toLowerCase(),position:A[0]});if(o&&o.length>0){let s=o.reduce((a,i)=>(a.position[2]||0)<(i.position[2]||0)?a:i);t.push({hand:n,gesture:`${s.name} forward`});let A=o.reduce((a,i)=>a.position[1]<i.position[1]?a:i);t.push({hand:n,gesture:`${A.name} up`})}if(e[n].keypoints){let s=Y3(e[n].keypoints);for(let A of s)t.push({hand:n,gesture:A.name})}}return t};function V2(e){return[Math.abs(e.endPoint[0]-e.startPoint[0]),Math.abs(e.endPoint[1]-e.startPoint[1])]}function r2(e){return[e.startPoint[0]+(e.endPoint[0]-e.startPoint[0])/2,e.startPoint[1]+(e.endPoint[1]-e.startPoint[1])/2]}function tn(e,t,n){let o=t.shape[1],s=t.shape[2],A=[[e.startPoint[1]/o,e.startPoint[0]/s,e.endPoint[1]/o,e.endPoint[0]/s]];return r.image.cropAndResize(t,A,[0],n)}function nn(e,t){let n=[e.startPoint[0]*t[0],e.startPoint[1]*t[1]],o=[e.endPoint[0]*t[0],e.endPoint[1]*t[1]],s=e.palmLandmarks.map(A=>[A[0]*t[0],A[1]*t[1]]);return{startPoint:n,endPoint:o,palmLandmarks:s,confidence:e.confidence}}function Z2(e,t=1.5){let n=r2(e),o=V2(e),s=[t*o[0]/2,t*o[1]/2],A=[n[0]-s[0],n[1]-s[1]],a=[n[0]+s[0],n[1]+s[1]];return{startPoint:A,endPoint:a,palmLandmarks:e.palmLandmarks}}function X2(e){let t=r2(e),n=V2(e),s=Math.max(...n)/2,A=[t[0]-s,t[1]-s],a=[t[0]+s,t[1]+s];return{startPoint:A,endPoint:a,palmLandmarks:e.palmLandmarks}}function Cs(e){return e-2*Math.PI*Math.floor((e+Math.PI)/(2*Math.PI))}function on(e,t){let n=Math.PI/2-Math.atan2(-(t[1]-e[1]),t[0]-e[0]);return Cs(n)}var $3=(e,t)=>[[1,0,e],[0,1,t],[0,0,1]];function ue(e,t){let n=0;for(let o=0;o<e.length;o++)n+=e[o]*t[o];return n}function Ws(e,t){let n=[];for(let o=0;o<e.length;o++)n.push(e[o][t]);return n}function en(e,t){let n=[],o=e.length;for(let s=0;s<o;s++){n.push([]);for(let A=0;A<o;A++)n[s].push(ue(e[s],Ws(t,A)))}return n}function y5(e,t){let n=Math.cos(e),o=Math.sin(e),s=[[n,-o,0],[o,n,0],[0,0,1]],A=$3(t[0],t[1]),a=en(A,s),i=$3(-t[0],-t[1]);return en(a,i)}function rn(e){let t=[[e[0][0],e[1][0]],[e[0][1],e[1][1]]],n=[e[0][2],e[1][2]],o=[-ue(t[0],n),-ue(t[1],n)];return[t[0].concat(o[0]),t[1].concat(o[1]),[0,0,1]]}function f5(e,t){return[ue(e,t[0]),ue(e,t[1])]}var An=[{x:.015625,y:.015625},{x:.015625,y:.015625},{x:.046875,y:.015625},{x:.046875,y:.015625},{x:.078125,y:.015625},{x:.078125,y:.015625},{x:.109375,y:.015625},{x:.109375,y:.015625},{x:.140625,y:.015625},{x:.140625,y:.015625},{x:.171875,y:.015625},{x:.171875,y:.015625},{x:.203125,y:.015625},{x:.203125,y:.015625},{x:.234375,y:.015625},{x:.234375,y:.015625},{x:.265625,y:.015625},{x:.265625,y:.015625},{x:.296875,y:.015625},{x:.296875,y:.015625},{x:.328125,y:.015625},{x:.328125,y:.015625},{x:.359375,y:.015625},{x:.359375,y:.015625},{x:.390625,y:.015625},{x:.390625,y:.015625},{x:.421875,y:.015625},{x:.421875,y:.015625},{x:.453125,y:.015625},{x:.453125,y:.015625},{x:.484375,y:.015625},{x:.484375,y:.015625},{x:.515625,y:.015625},{x:.515625,y:.015625},{x:.546875,y:.015625},{x:.546875,y:.015625},{x:.578125,y:.015625},{x:.578125,y:.015625},{x:.609375,y:.015625},{x:.609375,y:.015625},{x:.640625,y:.015625},{x:.640625,y:.015625},{x:.671875,y:.015625},{x:.671875,y:.015625},{x:.703125,y:.015625},{x:.703125,y:.015625},{x:.734375,y:.015625},{x:.734375,y:.015625},{x:.765625,y:.015625},{x:.765625,y:.015625},{x:.796875,y:.015625},{x:.796875,y:.015625},{x:.828125,y:.015625},{x:.828125,y:.015625},{x:.859375,y:.015625},{x:.859375,y:.015625},{x:.890625,y:.015625},{x:.890625,y:.015625},{x:.921875,y:.015625},{x:.921875,y:.015625},{x:.953125,y:.015625},{x:.953125,y:.015625},{x:.984375,y:.015625},{x:.984375,y:.015625},{x:.015625,y:.046875},{x:.015625,y:.046875},{x:.046875,y:.046875},{x:.046875,y:.046875},{x:.078125,y:.046875},{x:.078125,y:.046875},{x:.109375,y:.046875},{x:.109375,y:.046875},{x:.140625,y:.046875},{x:.140625,y:.046875},{x:.171875,y:.046875},{x:.171875,y:.046875},{x:.203125,y:.046875},{x:.203125,y:.046875},{x:.234375,y:.046875},{x:.234375,y:.046875},{x:.265625,y:.046875},{x:.265625,y:.046875},{x:.296875,y:.046875},{x:.296875,y:.046875},{x:.328125,y:.046875},{x:.328125,y:.046875},{x:.359375,y:.046875},{x:.359375,y:.046875},{x:.390625,y:.046875},{x:.390625,y:.046875},{x:.421875,y:.046875},{x:.421875,y:.046875},{x:.453125,y:.046875},{x:.453125,y:.046875},{x:.484375,y:.046875},{x:.484375,y:.046875},{x:.515625,y:.046875},{x:.515625,y:.046875},{x:.546875,y:.046875},{x:.546875,y:.046875},{x:.578125,y:.046875},{x:.578125,y:.046875},{x:.609375,y:.046875},{x:.609375,y:.046875},{x:.640625,y:.046875},{x:.640625,y:.046875},{x:.671875,y:.046875},{x:.671875,y:.046875},{x:.703125,y:.046875},{x:.703125,y:.046875},{x:.734375,y:.046875},{x:.734375,y:.046875},{x:.765625,y:.046875},{x:.765625,y:.046875},{x:.796875,y:.046875},{x:.796875,y:.046875},{x:.828125,y:.046875},{x:.828125,y:.046875},{x:.859375,y:.046875},{x:.859375,y:.046875},{x:.890625,y:.046875},{x:.890625,y:.046875},{x:.921875,y:.046875},{x:.921875,y:.046875},{x:.953125,y:.046875},{x:.953125,y:.046875},{x:.984375,y:.046875},{x:.984375,y:.046875},{x:.015625,y:.078125},{x:.015625,y:.078125},{x:.046875,y:.078125},{x:.046875,y:.078125},{x:.078125,y:.078125},{x:.078125,y:.078125},{x:.109375,y:.078125},{x:.109375,y:.078125},{x:.140625,y:.078125},{x:.140625,y:.078125},{x:.171875,y:.078125},{x:.171875,y:.078125},{x:.203125,y:.078125},{x:.203125,y:.078125},{x:.234375,y:.078125},{x:.234375,y:.078125},{x:.265625,y:.078125},{x:.265625,y:.078125},{x:.296875,y:.078125},{x:.296875,y:.078125},{x:.328125,y:.078125},{x:.328125,y:.078125},{x:.359375,y:.078125},{x:.359375,y:.078125},{x:.390625,y:.078125},{x:.390625,y:.078125},{x:.421875,y:.078125},{x:.421875,y:.078125},{x:.453125,y:.078125},{x:.453125,y:.078125},{x:.484375,y:.078125},{x:.484375,y:.078125},{x:.515625,y:.078125},{x:.515625,y:.078125},{x:.546875,y:.078125},{x:.546875,y:.078125},{x:.578125,y:.078125},{x:.578125,y:.078125},{x:.609375,y:.078125},{x:.609375,y:.078125},{x:.640625,y:.078125},{x:.640625,y:.078125},{x:.671875,y:.078125},{x:.671875,y:.078125},{x:.703125,y:.078125},{x:.703125,y:.078125},{x:.734375,y:.078125},{x:.734375,y:.078125},{x:.765625,y:.078125},{x:.765625,y:.078125},{x:.796875,y:.078125},{x:.796875,y:.078125},{x:.828125,y:.078125},{x:.828125,y:.078125},{x:.859375,y:.078125},{x:.859375,y:.078125},{x:.890625,y:.078125},{x:.890625,y:.078125},{x:.921875,y:.078125},{x:.921875,y:.078125},{x:.953125,y:.078125},{x:.953125,y:.078125},{x:.984375,y:.078125},{x:.984375,y:.078125},{x:.015625,y:.109375},{x:.015625,y:.109375},{x:.046875,y:.109375},{x:.046875,y:.109375},{x:.078125,y:.109375},{x:.078125,y:.109375},{x:.109375,y:.109375},{x:.109375,y:.109375},{x:.140625,y:.109375},{x:.140625,y:.109375},{x:.171875,y:.109375},{x:.171875,y:.109375},{x:.203125,y:.109375},{x:.203125,y:.109375},{x:.234375,y:.109375},{x:.234375,y:.109375},{x:.265625,y:.109375},{x:.265625,y:.109375},{x:.296875,y:.109375},{x:.296875,y:.109375},{x:.328125,y:.109375},{x:.328125,y:.109375},{x:.359375,y:.109375},{x:.359375,y:.109375},{x:.390625,y:.109375},{x:.390625,y:.109375},{x:.421875,y:.109375},{x:.421875,y:.109375},{x:.453125,y:.109375},{x:.453125,y:.109375},{x:.484375,y:.109375},{x:.484375,y:.109375},{x:.515625,y:.109375},{x:.515625,y:.109375},{x:.546875,y:.109375},{x:.546875,y:.109375},{x:.578125,y:.109375},{x:.578125,y:.109375},{x:.609375,y:.109375},{x:.609375,y:.109375},{x:.640625,y:.109375},{x:.640625,y:.109375},{x:.671875,y:.109375},{x:.671875,y:.109375},{x:.703125,y:.109375},{x:.703125,y:.109375},{x:.734375,y:.109375},{x:.734375,y:.109375},{x:.765625,y:.109375},{x:.765625,y:.109375},{x:.796875,y:.109375},{x:.796875,y:.109375},{x:.828125,y:.109375},{x:.828125,y:.109375},{x:.859375,y:.109375},{x:.859375,y:.109375},{x:.890625,y:.109375},{x:.890625,y:.109375},{x:.921875,y:.109375},{x:.921875,y:.109375},{x:.953125,y:.109375},{x:.953125,y:.109375},{x:.984375,y:.109375},{x:.984375,y:.109375},{x:.015625,y:.140625},{x:.015625,y:.140625},{x:.046875,y:.140625},{x:.046875,y:.140625},{x:.078125,y:.140625},{x:.078125,y:.140625},{x:.109375,y:.140625},{x:.109375,y:.140625},{x:.140625,y:.140625},{x:.140625,y:.140625},{x:.171875,y:.140625},{x:.171875,y:.140625},{x:.203125,y:.140625},{x:.203125,y:.140625},{x:.234375,y:.140625},{x:.234375,y:.140625},{x:.265625,y:.140625},{x:.265625,y:.140625},{x:.296875,y:.140625},{x:.296875,y:.140625},{x:.328125,y:.140625},{x:.328125,y:.140625},{x:.359375,y:.140625},{x:.359375,y:.140625},{x:.390625,y:.140625},{x:.390625,y:.140625},{x:.421875,y:.140625},{x:.421875,y:.140625},{x:.453125,y:.140625},{x:.453125,y:.140625},{x:.484375,y:.140625},{x:.484375,y:.140625},{x:.515625,y:.140625},{x:.515625,y:.140625},{x:.546875,y:.140625},{x:.546875,y:.140625},{x:.578125,y:.140625},{x:.578125,y:.140625},{x:.609375,y:.140625},{x:.609375,y:.140625},{x:.640625,y:.140625},{x:.640625,y:.140625},{x:.671875,y:.140625},{x:.671875,y:.140625},{x:.703125,y:.140625},{x:.703125,y:.140625},{x:.734375,y:.140625},{x:.734375,y:.140625},{x:.765625,y:.140625},{x:.765625,y:.140625},{x:.796875,y:.140625},{x:.796875,y:.140625},{x:.828125,y:.140625},{x:.828125,y:.140625},{x:.859375,y:.140625},{x:.859375,y:.140625},{x:.890625,y:.140625},{x:.890625,y:.140625},{x:.921875,y:.140625},{x:.921875,y:.140625},{x:.953125,y:.140625},{x:.953125,y:.140625},{x:.984375,y:.140625},{x:.984375,y:.140625},{x:.015625,y:.171875},{x:.015625,y:.171875},{x:.046875,y:.171875},{x:.046875,y:.171875},{x:.078125,y:.171875},{x:.078125,y:.171875},{x:.109375,y:.171875},{x:.109375,y:.171875},{x:.140625,y:.171875},{x:.140625,y:.171875},{x:.171875,y:.171875},{x:.171875,y:.171875},{x:.203125,y:.171875},{x:.203125,y:.171875},{x:.234375,y:.171875},{x:.234375,y:.171875},{x:.265625,y:.171875},{x:.265625,y:.171875},{x:.296875,y:.171875},{x:.296875,y:.171875},{x:.328125,y:.171875},{x:.328125,y:.171875},{x:.359375,y:.171875},{x:.359375,y:.171875},{x:.390625,y:.171875},{x:.390625,y:.171875},{x:.421875,y:.171875},{x:.421875,y:.171875},{x:.453125,y:.171875},{x:.453125,y:.171875},{x:.484375,y:.171875},{x:.484375,y:.171875},{x:.515625,y:.171875},{x:.515625,y:.171875},{x:.546875,y:.171875},{x:.546875,y:.171875},{x:.578125,y:.171875},{x:.578125,y:.171875},{x:.609375,y:.171875},{x:.609375,y:.171875},{x:.640625,y:.171875},{x:.640625,y:.171875},{x:.671875,y:.171875},{x:.671875,y:.171875},{x:.703125,y:.171875},{x:.703125,y:.171875},{x:.734375,y:.171875},{x:.734375,y:.171875},{x:.765625,y:.171875},{x:.765625,y:.171875},{x:.796875,y:.171875},{x:.796875,y:.171875},{x:.828125,y:.171875},{x:.828125,y:.171875},{x:.859375,y:.171875},{x:.859375,y:.171875},{x:.890625,y:.171875},{x:.890625,y:.171875},{x:.921875,y:.171875},{x:.921875,y:.171875},{x:.953125,y:.171875},{x:.953125,y:.171875},{x:.984375,y:.171875},{x:.984375,y:.171875},{x:.015625,y:.203125},{x:.015625,y:.203125},{x:.046875,y:.203125},{x:.046875,y:.203125},{x:.078125,y:.203125},{x:.078125,y:.203125},{x:.109375,y:.203125},{x:.109375,y:.203125},{x:.140625,y:.203125},{x:.140625,y:.203125},{x:.171875,y:.203125},{x:.171875,y:.203125},{x:.203125,y:.203125},{x:.203125,y:.203125},{x:.234375,y:.203125},{x:.234375,y:.203125},{x:.265625,y:.203125},{x:.265625,y:.203125},{x:.296875,y:.203125},{x:.296875,y:.203125},{x:.328125,y:.203125},{x:.328125,y:.203125},{x:.359375,y:.203125},{x:.359375,y:.203125},{x:.390625,y:.203125},{x:.390625,y:.203125},{x:.421875,y:.203125},{x:.421875,y:.203125},{x:.453125,y:.203125},{x:.453125,y:.203125},{x:.484375,y:.203125},{x:.484375,y:.203125},{x:.515625,y:.203125},{x:.515625,y:.203125},{x:.546875,y:.203125},{x:.546875,y:.203125},{x:.578125,y:.203125},{x:.578125,y:.203125},{x:.609375,y:.203125},{x:.609375,y:.203125},{x:.640625,y:.203125},{x:.640625,y:.203125},{x:.671875,y:.203125},{x:.671875,y:.203125},{x:.703125,y:.203125},{x:.703125,y:.203125},{x:.734375,y:.203125},{x:.734375,y:.203125},{x:.765625,y:.203125},{x:.765625,y:.203125},{x:.796875,y:.203125},{x:.796875,y:.203125},{x:.828125,y:.203125},{x:.828125,y:.203125},{x:.859375,y:.203125},{x:.859375,y:.203125},{x:.890625,y:.203125},{x:.890625,y:.203125},{x:.921875,y:.203125},{x:.921875,y:.203125},{x:.953125,y:.203125},{x:.953125,y:.203125},{x:.984375,y:.203125},{x:.984375,y:.203125},{x:.015625,y:.234375},{x:.015625,y:.234375},{x:.046875,y:.234375},{x:.046875,y:.234375},{x:.078125,y:.234375},{x:.078125,y:.234375},{x:.109375,y:.234375},{x:.109375,y:.234375},{x:.140625,y:.234375},{x:.140625,y:.234375},{x:.171875,y:.234375},{x:.171875,y:.234375},{x:.203125,y:.234375},{x:.203125,y:.234375},{x:.234375,y:.234375},{x:.234375,y:.234375},{x:.265625,y:.234375},{x:.265625,y:.234375},{x:.296875,y:.234375},{x:.296875,y:.234375},{x:.328125,y:.234375},{x:.328125,y:.234375},{x:.359375,y:.234375},{x:.359375,y:.234375},{x:.390625,y:.234375},{x:.390625,y:.234375},{x:.421875,y:.234375},{x:.421875,y:.234375},{x:.453125,y:.234375},{x:.453125,y:.234375},{x:.484375,y:.234375},{x:.484375,y:.234375},{x:.515625,y:.234375},{x:.515625,y:.234375},{x:.546875,y:.234375},{x:.546875,y:.234375},{x:.578125,y:.234375},{x:.578125,y:.234375},{x:.609375,y:.234375},{x:.609375,y:.234375},{x:.640625,y:.234375},{x:.640625,y:.234375},{x:.671875,y:.234375},{x:.671875,y:.234375},{x:.703125,y:.234375},{x:.703125,y:.234375},{x:.734375,y:.234375},{x:.734375,y:.234375},{x:.765625,y:.234375},{x:.765625,y:.234375},{x:.796875,y:.234375},{x:.796875,y:.234375},{x:.828125,y:.234375},{x:.828125,y:.234375},{x:.859375,y:.234375},{x:.859375,y:.234375},{x:.890625,y:.234375},{x:.890625,y:.234375},{x:.921875,y:.234375},{x:.921875,y:.234375},{x:.953125,y:.234375},{x:.953125,y:.234375},{x:.984375,y:.234375},{x:.984375,y:.234375},{x:.015625,y:.265625},{x:.015625,y:.265625},{x:.046875,y:.265625},{x:.046875,y:.265625},{x:.078125,y:.265625},{x:.078125,y:.265625},{x:.109375,y:.265625},{x:.109375,y:.265625},{x:.140625,y:.265625},{x:.140625,y:.265625},{x:.171875,y:.265625},{x:.171875,y:.265625},{x:.203125,y:.265625},{x:.203125,y:.265625},{x:.234375,y:.265625},{x:.234375,y:.265625},{x:.265625,y:.265625},{x:.265625,y:.265625},{x:.296875,y:.265625},{x:.296875,y:.265625},{x:.328125,y:.265625},{x:.328125,y:.265625},{x:.359375,y:.265625},{x:.359375,y:.265625},{x:.390625,y:.265625},{x:.390625,y:.265625},{x:.421875,y:.265625},{x:.421875,y:.265625},{x:.453125,y:.265625},{x:.453125,y:.265625},{x:.484375,y:.265625},{x:.484375,y:.265625},{x:.515625,y:.265625},{x:.515625,y:.265625},{x:.546875,y:.265625},{x:.546875,y:.265625},{x:.578125,y:.265625},{x:.578125,y:.265625},{x:.609375,y:.265625},{x:.609375,y:.265625},{x:.640625,y:.265625},{x:.640625,y:.265625},{x:.671875,y:.265625},{x:.671875,y:.265625},{x:.703125,y:.265625},{x:.703125,y:.265625},{x:.734375,y:.265625},{x:.734375,y:.265625},{x:.765625,y:.265625},{x:.765625,y:.265625},{x:.796875,y:.265625},{x:.796875,y:.265625},{x:.828125,y:.265625},{x:.828125,y:.265625},{x:.859375,y:.265625},{x:.859375,y:.265625},{x:.890625,y:.265625},{x:.890625,y:.265625},{x:.921875,y:.265625},{x:.921875,y:.265625},{x:.953125,y:.265625},{x:.953125,y:.265625},{x:.984375,y:.265625},{x:.984375,y:.265625},{x:.015625,y:.296875},{x:.015625,y:.296875},{x:.046875,y:.296875},{x:.046875,y:.296875},{x:.078125,y:.296875},{x:.078125,y:.296875},{x:.109375,y:.296875},{x:.109375,y:.296875},{x:.140625,y:.296875},{x:.140625,y:.296875},{x:.171875,y:.296875},{x:.171875,y:.296875},{x:.203125,y:.296875},{x:.203125,y:.296875},{x:.234375,y:.296875},{x:.234375,y:.296875},{x:.265625,y:.296875},{x:.265625,y:.296875},{x:.296875,y:.296875},{x:.296875,y:.296875},{x:.328125,y:.296875},{x:.328125,y:.296875},{x:.359375,y:.296875},{x:.359375,y:.296875},{x:.390625,y:.296875},{x:.390625,y:.296875},{x:.421875,y:.296875},{x:.421875,y:.296875},{x:.453125,y:.296875},{x:.453125,y:.296875},{x:.484375,y:.296875},{x:.484375,y:.296875},{x:.515625,y:.296875},{x:.515625,y:.296875},{x:.546875,y:.296875},{x:.546875,y:.296875},{x:.578125,y:.296875},{x:.578125,y:.296875},{x:.609375,y:.296875},{x:.609375,y:.296875},{x:.640625,y:.296875},{x:.640625,y:.296875},{x:.671875,y:.296875},{x:.671875,y:.296875},{x:.703125,y:.296875},{x:.703125,y:.296875},{x:.734375,y:.296875},{x:.734375,y:.296875},{x:.765625,y:.296875},{x:.765625,y:.296875},{x:.796875,y:.296875},{x:.796875,y:.296875},{x:.828125,y:.296875},{x:.828125,y:.296875},{x:.859375,y:.296875},{x:.859375,y:.296875},{x:.890625,y:.296875},{x:.890625,y:.296875},{x:.921875,y:.296875},{x:.921875,y:.296875},{x:.953125,y:.296875},{x:.953125,y:.296875},{x:.984375,y:.296875},{x:.984375,y:.296875},{x:.015625,y:.328125},{x:.015625,y:.328125},{x:.046875,y:.328125},{x:.046875,y:.328125},{x:.078125,y:.328125},{x:.078125,y:.328125},{x:.109375,y:.328125},{x:.109375,y:.328125},{x:.140625,y:.328125},{x:.140625,y:.328125},{x:.171875,y:.328125},{x:.171875,y:.328125},{x:.203125,y:.328125},{x:.203125,y:.328125},{x:.234375,y:.328125},{x:.234375,y:.328125},{x:.265625,y:.328125},{x:.265625,y:.328125},{x:.296875,y:.328125},{x:.296875,y:.328125},{x:.328125,y:.328125},{x:.328125,y:.328125},{x:.359375,y:.328125},{x:.359375,y:.328125},{x:.390625,y:.328125},{x:.390625,y:.328125},{x:.421875,y:.328125},{x:.421875,y:.328125},{x:.453125,y:.328125},{x:.453125,y:.328125},{x:.484375,y:.328125},{x:.484375,y:.328125},{x:.515625,y:.328125},{x:.515625,y:.328125},{x:.546875,y:.328125},{x:.546875,y:.328125},{x:.578125,y:.328125},{x:.578125,y:.328125},{x:.609375,y:.328125},{x:.609375,y:.328125},{x:.640625,y:.328125},{x:.640625,y:.328125},{x:.671875,y:.328125},{x:.671875,y:.328125},{x:.703125,y:.328125},{x:.703125,y:.328125},{x:.734375,y:.328125},{x:.734375,y:.328125},{x:.765625,y:.328125},{x:.765625,y:.328125},{x:.796875,y:.328125},{x:.796875,y:.328125},{x:.828125,y:.328125},{x:.828125,y:.328125},{x:.859375,y:.328125},{x:.859375,y:.328125},{x:.890625,y:.328125},{x:.890625,y:.328125},{x:.921875,y:.328125},{x:.921875,y:.328125},{x:.953125,y:.328125},{x:.953125,y:.328125},{x:.984375,y:.328125},{x:.984375,y:.328125},{x:.015625,y:.359375},{x:.015625,y:.359375},{x:.046875,y:.359375},{x:.046875,y:.359375},{x:.078125,y:.359375},{x:.078125,y:.359375},{x:.109375,y:.359375},{x:.109375,y:.359375},{x:.140625,y:.359375},{x:.140625,y:.359375},{x:.171875,y:.359375},{x:.171875,y:.359375},{x:.203125,y:.359375},{x:.203125,y:.359375},{x:.234375,y:.359375},{x:.234375,y:.359375},{x:.265625,y:.359375},{x:.265625,y:.359375},{x:.296875,y:.359375},{x:.296875,y:.359375},{x:.328125,y:.359375},{x:.328125,y:.359375},{x:.359375,y:.359375},{x:.359375,y:.359375},{x:.390625,y:.359375},{x:.390625,y:.359375},{x:.421875,y:.359375},{x:.421875,y:.359375},{x:.453125,y:.359375},{x:.453125,y:.359375},{x:.484375,y:.359375},{x:.484375,y:.359375},{x:.515625,y:.359375},{x:.515625,y:.359375},{x:.546875,y:.359375},{x:.546875,y:.359375},{x:.578125,y:.359375},{x:.578125,y:.359375},{x:.609375,y:.359375},{x:.609375,y:.359375},{x:.640625,y:.359375},{x:.640625,y:.359375},{x:.671875,y:.359375},{x:.671875,y:.359375},{x:.703125,y:.359375},{x:.703125,y:.359375},{x:.734375,y:.359375},{x:.734375,y:.359375},{x:.765625,y:.359375},{x:.765625,y:.359375},{x:.796875,y:.359375},{x:.796875,y:.359375},{x:.828125,y:.359375},{x:.828125,y:.359375},{x:.859375,y:.359375},{x:.859375,y:.359375},{x:.890625,y:.359375},{x:.890625,y:.359375},{x:.921875,y:.359375},{x:.921875,y:.359375},{x:.953125,y:.359375},{x:.953125,y:.359375},{x:.984375,y:.359375},{x:.984375,y:.359375},{x:.015625,y:.390625},{x:.015625,y:.390625},{x:.046875,y:.390625},{x:.046875,y:.390625},{x:.078125,y:.390625},{x:.078125,y:.390625},{x:.109375,y:.390625},{x:.109375,y:.390625},{x:.140625,y:.390625},{x:.140625,y:.390625},{x:.171875,y:.390625},{x:.171875,y:.390625},{x:.203125,y:.390625},{x:.203125,y:.390625},{x:.234375,y:.390625},{x:.234375,y:.390625},{x:.265625,y:.390625},{x:.265625,y:.390625},{x:.296875,y:.390625},{x:.296875,y:.390625},{x:.328125,y:.390625},{x:.328125,y:.390625},{x:.359375,y:.390625},{x:.359375,y:.390625},{x:.390625,y:.390625},{x:.390625,y:.390625},{x:.421875,y:.390625},{x:.421875,y:.390625},{x:.453125,y:.390625},{x:.453125,y:.390625},{x:.484375,y:.390625},{x:.484375,y:.390625},{x:.515625,y:.390625},{x:.515625,y:.390625},{x:.546875,y:.390625},{x:.546875,y:.390625},{x:.578125,y:.390625},{x:.578125,y:.390625},{x:.609375,y:.390625},{x:.609375,y:.390625},{x:.640625,y:.390625},{x:.640625,y:.390625},{x:.671875,y:.390625},{x:.671875,y:.390625},{x:.703125,y:.390625},{x:.703125,y:.390625},{x:.734375,y:.390625},{x:.734375,y:.390625},{x:.765625,y:.390625},{x:.765625,y:.390625},{x:.796875,y:.390625},{x:.796875,y:.390625},{x:.828125,y:.390625},{x:.828125,y:.390625},{x:.859375,y:.390625},{x:.859375,y:.390625},{x:.890625,y:.390625},{x:.890625,y:.390625},{x:.921875,y:.390625},{x:.921875,y:.390625},{x:.953125,y:.390625},{x:.953125,y:.390625},{x:.984375,y:.390625},{x:.984375,y:.390625},{x:.015625,y:.421875},{x:.015625,y:.421875},{x:.046875,y:.421875},{x:.046875,y:.421875},{x:.078125,y:.421875},{x:.078125,y:.421875},{x:.109375,y:.421875},{x:.109375,y:.421875},{x:.140625,y:.421875},{x:.140625,y:.421875},{x:.171875,y:.421875},{x:.171875,y:.421875},{x:.203125,y:.421875},{x:.203125,y:.421875},{x:.234375,y:.421875},{x:.234375,y:.421875},{x:.265625,y:.421875},{x:.265625,y:.421875},{x:.296875,y:.421875},{x:.296875,y:.421875},{x:.328125,y:.421875},{x:.328125,y:.421875},{x:.359375,y:.421875},{x:.359375,y:.421875},{x:.390625,y:.421875},{x:.390625,y:.421875},{x:.421875,y:.421875},{x:.421875,y:.421875},{x:.453125,y:.421875},{x:.453125,y:.421875},{x:.484375,y:.421875},{x:.484375,y:.421875},{x:.515625,y:.421875},{x:.515625,y:.421875},{x:.546875,y:.421875},{x:.546875,y:.421875},{x:.578125,y:.421875},{x:.578125,y:.421875},{x:.609375,y:.421875},{x:.609375,y:.421875},{x:.640625,y:.421875},{x:.640625,y:.421875},{x:.671875,y:.421875},{x:.671875,y:.421875},{x:.703125,y:.421875},{x:.703125,y:.421875},{x:.734375,y:.421875},{x:.734375,y:.421875},{x:.765625,y:.421875},{x:.765625,y:.421875},{x:.796875,y:.421875},{x:.796875,y:.421875},{x:.828125,y:.421875},{x:.828125,y:.421875},{x:.859375,y:.421875},{x:.859375,y:.421875},{x:.890625,y:.421875},{x:.890625,y:.421875},{x:.921875,y:.421875},{x:.921875,y:.421875},{x:.953125,y:.421875},{x:.953125,y:.421875},{x:.984375,y:.421875},{x:.984375,y:.421875},{x:.015625,y:.453125},{x:.015625,y:.453125},{x:.046875,y:.453125},{x:.046875,y:.453125},{x:.078125,y:.453125},{x:.078125,y:.453125},{x:.109375,y:.453125},{x:.109375,y:.453125},{x:.140625,y:.453125},{x:.140625,y:.453125},{x:.171875,y:.453125},{x:.171875,y:.453125},{x:.203125,y:.453125},{x:.203125,y:.453125},{x:.234375,y:.453125},{x:.234375,y:.453125},{x:.265625,y:.453125},{x:.265625,y:.453125},{x:.296875,y:.453125},{x:.296875,y:.453125},{x:.328125,y:.453125},{x:.328125,y:.453125},{x:.359375,y:.453125},{x:.359375,y:.453125},{x:.390625,y:.453125},{x:.390625,y:.453125},{x:.421875,y:.453125},{x:.421875,y:.453125},{x:.453125,y:.453125},{x:.453125,y:.453125},{x:.484375,y:.453125},{x:.484375,y:.453125},{x:.515625,y:.453125},{x:.515625,y:.453125},{x:.546875,y:.453125},{x:.546875,y:.453125},{x:.578125,y:.453125},{x:.578125,y:.453125},{x:.609375,y:.453125},{x:.609375,y:.453125},{x:.640625,y:.453125},{x:.640625,y:.453125},{x:.671875,y:.453125},{x:.671875,y:.453125},{x:.703125,y:.453125},{x:.703125,y:.453125},{x:.734375,y:.453125},{x:.734375,y:.453125},{x:.765625,y:.453125},{x:.765625,y:.453125},{x:.796875,y:.453125},{x:.796875,y:.453125},{x:.828125,y:.453125},{x:.828125,y:.453125},{x:.859375,y:.453125},{x:.859375,y:.453125},{x:.890625,y:.453125},{x:.890625,y:.453125},{x:.921875,y:.453125},{x:.921875,y:.453125},{x:.953125,y:.453125},{x:.953125,y:.453125},{x:.984375,y:.453125},{x:.984375,y:.453125},{x:.015625,y:.484375},{x:.015625,y:.484375},{x:.046875,y:.484375},{x:.046875,y:.484375},{x:.078125,y:.484375},{x:.078125,y:.484375},{x:.109375,y:.484375},{x:.109375,y:.484375},{x:.140625,y:.484375},{x:.140625,y:.484375},{x:.171875,y:.484375},{x:.171875,y:.484375},{x:.203125,y:.484375},{x:.203125,y:.484375},{x:.234375,y:.484375},{x:.234375,y:.484375},{x:.265625,y:.484375},{x:.265625,y:.484375},{x:.296875,y:.484375},{x:.296875,y:.484375},{x:.328125,y:.484375},{x:.328125,y:.484375},{x:.359375,y:.484375},{x:.359375,y:.484375},{x:.390625,y:.484375},{x:.390625,y:.484375},{x:.421875,y:.484375},{x:.421875,y:.484375},{x:.453125,y:.484375},{x:.453125,y:.484375},{x:.484375,y:.484375},{x:.484375,y:.484375},{x:.515625,y:.484375},{x:.515625,y:.484375},{x:.546875,y:.484375},{x:.546875,y:.484375},{x:.578125,y:.484375},{x:.578125,y:.484375},{x:.609375,y:.484375},{x:.609375,y:.484375},{x:.640625,y:.484375},{x:.640625,y:.484375},{x:.671875,y:.484375},{x:.671875,y:.484375},{x:.703125,y:.484375},{x:.703125,y:.484375},{x:.734375,y:.484375},{x:.734375,y:.484375},{x:.765625,y:.484375},{x:.765625,y:.484375},{x:.796875,y:.484375},{x:.796875,y:.484375},{x:.828125,y:.484375},{x:.828125,y:.484375},{x:.859375,y:.484375},{x:.859375,y:.484375},{x:.890625,y:.484375},{x:.890625,y:.484375},{x:.921875,y:.484375},{x:.921875,y:.484375},{x:.953125,y:.484375},{x:.953125,y:.484375},{x:.984375,y:.484375},{x:.984375,y:.484375},{x:.015625,y:.515625},{x:.015625,y:.515625},{x:.046875,y:.515625},{x:.046875,y:.515625},{x:.078125,y:.515625},{x:.078125,y:.515625},{x:.109375,y:.515625},{x:.109375,y:.515625},{x:.140625,y:.515625},{x:.140625,y:.515625},{x:.171875,y:.515625},{x:.171875,y:.515625},{x:.203125,y:.515625},{x:.203125,y:.515625},{x:.234375,y:.515625},{x:.234375,y:.515625},{x:.265625,y:.515625},{x:.265625,y:.515625},{x:.296875,y:.515625},{x:.296875,y:.515625},{x:.328125,y:.515625},{x:.328125,y:.515625},{x:.359375,y:.515625},{x:.359375,y:.515625},{x:.390625,y:.515625},{x:.390625,y:.515625},{x:.421875,y:.515625},{x:.421875,y:.515625},{x:.453125,y:.515625},{x:.453125,y:.515625},{x:.484375,y:.515625},{x:.484375,y:.515625},{x:.515625,y:.515625},{x:.515625,y:.515625},{x:.546875,y:.515625},{x:.546875,y:.515625},{x:.578125,y:.515625},{x:.578125,y:.515625},{x:.609375,y:.515625},{x:.609375,y:.515625},{x:.640625,y:.515625},{x:.640625,y:.515625},{x:.671875,y:.515625},{x:.671875,y:.515625},{x:.703125,y:.515625},{x:.703125,y:.515625},{x:.734375,y:.515625},{x:.734375,y:.515625},{x:.765625,y:.515625},{x:.765625,y:.515625},{x:.796875,y:.515625},{x:.796875,y:.515625},{x:.828125,y:.515625},{x:.828125,y:.515625},{x:.859375,y:.515625},{x:.859375,y:.515625},{x:.890625,y:.515625},{x:.890625,y:.515625},{x:.921875,y:.515625},{x:.921875,y:.515625},{x:.953125,y:.515625},{x:.953125,y:.515625},{x:.984375,y:.515625},{x:.984375,y:.515625},{x:.015625,y:.546875},{x:.015625,y:.546875},{x:.046875,y:.546875},{x:.046875,y:.546875},{x:.078125,y:.546875},{x:.078125,y:.546875},{x:.109375,y:.546875},{x:.109375,y:.546875},{x:.140625,y:.546875},{x:.140625,y:.546875},{x:.171875,y:.546875},{x:.171875,y:.546875},{x:.203125,y:.546875},{x:.203125,y:.546875},{x:.234375,y:.546875},{x:.234375,y:.546875},{x:.265625,y:.546875},{x:.265625,y:.546875},{x:.296875,y:.546875},{x:.296875,y:.546875},{x:.328125,y:.546875},{x:.328125,y:.546875},{x:.359375,y:.546875},{x:.359375,y:.546875},{x:.390625,y:.546875},{x:.390625,y:.546875},{x:.421875,y:.546875},{x:.421875,y:.546875},{x:.453125,y:.546875},{x:.453125,y:.546875},{x:.484375,y:.546875},{x:.484375,y:.546875},{x:.515625,y:.546875},{x:.515625,y:.546875},{x:.546875,y:.546875},{x:.546875,y:.546875},{x:.578125,y:.546875},{x:.578125,y:.546875},{x:.609375,y:.546875},{x:.609375,y:.546875},{x:.640625,y:.546875},{x:.640625,y:.546875},{x:.671875,y:.546875},{x:.671875,y:.546875},{x:.703125,y:.546875},{x:.703125,y:.546875},{x:.734375,y:.546875},{x:.734375,y:.546875},{x:.765625,y:.546875},{x:.765625,y:.546875},{x:.796875,y:.546875},{x:.796875,y:.546875},{x:.828125,y:.546875},{x:.828125,y:.546875},{x:.859375,y:.546875},{x:.859375,y:.546875},{x:.890625,y:.546875},{x:.890625,y:.546875},{x:.921875,y:.546875},{x:.921875,y:.546875},{x:.953125,y:.546875},{x:.953125,y:.546875},{x:.984375,y:.546875},{x:.984375,y:.546875},{x:.015625,y:.578125},{x:.015625,y:.578125},{x:.046875,y:.578125},{x:.046875,y:.578125},{x:.078125,y:.578125},{x:.078125,y:.578125},{x:.109375,y:.578125},{x:.109375,y:.578125},{x:.140625,y:.578125},{x:.140625,y:.578125},{x:.171875,y:.578125},{x:.171875,y:.578125},{x:.203125,y:.578125},{x:.203125,y:.578125},{x:.234375,y:.578125},{x:.234375,y:.578125},{x:.265625,y:.578125},{x:.265625,y:.578125},{x:.296875,y:.578125},{x:.296875,y:.578125},{x:.328125,y:.578125},{x:.328125,y:.578125},{x:.359375,y:.578125},{x:.359375,y:.578125},{x:.390625,y:.578125},{x:.390625,y:.578125},{x:.421875,y:.578125},{x:.421875,y:.578125},{x:.453125,y:.578125},{x:.453125,y:.578125},{x:.484375,y:.578125},{x:.484375,y:.578125},{x:.515625,y:.578125},{x:.515625,y:.578125},{x:.546875,y:.578125},{x:.546875,y:.578125},{x:.578125,y:.578125},{x:.578125,y:.578125},{x:.609375,y:.578125},{x:.609375,y:.578125},{x:.640625,y:.578125},{x:.640625,y:.578125},{x:.671875,y:.578125},{x:.671875,y:.578125},{x:.703125,y:.578125},{x:.703125,y:.578125},{x:.734375,y:.578125},{x:.734375,y:.578125},{x:.765625,y:.578125},{x:.765625,y:.578125},{x:.796875,y:.578125},{x:.796875,y:.578125},{x:.828125,y:.578125},{x:.828125,y:.578125},{x:.859375,y:.578125},{x:.859375,y:.578125},{x:.890625,y:.578125},{x:.890625,y:.578125},{x:.921875,y:.578125},{x:.921875,y:.578125},{x:.953125,y:.578125},{x:.953125,y:.578125},{x:.984375,y:.578125},{x:.984375,y:.578125},{x:.015625,y:.609375},{x:.015625,y:.609375},{x:.046875,y:.609375},{x:.046875,y:.609375},{x:.078125,y:.609375},{x:.078125,y:.609375},{x:.109375,y:.609375},{x:.109375,y:.609375},{x:.140625,y:.609375},{x:.140625,y:.609375},{x:.171875,y:.609375},{x:.171875,y:.609375},{x:.203125,y:.609375},{x:.203125,y:.609375},{x:.234375,y:.609375},{x:.234375,y:.609375},{x:.265625,y:.609375},{x:.265625,y:.609375},{x:.296875,y:.609375},{x:.296875,y:.609375},{x:.328125,y:.609375},{x:.328125,y:.609375},{x:.359375,y:.609375},{x:.359375,y:.609375},{x:.390625,y:.609375},{x:.390625,y:.609375},{x:.421875,y:.609375},{x:.421875,y:.609375},{x:.453125,y:.609375},{x:.453125,y:.609375},{x:.484375,y:.609375},{x:.484375,y:.609375},{x:.515625,y:.609375},{x:.515625,y:.609375},{x:.546875,y:.609375},{x:.546875,y:.609375},{x:.578125,y:.609375},{x:.578125,y:.609375},{x:.609375,y:.609375},{x:.609375,y:.609375},{x:.640625,y:.609375},{x:.640625,y:.609375},{x:.671875,y:.609375},{x:.671875,y:.609375},{x:.703125,y:.609375},{x:.703125,y:.609375},{x:.734375,y:.609375},{x:.734375,y:.609375},{x:.765625,y:.609375},{x:.765625,y:.609375},{x:.796875,y:.609375},{x:.796875,y:.609375},{x:.828125,y:.609375},{x:.828125,y:.609375},{x:.859375,y:.609375},{x:.859375,y:.609375},{x:.890625,y:.609375},{x:.890625,y:.609375},{x:.921875,y:.609375},{x:.921875,y:.609375},{x:.953125,y:.609375},{x:.953125,y:.609375},{x:.984375,y:.609375},{x:.984375,y:.609375},{x:.015625,y:.640625},{x:.015625,y:.640625},{x:.046875,y:.640625},{x:.046875,y:.640625},{x:.078125,y:.640625},{x:.078125,y:.640625},{x:.109375,y:.640625},{x:.109375,y:.640625},{x:.140625,y:.640625},{x:.140625,y:.640625},{x:.171875,y:.640625},{x:.171875,y:.640625},{x:.203125,y:.640625},{x:.203125,y:.640625},{x:.234375,y:.640625},{x:.234375,y:.640625},{x:.265625,y:.640625},{x:.265625,y:.640625},{x:.296875,y:.640625},{x:.296875,y:.640625},{x:.328125,y:.640625},{x:.328125,y:.640625},{x:.359375,y:.640625},{x:.359375,y:.640625},{x:.390625,y:.640625},{x:.390625,y:.640625},{x:.421875,y:.640625},{x:.421875,y:.640625},{x:.453125,y:.640625},{x:.453125,y:.640625},{x:.484375,y:.640625},{x:.484375,y:.640625},{x:.515625,y:.640625},{x:.515625,y:.640625},{x:.546875,y:.640625},{x:.546875,y:.640625},{x:.578125,y:.640625},{x:.578125,y:.640625},{x:.609375,y:.640625},{x:.609375,y:.640625},{x:.640625,y:.640625},{x:.640625,y:.640625},{x:.671875,y:.640625},{x:.671875,y:.640625},{x:.703125,y:.640625},{x:.703125,y:.640625},{x:.734375,y:.640625},{x:.734375,y:.640625},{x:.765625,y:.640625},{x:.765625,y:.640625},{x:.796875,y:.640625},{x:.796875,y:.640625},{x:.828125,y:.640625},{x:.828125,y:.640625},{x:.859375,y:.640625},{x:.859375,y:.640625},{x:.890625,y:.640625},{x:.890625,y:.640625},{x:.921875,y:.640625},{x:.921875,y:.640625},{x:.953125,y:.640625},{x:.953125,y:.640625},{x:.984375,y:.640625},{x:.984375,y:.640625},{x:.015625,y:.671875},{x:.015625,y:.671875},{x:.046875,y:.671875},{x:.046875,y:.671875},{x:.078125,y:.671875},{x:.078125,y:.671875},{x:.109375,y:.671875},{x:.109375,y:.671875},{x:.140625,y:.671875},{x:.140625,y:.671875},{x:.171875,y:.671875},{x:.171875,y:.671875},{x:.203125,y:.671875},{x:.203125,y:.671875},{x:.234375,y:.671875},{x:.234375,y:.671875},{x:.265625,y:.671875},{x:.265625,y:.671875},{x:.296875,y:.671875},{x:.296875,y:.671875},{x:.328125,y:.671875},{x:.328125,y:.671875},{x:.359375,y:.671875},{x:.359375,y:.671875},{x:.390625,y:.671875},{x:.390625,y:.671875},{x:.421875,y:.671875},{x:.421875,y:.671875},{x:.453125,y:.671875},{x:.453125,y:.671875},{x:.484375,y:.671875},{x:.484375,y:.671875},{x:.515625,y:.671875},{x:.515625,y:.671875},{x:.546875,y:.671875},{x:.546875,y:.671875},{x:.578125,y:.671875},{x:.578125,y:.671875},{x:.609375,y:.671875},{x:.609375,y:.671875},{x:.640625,y:.671875},{x:.640625,y:.671875},{x:.671875,y:.671875},{x:.671875,y:.671875},{x:.703125,y:.671875},{x:.703125,y:.671875},{x:.734375,y:.671875},{x:.734375,y:.671875},{x:.765625,y:.671875},{x:.765625,y:.671875},{x:.796875,y:.671875},{x:.796875,y:.671875},{x:.828125,y:.671875},{x:.828125,y:.671875},{x:.859375,y:.671875},{x:.859375,y:.671875},{x:.890625,y:.671875},{x:.890625,y:.671875},{x:.921875,y:.671875},{x:.921875,y:.671875},{x:.953125,y:.671875},{x:.953125,y:.671875},{x:.984375,y:.671875},{x:.984375,y:.671875},{x:.015625,y:.703125},{x:.015625,y:.703125},{x:.046875,y:.703125},{x:.046875,y:.703125},{x:.078125,y:.703125},{x:.078125,y:.703125},{x:.109375,y:.703125},{x:.109375,y:.703125},{x:.140625,y:.703125},{x:.140625,y:.703125},{x:.171875,y:.703125},{x:.171875,y:.703125},{x:.203125,y:.703125},{x:.203125,y:.703125},{x:.234375,y:.703125},{x:.234375,y:.703125},{x:.265625,y:.703125},{x:.265625,y:.703125},{x:.296875,y:.703125},{x:.296875,y:.703125},{x:.328125,y:.703125},{x:.328125,y:.703125},{x:.359375,y:.703125},{x:.359375,y:.703125},{x:.390625,y:.703125},{x:.390625,y:.703125},{x:.421875,y:.703125},{x:.421875,y:.703125},{x:.453125,y:.703125},{x:.453125,y:.703125},{x:.484375,y:.703125},{x:.484375,y:.703125},{x:.515625,y:.703125},{x:.515625,y:.703125},{x:.546875,y:.703125},{x:.546875,y:.703125},{x:.578125,y:.703125},{x:.578125,y:.703125},{x:.609375,y:.703125},{x:.609375,y:.703125},{x:.640625,y:.703125},{x:.640625,y:.703125},{x:.671875,y:.703125},{x:.671875,y:.703125},{x:.703125,y:.703125},{x:.703125,y:.703125},{x:.734375,y:.703125},{x:.734375,y:.703125},{x:.765625,y:.703125},{x:.765625,y:.703125},{x:.796875,y:.703125},{x:.796875,y:.703125},{x:.828125,y:.703125},{x:.828125,y:.703125},{x:.859375,y:.703125},{x:.859375,y:.703125},{x:.890625,y:.703125},{x:.890625,y:.703125},{x:.921875,y:.703125},{x:.921875,y:.703125},{x:.953125,y:.703125},{x:.953125,y:.703125},{x:.984375,y:.703125},{x:.984375,y:.703125},{x:.015625,y:.734375},{x:.015625,y:.734375},{x:.046875,y:.734375},{x:.046875,y:.734375},{x:.078125,y:.734375},{x:.078125,y:.734375},{x:.109375,y:.734375},{x:.109375,y:.734375},{x:.140625,y:.734375},{x:.140625,y:.734375},{x:.171875,y:.734375},{x:.171875,y:.734375},{x:.203125,y:.734375},{x:.203125,y:.734375},{x:.234375,y:.734375},{x:.234375,y:.734375},{x:.265625,y:.734375},{x:.265625,y:.734375},{x:.296875,y:.734375},{x:.296875,y:.734375},{x:.328125,y:.734375},{x:.328125,y:.734375},{x:.359375,y:.734375},{x:.359375,y:.734375},{x:.390625,y:.734375},{x:.390625,y:.734375},{x:.421875,y:.734375},{x:.421875,y:.734375},{x:.453125,y:.734375},{x:.453125,y:.734375},{x:.484375,y:.734375},{x:.484375,y:.734375},{x:.515625,y:.734375},{x:.515625,y:.734375},{x:.546875,y:.734375},{x:.546875,y:.734375},{x:.578125,y:.734375},{x:.578125,y:.734375},{x:.609375,y:.734375},{x:.609375,y:.734375},{x:.640625,y:.734375},{x:.640625,y:.734375},{x:.671875,y:.734375},{x:.671875,y:.734375},{x:.703125,y:.734375},{x:.703125,y:.734375},{x:.734375,y:.734375},{x:.734375,y:.734375},{x:.765625,y:.734375},{x:.765625,y:.734375},{x:.796875,y:.734375},{x:.796875,y:.734375},{x:.828125,y:.734375},{x:.828125,y:.734375},{x:.859375,y:.734375},{x:.859375,y:.734375},{x:.890625,y:.734375},{x:.890625,y:.734375},{x:.921875,y:.734375},{x:.921875,y:.734375},{x:.953125,y:.734375},{x:.953125,y:.734375},{x:.984375,y:.734375},{x:.984375,y:.734375},{x:.015625,y:.765625},{x:.015625,y:.765625},{x:.046875,y:.765625},{x:.046875,y:.765625},{x:.078125,y:.765625},{x:.078125,y:.765625},{x:.109375,y:.765625},{x:.109375,y:.765625},{x:.140625,y:.765625},{x:.140625,y:.765625},{x:.171875,y:.765625},{x:.171875,y:.765625},{x:.203125,y:.765625},{x:.203125,y:.765625},{x:.234375,y:.765625},{x:.234375,y:.765625},{x:.265625,y:.765625},{x:.265625,y:.765625},{x:.296875,y:.765625},{x:.296875,y:.765625},{x:.328125,y:.765625},{x:.328125,y:.765625},{x:.359375,y:.765625},{x:.359375,y:.765625},{x:.390625,y:.765625},{x:.390625,y:.765625},{x:.421875,y:.765625},{x:.421875,y:.765625},{x:.453125,y:.765625},{x:.453125,y:.765625},{x:.484375,y:.765625},{x:.484375,y:.765625},{x:.515625,y:.765625},{x:.515625,y:.765625},{x:.546875,y:.765625},{x:.546875,y:.765625},{x:.578125,y:.765625},{x:.578125,y:.765625},{x:.609375,y:.765625},{x:.609375,y:.765625},{x:.640625,y:.765625},{x:.640625,y:.765625},{x:.671875,y:.765625},{x:.671875,y:.765625},{x:.703125,y:.765625},{x:.703125,y:.765625},{x:.734375,y:.765625},{x:.734375,y:.765625},{x:.765625,y:.765625},{x:.765625,y:.765625},{x:.796875,y:.765625},{x:.796875,y:.765625},{x:.828125,y:.765625},{x:.828125,y:.765625},{x:.859375,y:.765625},{x:.859375,y:.765625},{x:.890625,y:.765625},{x:.890625,y:.765625},{x:.921875,y:.765625},{x:.921875,y:.765625},{x:.953125,y:.765625},{x:.953125,y:.765625},{x:.984375,y:.765625},{x:.984375,y:.765625},{x:.015625,y:.796875},{x:.015625,y:.796875},{x:.046875,y:.796875},{x:.046875,y:.796875},{x:.078125,y:.796875},{x:.078125,y:.796875},{x:.109375,y:.796875},{x:.109375,y:.796875},{x:.140625,y:.796875},{x:.140625,y:.796875},{x:.171875,y:.796875},{x:.171875,y:.796875},{x:.203125,y:.796875},{x:.203125,y:.796875},{x:.234375,y:.796875},{x:.234375,y:.796875},{x:.265625,y:.796875},{x:.265625,y:.796875},{x:.296875,y:.796875},{x:.296875,y:.796875},{x:.328125,y:.796875},{x:.328125,y:.796875},{x:.359375,y:.796875},{x:.359375,y:.796875},{x:.390625,y:.796875},{x:.390625,y:.796875},{x:.421875,y:.796875},{x:.421875,y:.796875},{x:.453125,y:.796875},{x:.453125,y:.796875},{x:.484375,y:.796875},{x:.484375,y:.796875},{x:.515625,y:.796875},{x:.515625,y:.796875},{x:.546875,y:.796875},{x:.546875,y:.796875},{x:.578125,y:.796875},{x:.578125,y:.796875},{x:.609375,y:.796875},{x:.609375,y:.796875},{x:.640625,y:.796875},{x:.640625,y:.796875},{x:.671875,y:.796875},{x:.671875,y:.796875},{x:.703125,y:.796875},{x:.703125,y:.796875},{x:.734375,y:.796875},{x:.734375,y:.796875},{x:.765625,y:.796875},{x:.765625,y:.796875},{x:.796875,y:.796875},{x:.796875,y:.796875},{x:.828125,y:.796875},{x:.828125,y:.796875},{x:.859375,y:.796875},{x:.859375,y:.796875},{x:.890625,y:.796875},{x:.890625,y:.796875},{x:.921875,y:.796875},{x:.921875,y:.796875},{x:.953125,y:.796875},{x:.953125,y:.796875},{x:.984375,y:.796875},{x:.984375,y:.796875},{x:.015625,y:.828125},{x:.015625,y:.828125},{x:.046875,y:.828125},{x:.046875,y:.828125},{x:.078125,y:.828125},{x:.078125,y:.828125},{x:.109375,y:.828125},{x:.109375,y:.828125},{x:.140625,y:.828125},{x:.140625,y:.828125},{x:.171875,y:.828125},{x:.171875,y:.828125},{x:.203125,y:.828125},{x:.203125,y:.828125},{x:.234375,y:.828125},{x:.234375,y:.828125},{x:.265625,y:.828125},{x:.265625,y:.828125},{x:.296875,y:.828125},{x:.296875,y:.828125},{x:.328125,y:.828125},{x:.328125,y:.828125},{x:.359375,y:.828125},{x:.359375,y:.828125},{x:.390625,y:.828125},{x:.390625,y:.828125},{x:.421875,y:.828125},{x:.421875,y:.828125},{x:.453125,y:.828125},{x:.453125,y:.828125},{x:.484375,y:.828125},{x:.484375,y:.828125},{x:.515625,y:.828125},{x:.515625,y:.828125},{x:.546875,y:.828125},{x:.546875,y:.828125},{x:.578125,y:.828125},{x:.578125,y:.828125},{x:.609375,y:.828125},{x:.609375,y:.828125},{x:.640625,y:.828125},{x:.640625,y:.828125},{x:.671875,y:.828125},{x:.671875,y:.828125},{x:.703125,y:.828125},{x:.703125,y:.828125},{x:.734375,y:.828125},{x:.734375,y:.828125},{x:.765625,y:.828125},{x:.765625,y:.828125},{x:.796875,y:.828125},{x:.796875,y:.828125},{x:.828125,y:.828125},{x:.828125,y:.828125},{x:.859375,y:.828125},{x:.859375,y:.828125},{x:.890625,y:.828125},{x:.890625,y:.828125},{x:.921875,y:.828125},{x:.921875,y:.828125},{x:.953125,y:.828125},{x:.953125,y:.828125},{x:.984375,y:.828125},{x:.984375,y:.828125},{x:.015625,y:.859375},{x:.015625,y:.859375},{x:.046875,y:.859375},{x:.046875,y:.859375},{x:.078125,y:.859375},{x:.078125,y:.859375},{x:.109375,y:.859375},{x:.109375,y:.859375},{x:.140625,y:.859375},{x:.140625,y:.859375},{x:.171875,y:.859375},{x:.171875,y:.859375},{x:.203125,y:.859375},{x:.203125,y:.859375},{x:.234375,y:.859375},{x:.234375,y:.859375},{x:.265625,y:.859375},{x:.265625,y:.859375},{x:.296875,y:.859375},{x:.296875,y:.859375},{x:.328125,y:.859375},{x:.328125,y:.859375},{x:.359375,y:.859375},{x:.359375,y:.859375},{x:.390625,y:.859375},{x:.390625,y:.859375},{x:.421875,y:.859375},{x:.421875,y:.859375},{x:.453125,y:.859375},{x:.453125,y:.859375},{x:.484375,y:.859375},{x:.484375,y:.859375},{x:.515625,y:.859375},{x:.515625,y:.859375},{x:.546875,y:.859375},{x:.546875,y:.859375},{x:.578125,y:.859375},{x:.578125,y:.859375},{x:.609375,y:.859375},{x:.609375,y:.859375},{x:.640625,y:.859375},{x:.640625,y:.859375},{x:.671875,y:.859375},{x:.671875,y:.859375},{x:.703125,y:.859375},{x:.703125,y:.859375},{x:.734375,y:.859375},{x:.734375,y:.859375},{x:.765625,y:.859375},{x:.765625,y:.859375},{x:.796875,y:.859375},{x:.796875,y:.859375},{x:.828125,y:.859375},{x:.828125,y:.859375},{x:.859375,y:.859375},{x:.859375,y:.859375},{x:.890625,y:.859375},{x:.890625,y:.859375},{x:.921875,y:.859375},{x:.921875,y:.859375},{x:.953125,y:.859375},{x:.953125,y:.859375},{x:.984375,y:.859375},{x:.984375,y:.859375},{x:.015625,y:.890625},{x:.015625,y:.890625},{x:.046875,y:.890625},{x:.046875,y:.890625},{x:.078125,y:.890625},{x:.078125,y:.890625},{x:.109375,y:.890625},{x:.109375,y:.890625},{x:.140625,y:.890625},{x:.140625,y:.890625},{x:.171875,y:.890625},{x:.171875,y:.890625},{x:.203125,y:.890625},{x:.203125,y:.890625},{x:.234375,y:.890625},{x:.234375,y:.890625},{x:.265625,y:.890625},{x:.265625,y:.890625},{x:.296875,y:.890625},{x:.296875,y:.890625},{x:.328125,y:.890625},{x:.328125,y:.890625},{x:.359375,y:.890625},{x:.359375,y:.890625},{x:.390625,y:.890625},{x:.390625,y:.890625},{x:.421875,y:.890625},{x:.421875,y:.890625},{x:.453125,y:.890625},{x:.453125,y:.890625},{x:.484375,y:.890625},{x:.484375,y:.890625},{x:.515625,y:.890625},{x:.515625,y:.890625},{x:.546875,y:.890625},{x:.546875,y:.890625},{x:.578125,y:.890625},{x:.578125,y:.890625},{x:.609375,y:.890625},{x:.609375,y:.890625},{x:.640625,y:.890625},{x:.640625,y:.890625},{x:.671875,y:.890625},{x:.671875,y:.890625},{x:.703125,y:.890625},{x:.703125,y:.890625},{x:.734375,y:.890625},{x:.734375,y:.890625},{x:.765625,y:.890625},{x:.765625,y:.890625},{x:.796875,y:.890625},{x:.796875,y:.890625},{x:.828125,y:.890625},{x:.828125,y:.890625},{x:.859375,y:.890625},{x:.859375,y:.890625},{x:.890625,y:.890625},{x:.890625,y:.890625},{x:.921875,y:.890625},{x:.921875,y:.890625},{x:.953125,y:.890625},{x:.953125,y:.890625},{x:.984375,y:.890625},{x:.984375,y:.890625},{x:.015625,y:.921875},{x:.015625,y:.921875},{x:.046875,y:.921875},{x:.046875,y:.921875},{x:.078125,y:.921875},{x:.078125,y:.921875},{x:.109375,y:.921875},{x:.109375,y:.921875},{x:.140625,y:.921875},{x:.140625,y:.921875},{x:.171875,y:.921875},{x:.171875,y:.921875},{x:.203125,y:.921875},{x:.203125,y:.921875},{x:.234375,y:.921875},{x:.234375,y:.921875},{x:.265625,y:.921875},{x:.265625,y:.921875},{x:.296875,y:.921875},{x:.296875,y:.921875},{x:.328125,y:.921875},{x:.328125,y:.921875},{x:.359375,y:.921875},{x:.359375,y:.921875},{x:.390625,y:.921875},{x:.390625,y:.921875},{x:.421875,y:.921875},{x:.421875,y:.921875},{x:.453125,y:.921875},{x:.453125,y:.921875},{x:.484375,y:.921875},{x:.484375,y:.921875},{x:.515625,y:.921875},{x:.515625,y:.921875},{x:.546875,y:.921875},{x:.546875,y:.921875},{x:.578125,y:.921875},{x:.578125,y:.921875},{x:.609375,y:.921875},{x:.609375,y:.921875},{x:.640625,y:.921875},{x:.640625,y:.921875},{x:.671875,y:.921875},{x:.671875,y:.921875},{x:.703125,y:.921875},{x:.703125,y:.921875},{x:.734375,y:.921875},{x:.734375,y:.921875},{x:.765625,y:.921875},{x:.765625,y:.921875},{x:.796875,y:.921875},{x:.796875,y:.921875},{x:.828125,y:.921875},{x:.828125,y:.921875},{x:.859375,y:.921875},{x:.859375,y:.921875},{x:.890625,y:.921875},{x:.890625,y:.921875},{x:.921875,y:.921875},{x:.921875,y:.921875},{x:.953125,y:.921875},{x:.953125,y:.921875},{x:.984375,y:.921875},{x:.984375,y:.921875},{x:.015625,y:.953125},{x:.015625,y:.953125},{x:.046875,y:.953125},{x:.046875,y:.953125},{x:.078125,y:.953125},{x:.078125,y:.953125},{x:.109375,y:.953125},{x:.109375,y:.953125},{x:.140625,y:.953125},{x:.140625,y:.953125},{x:.171875,y:.953125},{x:.171875,y:.953125},{x:.203125,y:.953125},{x:.203125,y:.953125},{x:.234375,y:.953125},{x:.234375,y:.953125},{x:.265625,y:.953125},{x:.265625,y:.953125},{x:.296875,y:.953125},{x:.296875,y:.953125},{x:.328125,y:.953125},{x:.328125,y:.953125},{x:.359375,y:.953125},{x:.359375,y:.953125},{x:.390625,y:.953125},{x:.390625,y:.953125},{x:.421875,y:.953125},{x:.421875,y:.953125},{x:.453125,y:.953125},{x:.453125,y:.953125},{x:.484375,y:.953125},{x:.484375,y:.953125},{x:.515625,y:.953125},{x:.515625,y:.953125},{x:.546875,y:.953125},{x:.546875,y:.953125},{x:.578125,y:.953125},{x:.578125,y:.953125},{x:.609375,y:.953125},{x:.609375,y:.953125},{x:.640625,y:.953125},{x:.640625,y:.953125},{x:.671875,y:.953125},{x:.671875,y:.953125},{x:.703125,y:.953125},{x:.703125,y:.953125},{x:.734375,y:.953125},{x:.734375,y:.953125},{x:.765625,y:.953125},{x:.765625,y:.953125},{x:.796875,y:.953125},{x:.796875,y:.953125},{x:.828125,y:.953125},{x:.828125,y:.953125},{x:.859375,y:.953125},{x:.859375,y:.953125},{x:.890625,y:.953125},{x:.890625,y:.953125},{x:.921875,y:.953125},{x:.921875,y:.953125},{x:.953125,y:.953125},{x:.953125,y:.953125},{x:.984375,y:.953125},{x:.984375,y:.953125},{x:.015625,y:.984375},{x:.015625,y:.984375},{x:.046875,y:.984375},{x:.046875,y:.984375},{x:.078125,y:.984375},{x:.078125,y:.984375},{x:.109375,y:.984375},{x:.109375,y:.984375},{x:.140625,y:.984375},{x:.140625,y:.984375},{x:.171875,y:.984375},{x:.171875,y:.984375},{x:.203125,y:.984375},{x:.203125,y:.984375},{x:.234375,y:.984375},{x:.234375,y:.984375},{x:.265625,y:.984375},{x:.265625,y:.984375},{x:.296875,y:.984375},{x:.296875,y:.984375},{x:.328125,y:.984375},{x:.328125,y:.984375},{x:.359375,y:.984375},{x:.359375,y:.984375},{x:.390625,y:.984375},{x:.390625,y:.984375},{x:.421875,y:.984375},{x:.421875,y:.984375},{x:.453125,y:.984375},{x:.453125,y:.984375},{x:.484375,y:.984375},{x:.484375,y:.984375},{x:.515625,y:.984375},{x:.515625,y:.984375},{x:.546875,y:.984375},{x:.546875,y:.984375},{x:.578125,y:.984375},{x:.578125,y:.984375},{x:.609375,y:.984375},{x:.609375,y:.984375},{x:.640625,y:.984375},{x:.640625,y:.984375},{x:.671875,y:.984375},{x:.671875,y:.984375},{x:.703125,y:.984375},{x:.703125,y:.984375},{x:.734375,y:.984375},{x:.734375,y:.984375},{x:.765625,y:.984375},{x:.765625,y:.984375},{x:.796875,y:.984375},{x:.796875,y:.984375},{x:.828125,y:.984375},{x:.828125,y:.984375},{x:.859375,y:.984375},{x:.859375,y:.984375},{x:.890625,y:.984375},{x:.890625,y:.984375},{x:.921875,y:.984375},{x:.921875,y:.984375},{x:.953125,y:.984375},{x:.953125,y:.984375},{x:.984375,y:.984375},{x:.984375,y:.984375},{x:.03125,y:.03125},{x:.03125,y:.03125},{x:.09375,y:.03125},{x:.09375,y:.03125},{x:.15625,y:.03125},{x:.15625,y:.03125},{x:.21875,y:.03125},{x:.21875,y:.03125},{x:.28125,y:.03125},{x:.28125,y:.03125},{x:.34375,y:.03125},{x:.34375,y:.03125},{x:.40625,y:.03125},{x:.40625,y:.03125},{x:.46875,y:.03125},{x:.46875,y:.03125},{x:.53125,y:.03125},{x:.53125,y:.03125},{x:.59375,y:.03125},{x:.59375,y:.03125},{x:.65625,y:.03125},{x:.65625,y:.03125},{x:.71875,y:.03125},{x:.71875,y:.03125},{x:.78125,y:.03125},{x:.78125,y:.03125},{x:.84375,y:.03125},{x:.84375,y:.03125},{x:.90625,y:.03125},{x:.90625,y:.03125},{x:.96875,y:.03125},{x:.96875,y:.03125},{x:.03125,y:.09375},{x:.03125,y:.09375},{x:.09375,y:.09375},{x:.09375,y:.09375},{x:.15625,y:.09375},{x:.15625,y:.09375},{x:.21875,y:.09375},{x:.21875,y:.09375},{x:.28125,y:.09375},{x:.28125,y:.09375},{x:.34375,y:.09375},{x:.34375,y:.09375},{x:.40625,y:.09375},{x:.40625,y:.09375},{x:.46875,y:.09375},{x:.46875,y:.09375},{x:.53125,y:.09375},{x:.53125,y:.09375},{x:.59375,y:.09375},{x:.59375,y:.09375},{x:.65625,y:.09375},{x:.65625,y:.09375},{x:.71875,y:.09375},{x:.71875,y:.09375},{x:.78125,y:.09375},{x:.78125,y:.09375},{x:.84375,y:.09375},{x:.84375,y:.09375},{x:.90625,y:.09375},{x:.90625,y:.09375},{x:.96875,y:.09375},{x:.96875,y:.09375},{x:.03125,y:.15625},{x:.03125,y:.15625},{x:.09375,y:.15625},{x:.09375,y:.15625},{x:.15625,y:.15625},{x:.15625,y:.15625},{x:.21875,y:.15625},{x:.21875,y:.15625},{x:.28125,y:.15625},{x:.28125,y:.15625},{x:.34375,y:.15625},{x:.34375,y:.15625},{x:.40625,y:.15625},{x:.40625,y:.15625},{x:.46875,y:.15625},{x:.46875,y:.15625},{x:.53125,y:.15625},{x:.53125,y:.15625},{x:.59375,y:.15625},{x:.59375,y:.15625},{x:.65625,y:.15625},{x:.65625,y:.15625},{x:.71875,y:.15625},{x:.71875,y:.15625},{x:.78125,y:.15625},{x:.78125,y:.15625},{x:.84375,y:.15625},{x:.84375,y:.15625},{x:.90625,y:.15625},{x:.90625,y:.15625},{x:.96875,y:.15625},{x:.96875,y:.15625},{x:.03125,y:.21875},{x:.03125,y:.21875},{x:.09375,y:.21875},{x:.09375,y:.21875},{x:.15625,y:.21875},{x:.15625,y:.21875},{x:.21875,y:.21875},{x:.21875,y:.21875},{x:.28125,y:.21875},{x:.28125,y:.21875},{x:.34375,y:.21875},{x:.34375,y:.21875},{x:.40625,y:.21875},{x:.40625,y:.21875},{x:.46875,y:.21875},{x:.46875,y:.21875},{x:.53125,y:.21875},{x:.53125,y:.21875},{x:.59375,y:.21875},{x:.59375,y:.21875},{x:.65625,y:.21875},{x:.65625,y:.21875},{x:.71875,y:.21875},{x:.71875,y:.21875},{x:.78125,y:.21875},{x:.78125,y:.21875},{x:.84375,y:.21875},{x:.84375,y:.21875},{x:.90625,y:.21875},{x:.90625,y:.21875},{x:.96875,y:.21875},{x:.96875,y:.21875},{x:.03125,y:.28125},{x:.03125,y:.28125},{x:.09375,y:.28125},{x:.09375,y:.28125},{x:.15625,y:.28125},{x:.15625,y:.28125},{x:.21875,y:.28125},{x:.21875,y:.28125},{x:.28125,y:.28125},{x:.28125,y:.28125},{x:.34375,y:.28125},{x:.34375,y:.28125},{x:.40625,y:.28125},{x:.40625,y:.28125},{x:.46875,y:.28125},{x:.46875,y:.28125},{x:.53125,y:.28125},{x:.53125,y:.28125},{x:.59375,y:.28125},{x:.59375,y:.28125},{x:.65625,y:.28125},{x:.65625,y:.28125},{x:.71875,y:.28125},{x:.71875,y:.28125},{x:.78125,y:.28125},{x:.78125,y:.28125},{x:.84375,y:.28125},{x:.84375,y:.28125},{x:.90625,y:.28125},{x:.90625,y:.28125},{x:.96875,y:.28125},{x:.96875,y:.28125},{x:.03125,y:.34375},{x:.03125,y:.34375},{x:.09375,y:.34375},{x:.09375,y:.34375},{x:.15625,y:.34375},{x:.15625,y:.34375},{x:.21875,y:.34375},{x:.21875,y:.34375},{x:.28125,y:.34375},{x:.28125,y:.34375},{x:.34375,y:.34375},{x:.34375,y:.34375},{x:.40625,y:.34375},{x:.40625,y:.34375},{x:.46875,y:.34375},{x:.46875,y:.34375},{x:.53125,y:.34375},{x:.53125,y:.34375},{x:.59375,y:.34375},{x:.59375,y:.34375},{x:.65625,y:.34375},{x:.65625,y:.34375},{x:.71875,y:.34375},{x:.71875,y:.34375},{x:.78125,y:.34375},{x:.78125,y:.34375},{x:.84375,y:.34375},{x:.84375,y:.34375},{x:.90625,y:.34375},{x:.90625,y:.34375},{x:.96875,y:.34375},{x:.96875,y:.34375},{x:.03125,y:.40625},{x:.03125,y:.40625},{x:.09375,y:.40625},{x:.09375,y:.40625},{x:.15625,y:.40625},{x:.15625,y:.40625},{x:.21875,y:.40625},{x:.21875,y:.40625},{x:.28125,y:.40625},{x:.28125,y:.40625},{x:.34375,y:.40625},{x:.34375,y:.40625},{x:.40625,y:.40625},{x:.40625,y:.40625},{x:.46875,y:.40625},{x:.46875,y:.40625},{x:.53125,y:.40625},{x:.53125,y:.40625},{x:.59375,y:.40625},{x:.59375,y:.40625},{x:.65625,y:.40625},{x:.65625,y:.40625},{x:.71875,y:.40625},{x:.71875,y:.40625},{x:.78125,y:.40625},{x:.78125,y:.40625},{x:.84375,y:.40625},{x:.84375,y:.40625},{x:.90625,y:.40625},{x:.90625,y:.40625},{x:.96875,y:.40625},{x:.96875,y:.40625},{x:.03125,y:.46875},{x:.03125,y:.46875},{x:.09375,y:.46875},{x:.09375,y:.46875},{x:.15625,y:.46875},{x:.15625,y:.46875},{x:.21875,y:.46875},{x:.21875,y:.46875},{x:.28125,y:.46875},{x:.28125,y:.46875},{x:.34375,y:.46875},{x:.34375,y:.46875},{x:.40625,y:.46875},{x:.40625,y:.46875},{x:.46875,y:.46875},{x:.46875,y:.46875},{x:.53125,y:.46875},{x:.53125,y:.46875},{x:.59375,y:.46875},{x:.59375,y:.46875},{x:.65625,y:.46875},{x:.65625,y:.46875},{x:.71875,y:.46875},{x:.71875,y:.46875},{x:.78125,y:.46875},{x:.78125,y:.46875},{x:.84375,y:.46875},{x:.84375,y:.46875},{x:.90625,y:.46875},{x:.90625,y:.46875},{x:.96875,y:.46875},{x:.96875,y:.46875},{x:.03125,y:.53125},{x:.03125,y:.53125},{x:.09375,y:.53125},{x:.09375,y:.53125},{x:.15625,y:.53125},{x:.15625,y:.53125},{x:.21875,y:.53125},{x:.21875,y:.53125},{x:.28125,y:.53125},{x:.28125,y:.53125},{x:.34375,y:.53125},{x:.34375,y:.53125},{x:.40625,y:.53125},{x:.40625,y:.53125},{x:.46875,y:.53125},{x:.46875,y:.53125},{x:.53125,y:.53125},{x:.53125,y:.53125},{x:.59375,y:.53125},{x:.59375,y:.53125},{x:.65625,y:.53125},{x:.65625,y:.53125},{x:.71875,y:.53125},{x:.71875,y:.53125},{x:.78125,y:.53125},{x:.78125,y:.53125},{x:.84375,y:.53125},{x:.84375,y:.53125},{x:.90625,y:.53125},{x:.90625,y:.53125},{x:.96875,y:.53125},{x:.96875,y:.53125},{x:.03125,y:.59375},{x:.03125,y:.59375},{x:.09375,y:.59375},{x:.09375,y:.59375},{x:.15625,y:.59375},{x:.15625,y:.59375},{x:.21875,y:.59375},{x:.21875,y:.59375},{x:.28125,y:.59375},{x:.28125,y:.59375},{x:.34375,y:.59375},{x:.34375,y:.59375},{x:.40625,y:.59375},{x:.40625,y:.59375},{x:.46875,y:.59375},{x:.46875,y:.59375},{x:.53125,y:.59375},{x:.53125,y:.59375},{x:.59375,y:.59375},{x:.59375,y:.59375},{x:.65625,y:.59375},{x:.65625,y:.59375},{x:.71875,y:.59375},{x:.71875,y:.59375},{x:.78125,y:.59375},{x:.78125,y:.59375},{x:.84375,y:.59375},{x:.84375,y:.59375},{x:.90625,y:.59375},{x:.90625,y:.59375},{x:.96875,y:.59375},{x:.96875,y:.59375},{x:.03125,y:.65625},{x:.03125,y:.65625},{x:.09375,y:.65625},{x:.09375,y:.65625},{x:.15625,y:.65625},{x:.15625,y:.65625},{x:.21875,y:.65625},{x:.21875,y:.65625},{x:.28125,y:.65625},{x:.28125,y:.65625},{x:.34375,y:.65625},{x:.34375,y:.65625},{x:.40625,y:.65625},{x:.40625,y:.65625},{x:.46875,y:.65625},{x:.46875,y:.65625},{x:.53125,y:.65625},{x:.53125,y:.65625},{x:.59375,y:.65625},{x:.59375,y:.65625},{x:.65625,y:.65625},{x:.65625,y:.65625},{x:.71875,y:.65625},{x:.71875,y:.65625},{x:.78125,y:.65625},{x:.78125,y:.65625},{x:.84375,y:.65625},{x:.84375,y:.65625},{x:.90625,y:.65625},{x:.90625,y:.65625},{x:.96875,y:.65625},{x:.96875,y:.65625},{x:.03125,y:.71875},{x:.03125,y:.71875},{x:.09375,y:.71875},{x:.09375,y:.71875},{x:.15625,y:.71875},{x:.15625,y:.71875},{x:.21875,y:.71875},{x:.21875,y:.71875},{x:.28125,y:.71875},{x:.28125,y:.71875},{x:.34375,y:.71875},{x:.34375,y:.71875},{x:.40625,y:.71875},{x:.40625,y:.71875},{x:.46875,y:.71875},{x:.46875,y:.71875},{x:.53125,y:.71875},{x:.53125,y:.71875},{x:.59375,y:.71875},{x:.59375,y:.71875},{x:.65625,y:.71875},{x:.65625,y:.71875},{x:.71875,y:.71875},{x:.71875,y:.71875},{x:.78125,y:.71875},{x:.78125,y:.71875},{x:.84375,y:.71875},{x:.84375,y:.71875},{x:.90625,y:.71875},{x:.90625,y:.71875},{x:.96875,y:.71875},{x:.96875,y:.71875},{x:.03125,y:.78125},{x:.03125,y:.78125},{x:.09375,y:.78125},{x:.09375,y:.78125},{x:.15625,y:.78125},{x:.15625,y:.78125},{x:.21875,y:.78125},{x:.21875,y:.78125},{x:.28125,y:.78125},{x:.28125,y:.78125},{x:.34375,y:.78125},{x:.34375,y:.78125},{x:.40625,y:.78125},{x:.40625,y:.78125},{x:.46875,y:.78125},{x:.46875,y:.78125},{x:.53125,y:.78125},{x:.53125,y:.78125},{x:.59375,y:.78125},{x:.59375,y:.78125},{x:.65625,y:.78125},{x:.65625,y:.78125},{x:.71875,y:.78125},{x:.71875,y:.78125},{x:.78125,y:.78125},{x:.78125,y:.78125},{x:.84375,y:.78125},{x:.84375,y:.78125},{x:.90625,y:.78125},{x:.90625,y:.78125},{x:.96875,y:.78125},{x:.96875,y:.78125},{x:.03125,y:.84375},{x:.03125,y:.84375},{x:.09375,y:.84375},{x:.09375,y:.84375},{x:.15625,y:.84375},{x:.15625,y:.84375},{x:.21875,y:.84375},{x:.21875,y:.84375},{x:.28125,y:.84375},{x:.28125,y:.84375},{x:.34375,y:.84375},{x:.34375,y:.84375},{x:.40625,y:.84375},{x:.40625,y:.84375},{x:.46875,y:.84375},{x:.46875,y:.84375},{x:.53125,y:.84375},{x:.53125,y:.84375},{x:.59375,y:.84375},{x:.59375,y:.84375},{x:.65625,y:.84375},{x:.65625,y:.84375},{x:.71875,y:.84375},{x:.71875,y:.84375},{x:.78125,y:.84375},{x:.78125,y:.84375},{x:.84375,y:.84375},{x:.84375,y:.84375},{x:.90625,y:.84375},{x:.90625,y:.84375},{x:.96875,y:.84375},{x:.96875,y:.84375},{x:.03125,y:.90625},{x:.03125,y:.90625},{x:.09375,y:.90625},{x:.09375,y:.90625},{x:.15625,y:.90625},{x:.15625,y:.90625},{x:.21875,y:.90625},{x:.21875,y:.90625},{x:.28125,y:.90625},{x:.28125,y:.90625},{x:.34375,y:.90625},{x:.34375,y:.90625},{x:.40625,y:.90625},{x:.40625,y:.90625},{x:.46875,y:.90625},{x:.46875,y:.90625},{x:.53125,y:.90625},{x:.53125,y:.90625},{x:.59375,y:.90625},{x:.59375,y:.90625},{x:.65625,y:.90625},{x:.65625,y:.90625},{x:.71875,y:.90625},{x:.71875,y:.90625},{x:.78125,y:.90625},{x:.78125,y:.90625},{x:.84375,y:.90625},{x:.84375,y:.90625},{x:.90625,y:.90625},{x:.90625,y:.90625},{x:.96875,y:.90625},{x:.96875,y:.90625},{x:.03125,y:.96875},{x:.03125,y:.96875},{x:.09375,y:.96875},{x:.09375,y:.96875},{x:.15625,y:.96875},{x:.15625,y:.96875},{x:.21875,y:.96875},{x:.21875,y:.96875},{x:.28125,y:.96875},{x:.28125,y:.96875},{x:.34375,y:.96875},{x:.34375,y:.96875},{x:.40625,y:.96875},{x:.40625,y:.96875},{x:.46875,y:.96875},{x:.46875,y:.96875},{x:.53125,y:.96875},{x:.53125,y:.96875},{x:.59375,y:.96875},{x:.59375,y:.96875},{x:.65625,y:.96875},{x:.65625,y:.96875},{x:.71875,y:.96875},{x:.71875,y:.96875},{x:.78125,y:.96875},{x:.78125,y:.96875},{x:.84375,y:.96875},{x:.84375,y:.96875},{x:.90625,y:.96875},{x:.90625,y:.96875},{x:.96875,y:.96875},{x:.96875,y:.96875},{x:.0625,y:.0625},{x:.0625,y:.0625},{x:.0625,y:.0625},{x:.0625,y:.0625},{x:.0625,y:.0625},{x:.0625,y:.0625},{x:.1875,y:.0625},{x:.1875,y:.0625},{x:.1875,y:.0625},{x:.1875,y:.0625},{x:.1875,y:.0625},{x:.1875,y:.0625},{x:.3125,y:.0625},{x:.3125,y:.0625},{x:.3125,y:.0625},{x:.3125,y:.0625},{x:.3125,y:.0625},{x:.3125,y:.0625},{x:.4375,y:.0625},{x:.4375,y:.0625},{x:.4375,y:.0625},{x:.4375,y:.0625},{x:.4375,y:.0625},{x:.4375,y:.0625},{x:.5625,y:.0625},{x:.5625,y:.0625},{x:.5625,y:.0625},{x:.5625,y:.0625},{x:.5625,y:.0625},{x:.5625,y:.0625},{x:.6875,y:.0625},{x:.6875,y:.0625},{x:.6875,y:.0625},{x:.6875,y:.0625},{x:.6875,y:.0625},{x:.6875,y:.0625},{x:.8125,y:.0625},{x:.8125,y:.0625},{x:.8125,y:.0625},{x:.8125,y:.0625},{x:.8125,y:.0625},{x:.8125,y:.0625},{x:.9375,y:.0625},{x:.9375,y:.0625},{x:.9375,y:.0625},{x:.9375,y:.0625},{x:.9375,y:.0625},{x:.9375,y:.0625},{x:.0625,y:.1875},{x:.0625,y:.1875},{x:.0625,y:.1875},{x:.0625,y:.1875},{x:.0625,y:.1875},{x:.0625,y:.1875},{x:.1875,y:.1875},{x:.1875,y:.1875},{x:.1875,y:.1875},{x:.1875,y:.1875},{x:.1875,y:.1875},{x:.1875,y:.1875},{x:.3125,y:.1875},{x:.3125,y:.1875},{x:.3125,y:.1875},{x:.3125,y:.1875},{x:.3125,y:.1875},{x:.3125,y:.1875},{x:.4375,y:.1875},{x:.4375,y:.1875},{x:.4375,y:.1875},{x:.4375,y:.1875},{x:.4375,y:.1875},{x:.4375,y:.1875},{x:.5625,y:.1875},{x:.5625,y:.1875},{x:.5625,y:.1875},{x:.5625,y:.1875},{x:.5625,y:.1875},{x:.5625,y:.1875},{x:.6875,y:.1875},{x:.6875,y:.1875},{x:.6875,y:.1875},{x:.6875,y:.1875},{x:.6875,y:.1875},{x:.6875,y:.1875},{x:.8125,y:.1875},{x:.8125,y:.1875},{x:.8125,y:.1875},{x:.8125,y:.1875},{x:.8125,y:.1875},{x:.8125,y:.1875},{x:.9375,y:.1875},{x:.9375,y:.1875},{x:.9375,y:.1875},{x:.9375,y:.1875},{x:.9375,y:.1875},{x:.9375,y:.1875},{x:.0625,y:.3125},{x:.0625,y:.3125},{x:.0625,y:.3125},{x:.0625,y:.3125},{x:.0625,y:.3125},{x:.0625,y:.3125},{x:.1875,y:.3125},{x:.1875,y:.3125},{x:.1875,y:.3125},{x:.1875,y:.3125},{x:.1875,y:.3125},{x:.1875,y:.3125},{x:.3125,y:.3125},{x:.3125,y:.3125},{x:.3125,y:.3125},{x:.3125,y:.3125},{x:.3125,y:.3125},{x:.3125,y:.3125},{x:.4375,y:.3125},{x:.4375,y:.3125},{x:.4375,y:.3125},{x:.4375,y:.3125},{x:.4375,y:.3125},{x:.4375,y:.3125},{x:.5625,y:.3125},{x:.5625,y:.3125},{x:.5625,y:.3125},{x:.5625,y:.3125},{x:.5625,y:.3125},{x:.5625,y:.3125},{x:.6875,y:.3125},{x:.6875,y:.3125},{x:.6875,y:.3125},{x:.6875,y:.3125},{x:.6875,y:.3125},{x:.6875,y:.3125},{x:.8125,y:.3125},{x:.8125,y:.3125},{x:.8125,y:.3125},{x:.8125,y:.3125},{x:.8125,y:.3125},{x:.8125,y:.3125},{x:.9375,y:.3125},{x:.9375,y:.3125},{x:.9375,y:.3125},{x:.9375,y:.3125},{x:.9375,y:.3125},{x:.9375,y:.3125},{x:.0625,y:.4375},{x:.0625,y:.4375},{x:.0625,y:.4375},{x:.0625,y:.4375},{x:.0625,y:.4375},{x:.0625,y:.4375},{x:.1875,y:.4375},{x:.1875,y:.4375},{x:.1875,y:.4375},{x:.1875,y:.4375},{x:.1875,y:.4375},{x:.1875,y:.4375},{x:.3125,y:.4375},{x:.3125,y:.4375},{x:.3125,y:.4375},{x:.3125,y:.4375},{x:.3125,y:.4375},{x:.3125,y:.4375},{x:.4375,y:.4375},{x:.4375,y:.4375},{x:.4375,y:.4375},{x:.4375,y:.4375},{x:.4375,y:.4375},{x:.4375,y:.4375},{x:.5625,y:.4375},{x:.5625,y:.4375},{x:.5625,y:.4375},{x:.5625,y:.4375},{x:.5625,y:.4375},{x:.5625,y:.4375},{x:.6875,y:.4375},{x:.6875,y:.4375},{x:.6875,y:.4375},{x:.6875,y:.4375},{x:.6875,y:.4375},{x:.6875,y:.4375},{x:.8125,y:.4375},{x:.8125,y:.4375},{x:.8125,y:.4375},{x:.8125,y:.4375},{x:.8125,y:.4375},{x:.8125,y:.4375},{x:.9375,y:.4375},{x:.9375,y:.4375},{x:.9375,y:.4375},{x:.9375,y:.4375},{x:.9375,y:.4375},{x:.9375,y:.4375},{x:.0625,y:.5625},{x:.0625,y:.5625},{x:.0625,y:.5625},{x:.0625,y:.5625},{x:.0625,y:.5625},{x:.0625,y:.5625},{x:.1875,y:.5625},{x:.1875,y:.5625},{x:.1875,y:.5625},{x:.1875,y:.5625},{x:.1875,y:.5625},{x:.1875,y:.5625},{x:.3125,y:.5625},{x:.3125,y:.5625},{x:.3125,y:.5625},{x:.3125,y:.5625},{x:.3125,y:.5625},{x:.3125,y:.5625},{x:.4375,y:.5625},{x:.4375,y:.5625},{x:.4375,y:.5625},{x:.4375,y:.5625},{x:.4375,y:.5625},{x:.4375,y:.5625},{x:.5625,y:.5625},{x:.5625,y:.5625},{x:.5625,y:.5625},{x:.5625,y:.5625},{x:.5625,y:.5625},{x:.5625,y:.5625},{x:.6875,y:.5625},{x:.6875,y:.5625},{x:.6875,y:.5625},{x:.6875,y:.5625},{x:.6875,y:.5625},{x:.6875,y:.5625},{x:.8125,y:.5625},{x:.8125,y:.5625},{x:.8125,y:.5625},{x:.8125,y:.5625},{x:.8125,y:.5625},{x:.8125,y:.5625},{x:.9375,y:.5625},{x:.9375,y:.5625},{x:.9375,y:.5625},{x:.9375,y:.5625},{x:.9375,y:.5625},{x:.9375,y:.5625},{x:.0625,y:.6875},{x:.0625,y:.6875},{x:.0625,y:.6875},{x:.0625,y:.6875},{x:.0625,y:.6875},{x:.0625,y:.6875},{x:.1875,y:.6875},{x:.1875,y:.6875},{x:.1875,y:.6875},{x:.1875,y:.6875},{x:.1875,y:.6875},{x:.1875,y:.6875},{x:.3125,y:.6875},{x:.3125,y:.6875},{x:.3125,y:.6875},{x:.3125,y:.6875},{x:.3125,y:.6875},{x:.3125,y:.6875},{x:.4375,y:.6875},{x:.4375,y:.6875},{x:.4375,y:.6875},{x:.4375,y:.6875},{x:.4375,y:.6875},{x:.4375,y:.6875},{x:.5625,y:.6875},{x:.5625,y:.6875},{x:.5625,y:.6875},{x:.5625,y:.6875},{x:.5625,y:.6875},{x:.5625,y:.6875},{x:.6875,y:.6875},{x:.6875,y:.6875},{x:.6875,y:.6875},{x:.6875,y:.6875},{x:.6875,y:.6875},{x:.6875,y:.6875},{x:.8125,y:.6875},{x:.8125,y:.6875},{x:.8125,y:.6875},{x:.8125,y:.6875},{x:.8125,y:.6875},{x:.8125,y:.6875},{x:.9375,y:.6875},{x:.9375,y:.6875},{x:.9375,y:.6875},{x:.9375,y:.6875},{x:.9375,y:.6875},{x:.9375,y:.6875},{x:.0625,y:.8125},{x:.0625,y:.8125},{x:.0625,y:.8125},{x:.0625,y:.8125},{x:.0625,y:.8125},{x:.0625,y:.8125},{x:.1875,y:.8125},{x:.1875,y:.8125},{x:.1875,y:.8125},{x:.1875,y:.8125},{x:.1875,y:.8125},{x:.1875,y:.8125},{x:.3125,y:.8125},{x:.3125,y:.8125},{x:.3125,y:.8125},{x:.3125,y:.8125},{x:.3125,y:.8125},{x:.3125,y:.8125},{x:.4375,y:.8125},{x:.4375,y:.8125},{x:.4375,y:.8125},{x:.4375,y:.8125},{x:.4375,y:.8125},{x:.4375,y:.8125},{x:.5625,y:.8125},{x:.5625,y:.8125},{x:.5625,y:.8125},{x:.5625,y:.8125},{x:.5625,y:.8125},{x:.5625,y:.8125},{x:.6875,y:.8125},{x:.6875,y:.8125},{x:.6875,y:.8125},{x:.6875,y:.8125},{x:.6875,y:.8125},{x:.6875,y:.8125},{x:.8125,y:.8125},{x:.8125,y:.8125},{x:.8125,y:.8125},{x:.8125,y:.8125},{x:.8125,y:.8125},{x:.8125,y:.8125},{x:.9375,y:.8125},{x:.9375,y:.8125},{x:.9375,y:.8125},{x:.9375,y:.8125},{x:.9375,y:.8125},{x:.9375,y:.8125},{x:.0625,y:.9375},{x:.0625,y:.9375},{x:.0625,y:.9375},{x:.0625,y:.9375},{x:.0625,y:.9375},{x:.0625,y:.9375},{x:.1875,y:.9375},{x:.1875,y:.9375},{x:.1875,y:.9375},{x:.1875,y:.9375},{x:.1875,y:.9375},{x:.1875,y:.9375},{x:.3125,y:.9375},{x:.3125,y:.9375},{x:.3125,y:.9375},{x:.3125,y:.9375},{x:.3125,y:.9375},{x:.3125,y:.9375},{x:.4375,y:.9375},{x:.4375,y:.9375},{x:.4375,y:.9375},{x:.4375,y:.9375},{x:.4375,y:.9375},{x:.4375,y:.9375},{x:.5625,y:.9375},{x:.5625,y:.9375},{x:.5625,y:.9375},{x:.5625,y:.9375},{x:.5625,y:.9375},{x:.5625,y:.9375},{x:.6875,y:.9375},{x:.6875,y:.9375},{x:.6875,y:.9375},{x:.6875,y:.9375},{x:.6875,y:.9375},{x:.6875,y:.9375},{x:.8125,y:.9375},{x:.8125,y:.9375},{x:.8125,y:.9375},{x:.8125,y:.9375},{x:.8125,y:.9375},{x:.8125,y:.9375},{x:.9375,y:.9375},{x:.9375,y:.9375},{x:.9375,y:.9375},{x:.9375,y:.9375},{x:.9375,y:.9375},{x:.9375,y:.9375}];var q2=class{constructor(t){z(this,"model");z(this,"anchors");z(this,"anchorsTensor");z(this,"inputSize");z(this,"inputSizeTensor");z(this,"doubleInputSizeTensor");var n,o,s,A;this.model=t,this.anchors=An.map(a=>[a.x,a.y]),this.anchorsTensor=r.tensor2d(this.anchors),this.inputSize=((A=(s=(o=(n=this==null?void 0:this.model)==null?void 0:n.inputs)==null?void 0:o[0])==null?void 0:s.shape)==null?void 0:A[2])||0,this.inputSizeTensor=r.tensor1d([this.inputSize,this.inputSize]),this.doubleInputSizeTensor=r.tensor1d([this.inputSize*2,this.inputSize*2])}normalizeBoxes(t){let n={};n.boxOffsets=r.slice(t,[0,0],[-1,2]),n.boxSizes=r.slice(t,[0,2],[-1,2]),n.div=r.div(n.boxOffsets,this.inputSizeTensor),n.boxCenterPoints=r.add(n.div,this.anchorsTensor),n.halfBoxSizes=r.div(n.boxSizes,this.doubleInputSizeTensor),n.sub=r.sub(n.boxCenterPoints,n.halfBoxSizes),n.startPoints=r.mul(n.sub,this.inputSizeTensor),n.add=r.add(n.boxCenterPoints,n.halfBoxSizes),n.endPoints=r.mul(n.add,this.inputSizeTensor);let o=r.concat2d([n.startPoints,n.endPoints],1);return Object.keys(n).forEach(s=>r.dispose(n[s])),o}normalizeLandmarks(t,n){let o={};o.reshape=r.reshape(t,[-1,7,2]),o.div=r.div(o.reshape,this.inputSizeTensor),o.landmarks=r.add(o.div,this.anchors[n]?this.anchors[n]:0);let s=r.mul(o.landmarks,this.inputSizeTensor);return Object.keys(o).forEach(A=>r.dispose(o[A])),s}async predict(t,n){var i;let o={};o.resize=r.image.resizeBilinear(t,[this.inputSize,this.inputSize]),o.div=r.div(o.resize,O.tf127),o.image=r.sub(o.div,O.tf1),o.batched=this.model.execute(o.image),o.predictions=r.squeeze(o.batched),o.slice=r.slice(o.predictions,[0,0],[-1,1]),o.sigmoid=r.sigmoid(o.slice),o.scores=r.squeeze(o.sigmoid);let s=await o.scores.data();o.boxes=r.slice(o.predictions,[0,1],[-1,4]),o.norm=this.normalizeBoxes(o.boxes),o.nms=await r.image.nonMaxSuppressionAsync(o.norm,o.scores,3*(((i=n.hand)==null?void 0:i.maxDetected)||1),n.hand.iouThreshold,n.hand.minConfidence);let A=await o.nms.array(),a=[];for(let c of A){let d={};d.box=r.slice(o.norm,[c,0],[1,-1]),d.slice=r.slice(o.predictions,[c,5],[1,14]),d.norm=this.normalizeLandmarks(d.slice,c),d.palmLandmarks=r.reshape(d.norm,[-1,2]);let x=await d.box.data(),l=x.slice(0,2),f=x.slice(2,4),y=await d.palmLandmarks.array(),p={startPoint:l,endPoint:f,palmLandmarks:y,confidence:s[c]},m=nn(p,[(t.shape[2]||1)/this.inputSize,(t.shape[1]||0)/this.inputSize]);a.push(m),Object.keys(d).forEach(b=>r.dispose(d[b]))}return Object.keys(o).forEach(c=>r.dispose(o[c])),a}};var Bs=5,an=1.65,ln=[0,5,9,13,17,1,2],Hs=0,Gs=2,cn=0,U2=class{constructor(t,n){z(this,"handDetector");z(this,"handPoseModel");z(this,"inputSize");z(this,"storedBoxes");z(this,"skipped");z(this,"detectedHands");var o,s,A;this.handDetector=t,this.handPoseModel=n,this.inputSize=((A=(s=(o=this.handPoseModel)==null?void 0:o.inputs)==null?void 0:s[0].shape)==null?void 0:A[2])||0,this.storedBoxes=[],this.skipped=Number.MAX_SAFE_INTEGER,this.detectedHands=0}calculateLandmarksBoundingBox(t){let n=t.map(a=>a[0]),o=t.map(a=>a[1]),s=[Math.min(...n),Math.min(...o)],A=[Math.max(...n),Math.max(...o)];return{startPoint:s,endPoint:A}}getBoxForPalmLandmarks(t,n){let o=t.map(A=>f5([...A,1],n)),s=this.calculateLandmarksBoundingBox(o);return Z2(X2(s),Bs)}getBoxForHandLandmarks(t){let n=this.calculateLandmarksBoundingBox(t),o=Z2(X2(n),an);o.palmLandmarks=[];for(let s=0;s<ln.length;s++)o.palmLandmarks.push(t[ln[s]].slice(0,2));return o}transformRawCoords(t,n,o,s){let A=V2(n),a=[A[0]/this.inputSize,A[1]/this.inputSize,(A[0]+A[1])/this.inputSize/2],i=t.map(y=>[a[0]*(y[0]-this.inputSize/2),a[1]*(y[1]-this.inputSize/2),a[2]*y[2]]),c=y5(o,[0,0]),d=i.map(y=>[...f5(y,c),y[2]]),x=rn(s),l=[...r2(n),1],f=[ue(l,x[0]),ue(l,x[1])];return d.map(y=>[Math.trunc(y[0]+f[0]),Math.trunc(y[1]+f[1]),Math.trunc(y[2])])}async estimateHands(t,n){let o=!1,s,A=(n.hand.skipTime||0)>R()-cn,a=this.skipped<(n.hand.skipFrames||0);n.skipAllowed&&A&&a?this.skipped++:(s=await this.handDetector.predict(t,n),this.skipped=0),s&&s.length>0&&(s.length!==this.detectedHands&&this.detectedHands!==n.hand.maxDetected||!n.hand.landmarks)&&(this.detectedHands=0,this.storedBoxes=[...s],this.storedBoxes.length>0&&(o=!0));let i=[];for(let c=0;c<this.storedBoxes.length;c++){let d=this.storedBoxes[c];if(d)if(n.hand.landmarks){let x=n.hand.rotation?on(d.palmLandmarks[Hs],d.palmLandmarks[Gs]):0,l=r2(d),f=[l[0]/t.shape[2],l[1]/t.shape[1]],y=n.hand.rotation&&M.kernels.includes("rotatewithoffset")?r.image.rotateWithOffset(t,x,0,f):t.clone(),p=y5(-x,l),m=o?this.getBoxForPalmLandmarks(d.palmLandmarks,p):d,b=tn(m,y,[this.inputSize,this.inputSize]),v=r.div(b,O.tf255);r.dispose(b),r.dispose(y);let[T,u]=this.handPoseModel.execute(v);cn=R(),r.dispose(v);let h=(await T.data())[0];if(r.dispose(T),h>=n.hand.minConfidence/4){let w=r.reshape(u,[-1,3]),k=await w.array();r.dispose(u),r.dispose(w);let I=this.transformRawCoords(k,m,x,p),C=this.getBoxForHandLandmarks(I);this.storedBoxes[c]={...C,confidence:h};let V={landmarks:I,confidence:h,boxConfidence:d.confidence,fingerConfidence:h,box:{topLeft:C.startPoint,bottomRight:C.endPoint}};i.push(V)}else this.storedBoxes[c]=null;r.dispose(u)}else{let x=Z2(X2(d),an),l={confidence:d.confidence,boxConfidence:d.confidence,fingerConfidence:0,box:{topLeft:x.startPoint,bottomRight:x.endPoint},landmarks:[]};i.push(l)}}return this.storedBoxes=this.storedBoxes.filter(c=>c!==null),this.detectedHands=i.length,i.length>n.hand.maxDetected&&(i.length=n.hand.maxDetected),i}};var dn={thumb:[1,2,3,4],index:[5,6,7,8],middle:[9,10,11,12],ring:[13,14,15,16],pinky:[17,18,19,20],palm:[0]},Ne,Ie,m5;function Zs(){let e=Ne?new q2(Ne):void 0;e&&Ie&&(m5=new U2(e,Ie))}async function p5(e,t){m5||Zs();let n=await m5.estimateHands(e,t);if(!n)return[];let o=[];for(let s=0;s<n.length;s++){let A={};if(n[s].landmarks)for(let x of Object.keys(dn))A[x]=dn[x].map(l=>n[s].landmarks[l]);let a=n[s].landmarks,i=[Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,0,0],c=[0,0,0,0];if(a&&a.length>0){for(let x of a)x[0]<i[0]&&(i[0]=x[0]),x[1]<i[1]&&(i[1]=x[1]),x[0]>i[2]&&(i[2]=x[0]),x[1]>i[3]&&(i[3]=x[1]);i[2]-=i[0],i[3]-=i[1],c=[i[0]/(e.shape[2]||0),i[1]/(e.shape[1]||0),i[2]/(e.shape[2]||0),i[3]/(e.shape[1]||0)]}else i=n[s].box?[Math.trunc(Math.max(0,n[s].box.topLeft[0])),Math.trunc(Math.max(0,n[s].box.topLeft[1])),Math.trunc(Math.min(e.shape[2]||0,n[s].box.bottomRight[0])-Math.max(0,n[s].box.topLeft[0])),Math.trunc(Math.min(e.shape[1]||0,n[s].box.bottomRight[1])-Math.max(0,n[s].box.topLeft[1]))]:[0,0,0,0],c=[n[s].box.topLeft[0]/(e.shape[2]||0),n[s].box.topLeft[1]/(e.shape[1]||0),(n[s].box.bottomRight[0]-n[s].box.topLeft[0])/(e.shape[2]||0),(n[s].box.bottomRight[1]-n[s].box.topLeft[1])/(e.shape[1]||0)];let d=G2(a);o.push({id:s,score:Math.round(100*n[s].confidence)/100,boxScore:Math.round(100*n[s].boxConfidence)/100,fingerScore:Math.round(100*n[s].fingerConfidence)/100,label:"hand",box:i,boxRaw:c,keypoints:a,annotations:A,landmarks:d})}return o}async function xn(e){var t;return M.initial&&(Ne=null),Ne?e.debug&&g("cached model:",Ne.modelUrl):Ne=await L((t=e.hand.detector)==null?void 0:t.modelPath),Ne}async function yn(e){var t;return M.initial&&(Ie=null),Ie?e.debug&&g("cached model:",Ie.modelUrl):Ie=await L((t=e.hand.skeleton)==null?void 0:t.modelPath),Ie}var r0=[null,null],Xs=["StatefulPartitionedCall/Postprocessor/Slice","StatefulPartitionedCall/Postprocessor/ExpandDims_1"],he=[[0,0],[0,0]],qs=["hand","fist","pinch","point","face","tip","pinchtip"],mn=4,pn=1.6,Us=512,Ys=1.4,Y2=Number.MAX_SAFE_INTEGER,u5=0,te=[0,0],o0={boxes:[],hands:[]},un={thumb:[1,2,3,4],index:[5,6,7,8],middle:[9,10,11,12],ring:[13,14,15,16],pinky:[17,18,19,20],base:[0],palm:[0,17,13,9,5,1,0]};async function hn(e){var t;if(M.initial&&(r0[0]=null),r0[0])e.debug&&g("cached model:",r0[0].modelUrl);else{h2(["tensorlistreserve","enter","tensorlistfromtensor","merge","loopcond","switch","exit","tensorliststack","nextiteration","tensorlistsetitem","tensorlistgetitem","reciprocal","shape","split","where"],e),r0[0]=await L((t=e.hand.detector)==null?void 0:t.modelPath);let n=r0[0].executor?Object.values(r0[0].modelSignature.inputs):void 0;he[0][0]=Array.isArray(n)?parseInt(n[0].tensorShape.dim[1].size):0,he[0][1]=Array.isArray(n)?parseInt(n[0].tensorShape.dim[2].size):0}return r0[0]}async function bn(e){var t;if(M.initial&&(r0[1]=null),r0[1])e.debug&&g("cached model:",r0[1].modelUrl);else{r0[1]=await L((t=e.hand.skeleton)==null?void 0:t.modelPath);let n=r0[1].executor?Object.values(r0[1].modelSignature.inputs):void 0;he[1][0]=Array.isArray(n)?parseInt(n[0].tensorShape.dim[1].size):0,he[1][1]=Array.isArray(n)?parseInt(n[0].tensorShape.dim[2].size):0}return r0[1]}async function Ks(e,t){let n=[];if(!e||!r0[0])return n;let o={},s=(e.shape[2]||1)/(e.shape[1]||1),A=Math.min(Math.round((e.shape[1]||0)/8)*8,Us),a=Math.round(A*s/8)*8;o.resize=r.image.resizeBilinear(e,[A,a]),o.cast=r.cast(o.resize,"int32"),[o.rawScores,o.rawBoxes]=await r0[0].executeAsync(o.cast,Xs),o.boxes=r.squeeze(o.rawBoxes,[0,2]),o.scores=r.squeeze(o.rawScores,[0]);let i=r.unstack(o.scores,1);r.dispose(i[mn]),i.splice(mn,1),o.filtered=r.stack(i,1),r.dispose(i),o.max=r.max(o.filtered,1),o.argmax=r.argMax(o.filtered,1);let c=0;o.nms=await r.image.nonMaxSuppressionAsync(o.boxes,o.max,(t.hand.maxDetected||0)+1,t.hand.iouThreshold||0,t.hand.minConfidence||1);let d=await o.nms.data(),x=await o.max.data(),l=await o.argmax.data();for(let f of Array.from(d)){let y=r.slice(o.boxes,f,1),p=await y.data();r.dispose(y);let m=[p[1],p[0],p[3]-p[1],p[2]-p[0]],b=k2(m,Ys),v=[Math.trunc(m[0]*te[0]),Math.trunc(m[1]*te[1]),Math.trunc(m[2]*te[0]),Math.trunc(m[3]*te[1])],T=x[f],u=qs[l[f]],h={id:c++,score:T,box:v,boxRaw:b,label:u};n.push(h)}return Object.keys(o).forEach(f=>r.dispose(o[f])),n.sort((f,y)=>y.score-f.score),n.length>(t.hand.maxDetected||1)&&(n.length=t.hand.maxDetected||1),n}async function h5(e,t,n){let o={id:t.id,score:Math.round(100*t.score)/100,boxScore:Math.round(100*t.score)/100,fingerScore:0,box:t.box,boxRaw:t.boxRaw,label:t.label,keypoints:[],landmarks:{},annotations:{}};if(e&&r0[1]&&n.hand.landmarks&&t.score>(n.hand.minConfidence||0)){let s={},A=[t.boxRaw[1],t.boxRaw[0],t.boxRaw[3]+t.boxRaw[1],t.boxRaw[2]+t.boxRaw[0]];s.crop=r.image.cropAndResize(e,[A],[0],[he[1][0],he[1][1]],"bilinear"),s.div=r.div(s.crop,O.tf255),[s.score,s.keypoints]=r0[1].execute(s.div,["Identity_1","Identity"]);let a=(await s.score.data())[0],i=(100-Math.trunc(100/(1+Math.exp(a))))/100;if(i>=(n.hand.minConfidence||0)){o.fingerScore=i,s.reshaped=r.reshape(s.keypoints,[-1,3]);let x=(await s.reshaped.array()).map(l=>[l[0]/he[1][1],l[1]/he[1][0],l[2]||0]).map(l=>[l[0]*t.boxRaw[2],l[1]*t.boxRaw[3],l[2]||0]);o.keypoints=x.map(l=>[te[0]*(l[0]+t.boxRaw[0]),te[1]*(l[1]+t.boxRaw[1]),l[2]||0]),o.landmarks=G2(o.keypoints);for(let l of Object.keys(un))o.annotations[l]=un[l].map(f=>o.landmarks&&o.keypoints[f]?o.keypoints[f]:null)}Object.keys(s).forEach(c=>r.dispose(s[c]))}return o}async function b5(e,t){var s,A;if(!((s=r0[0])!=null&&s.executor)||!((A=r0[1])!=null&&A.executor)||!r0[0].inputs[0].shape||!r0[1].inputs[0].shape)return[];te=[e.shape[2]||0,e.shape[1]||0],Y2++;let n=(t.hand.skipTime||0)>R()-u5,o=Y2<(t.hand.skipFrames||0);return t.skipAllowed&&n&&o?o0.hands:new Promise(async a=>{let i=3*(t.hand.skipTime||0)>R()-u5,c=Y2<3*(t.hand.skipFrames||0);t.skipAllowed&&o0.hands.length===t.hand.maxDetected?o0.hands=await Promise.all(o0.boxes.map(x=>h5(e,x,t))):t.skipAllowed&&i&&c&&o0.hands.length>0?o0.hands=await Promise.all(o0.boxes.map(x=>h5(e,x,t))):(o0.boxes=await Ks(e,t),u5=R(),o0.hands=await Promise.all(o0.boxes.map(x=>h5(e,x,t))),Y2=0);let d=[...o0.boxes];if(o0.boxes.length=0,t.cacheSensitivity>0)for(let x=0;x<o0.hands.length;x++){let l=p1(o0.hands[x].keypoints,te);if(l.box[2]/(e.shape[2]||1)>.05&&l.box[3]/(e.shape[1]||1)>.05&&o0.hands[x].fingerScore&&o0.hands[x].fingerScore>(t.hand.minConfidence||0)){let f=k2(l.box,pn),y=k2(l.boxRaw,pn);o0.boxes.push({...d[x],box:f,boxRaw:y})}}for(let x=0;x<o0.hands.length;x++){let l=ie(o0.hands[x].keypoints,te);o0.hands[x].box=l.box,o0.hands[x].boxRaw=l.boxRaw}a(o0.hands)})}var ne=(e=null)=>({face:[],body:[],hand:[],gesture:[],object:[],persons:[],performance:{},timestamp:0,width:0,height:0,error:e});var s2={};re(s2,{connected:()=>J2,horizontal:()=>g5,kpt:()=>K2,relative:()=>v5,vertical:()=>T5});var K2=["nose","leftEye","rightEye","leftEar","rightEar","leftShoulder","rightShoulder","leftElbow","rightElbow","leftWrist","rightWrist","leftHip","rightHip","leftKnee","rightKnee","leftAnkle","rightAnkle"],g5=[["leftEye","rightEye"],["leftEar","rightEar"],["leftShoulder","rightShoulder"],["leftElbow","rightElbow"],["leftWrist","rightWrist"],["leftHip","rightHip"],["leftKnee","rightKnee"],["leftAnkle","rightAnkle"]],T5=[["leftKnee","leftShoulder"],["rightKnee","rightShoulder"],["leftAnkle","leftKnee"],["rightAnkle","rightKnee"]],v5=[[["leftHip","rightHip"],["leftShoulder","rightShoulder"]],[["leftElbow","rightElbow"],["leftShoulder","rightShoulder"]]],J2={leftLeg:["leftHip","leftKnee","leftAnkle"],rightLeg:["rightHip","rightKnee","rightAnkle"],torso:["leftShoulder","rightShoulder","rightHip","leftHip","leftShoulder"],leftArm:["leftShoulder","leftElbow","leftWrist"],rightArm:["rightShoulder","rightElbow","rightWrist"],head:[]};var j=ne(),R5=0;function Tn(e,t){var a,i,c,d,x,l,f,y,p,m,b,v,T,u,h,w,k,I,C,V,W,G,X,U,Y,p0;let n=R();if(!e)return ne();let o=Date.now()-e.timestamp,s=o<1e3?8-Math.log(o+1):1;if(e.canvas&&(j.canvas=e.canvas),e.error&&(j.error=e.error),!j.body||e.body.length!==j.body.length)j.body=JSON.parse(JSON.stringify(e.body));else for(let P=0;P<e.body.length;P++){let i0=e.body[P].box.map((D,F)=>((s-1)*j.body[P].box[F]+D)/s),g0=e.body[P].boxRaw.map((D,F)=>((s-1)*j.body[P].boxRaw[F]+D)/s),e0=e.body[P].keypoints.map((D,F)=>{var w0,t0,oe,Ke,Oe,H5,G5,V5,Z5;return{score:D.score,part:D.part,position:[j.body[P].keypoints[F]?((s-1)*(j.body[P].keypoints[F].position[0]||0)+(D.position[0]||0))/s:D.position[0],j.body[P].keypoints[F]?((s-1)*(j.body[P].keypoints[F].position[1]||0)+(D.position[1]||0))/s:D.position[1],j.body[P].keypoints[F]?((s-1)*(j.body[P].keypoints[F].position[2]||0)+(D.position[2]||0))/s:D.position[2]],positionRaw:[j.body[P].keypoints[F]?((s-1)*(j.body[P].keypoints[F].positionRaw[0]||0)+(D.positionRaw[0]||0))/s:D.positionRaw[0],j.body[P].keypoints[F]?((s-1)*(j.body[P].keypoints[F].positionRaw[1]||0)+(D.positionRaw[1]||0))/s:D.positionRaw[1],j.body[P].keypoints[F]?((s-1)*(j.body[P].keypoints[F].positionRaw[2]||0)+(D.positionRaw[2]||0))/s:D.positionRaw[2]],distance:[j.body[P].keypoints[F]?((s-1)*(((w0=j.body[P].keypoints[F].distance)==null?void 0:w0[0])||0)+(((t0=D.distance)==null?void 0:t0[0])||0))/s:(oe=D.distance)==null?void 0:oe[0],j.body[P].keypoints[F]?((s-1)*(((Ke=j.body[P].keypoints[F].distance)==null?void 0:Ke[1])||0)+(((Oe=D.distance)==null?void 0:Oe[1])||0))/s:(H5=D.distance)==null?void 0:H5[1],j.body[P].keypoints[F]?((s-1)*(((G5=j.body[P].keypoints[F].distance)==null?void 0:G5[2])||0)+(((V5=D.distance)==null?void 0:V5[2])||0))/s:(Z5=D.distance)==null?void 0:Z5[2]]}}),Z={},S={connected:{}};(a=t.body.modelPath)!=null&&a.includes("efficientpose")?S=z2:(i=t.body.modelPath)!=null&&i.includes("blazepose")?S=M2:(c=t.body.modelPath)!=null&&c.includes("movenet")&&(S=s2);for(let[D,F]of Object.entries(S.connected)){let w0=[];for(let t0=0;t0<F.length-1;t0++){let oe=e0.find(Oe=>Oe.part===F[t0]),Ke=e0.find(Oe=>Oe.part===F[t0+1]);oe&&Ke&&w0.push([oe.position,Ke.position])}Z[D]=w0}j.body[P]={...e.body[P],box:i0,boxRaw:g0,keypoints:e0,annotations:Z}}if(!j.hand||e.hand.length!==j.hand.length)j.hand=JSON.parse(JSON.stringify(e.hand));else for(let P=0;P<e.hand.length;P++){let i0=e.hand[P].box.map((S,D)=>((s-1)*j.hand[P].box[D]+S)/s),g0=e.hand[P].boxRaw.map((S,D)=>((s-1)*j.hand[P].boxRaw[D]+S)/s);j.hand[P].keypoints.length!==e.hand[P].keypoints.length&&(j.hand[P].keypoints=e.hand[P].keypoints);let e0=e.hand[P].keypoints&&e.hand[P].keypoints.length>0?e.hand[P].keypoints.map((S,D)=>S.map((F,w0)=>((s-1)*(j.hand[P].keypoints[D][w0]||1)+(F||0))/s)):[],Z={};if(Object.keys(j.hand[P].annotations).length!==Object.keys(e.hand[P].annotations).length)j.hand[P].annotations=e.hand[P].annotations,Z=j.hand[P].annotations;else if(e.hand[P].annotations)for(let S of Object.keys(e.hand[P].annotations))Z[S]=(l=(x=(d=e.hand[P])==null?void 0:d.annotations)==null?void 0:x[S])!=null&&l[0]?e.hand[P].annotations[S].map((D,F)=>D.map((w0,t0)=>((s-1)*j.hand[P].annotations[S][F][t0]+w0)/s)):null;j.hand[P]={...e.hand[P],box:i0,boxRaw:g0,keypoints:e0,annotations:Z}}if(!j.face||e.face.length!==j.face.length)j.face=JSON.parse(JSON.stringify(e.face));else for(let P=0;P<e.face.length;P++){let i0=e.face[P].box.map((Z,S)=>((s-1)*j.face[P].box[S]+Z)/s),g0=e.face[P].boxRaw.map((Z,S)=>((s-1)*j.face[P].boxRaw[S]+Z)/s),e0=e.face[P].annotations;if(Object.keys(j.face[P].annotations).length!==Object.keys(e.face[P].annotations).length)j.face[P].annotations=e.face[P].annotations,e0=j.face[P].annotations;else if(e.face[P].annotations)for(let Z of Object.keys(e.face[P].annotations))e0[Z]=(p=(y=(f=e.face[P])==null?void 0:f.annotations)==null?void 0:y[Z])!=null&&p[0]?e.face[P].annotations[Z].map((S,D)=>S.map((F,w0)=>((s-1)*j.face[P].annotations[Z][D][w0]+F)/s)):null;if(e.face[P].rotation){let Z={matrix:[0,0,0,0,0,0,0,0,0],angle:{roll:0,yaw:0,pitch:0},gaze:{bearing:0,strength:0}};Z.matrix=(m=e.face[P].rotation)==null?void 0:m.matrix,Z.angle={roll:((s-1)*(((v=(b=j.face[P].rotation)==null?void 0:b.angle)==null?void 0:v.roll)||0)+(((u=(T=e.face[P].rotation)==null?void 0:T.angle)==null?void 0:u.roll)||0))/s,yaw:((s-1)*(((w=(h=j.face[P].rotation)==null?void 0:h.angle)==null?void 0:w.yaw)||0)+(((I=(k=e.face[P].rotation)==null?void 0:k.angle)==null?void 0:I.yaw)||0))/s,pitch:((s-1)*(((V=(C=j.face[P].rotation)==null?void 0:C.angle)==null?void 0:V.pitch)||0)+(((G=(W=e.face[P].rotation)==null?void 0:W.angle)==null?void 0:G.pitch)||0))/s},Z.gaze={bearing:((s-1)*(((X=j.face[P].rotation)==null?void 0:X.gaze.bearing)||0)+(((U=e.face[P].rotation)==null?void 0:U.gaze.bearing)||0))/s,strength:((s-1)*(((Y=j.face[P].rotation)==null?void 0:Y.gaze.strength)||0)+(((p0=e.face[P].rotation)==null?void 0:p0.gaze.strength)||0))/s},j.face[P]={...e.face[P],rotation:Z,box:i0,boxRaw:g0,annotations:e0}}else j.face[P]={...e.face[P],box:i0,boxRaw:g0,annotations:e0}}if(!j.object||e.object.length!==j.object.length)j.object=JSON.parse(JSON.stringify(e.object));else for(let P=0;P<e.object.length;P++){let i0=e.object[P].box.map((e0,Z)=>((s-1)*j.object[P].box[Z]+e0)/s),g0=e.object[P].boxRaw.map((e0,Z)=>((s-1)*j.object[P].boxRaw[Z]+e0)/s);j.object[P]={...e.object[P],box:i0,boxRaw:g0}}if(e.persons){let P=e.persons;if(!j.persons||P.length!==j.persons.length)j.persons=JSON.parse(JSON.stringify(P));else for(let i0=0;i0<P.length;i0++)j.persons[i0].box=P[i0].box.map((g0,e0)=>((s-1)*j.persons[i0].box[e0]+g0)/s)}e.gesture&&(j.gesture=e.gesture),j.width=e.width,j.height=e.height;let A=R();return R5=M.perfadd?R5+Math.round(A-n):Math.round(A-n),e.performance&&(j.performance={...e.performance,interpolate:R5}),j}var h0;async function M5(e){return!h0||M.initial?h0=await L(e.segmentation.modelPath):e.debug&&g("cached model:",h0.modelUrl),h0}async function vn(e,t){var s;if(h0||(h0=await M5(t)),!(h0!=null&&h0.executor)||!((s=h0==null?void 0:h0.inputs)!=null&&s[0].shape))return null;let n={};n.resize=r.image.resizeBilinear(e,[h0.inputs[0].shape?h0.inputs[0].shape[1]:0,h0.inputs[0].shape?h0.inputs[0].shape[2]:0],!1),n.norm=r.div(n.resize,O.tf255),n.res=h0.execute(n.norm),n.squeeze=r.squeeze(n.res,[0]),[n.bgRaw,n.fgRaw]=r.unstack(n.squeeze,2),n.fg=r.softmax(n.fgRaw),n.mul=r.mul(n.fg,O.tf255),n.expand=r.expandDims(n.mul,2),n.output=r.image.resizeBilinear(n.expand,[e.shape[1]||0,e.shape[2]||0]);let o;switch(t.segmentation.mode||"default"){case"default":n.input=r.squeeze(e),n.concat=r.concat([n.input,n.output],-1),o=r.cast(n.concat,"int32");break;case"alpha":o=r.cast(n.output,"int32");break;default:o=r.tensor(0)}return Object.keys(n).forEach(A=>r.dispose(n[A])),o}var k5={};re(k5,{distance:()=>P5,find:()=>_s,similarity:()=>Qs});function P5(e,t,n={order:2,multiplier:25}){if(!e||!e)return Number.MAX_SAFE_INTEGER;let o=0;for(let A=0;A<e.length;A++){let a=!n.order||n.order===2?e[A]-t[A]:Math.abs(e[A]-t[A]);o+=!n.order||n.order===2?a*a:a**n.order}return Math.round(100*(n.multiplier||20)*o)/100}var Mn=(e,t,n,o)=>{if(e===0)return 1;let A=(1-(t===2?Math.sqrt(e):e**(1/t))/100-n)/(o-n);return Math.round(100*Math.max(Math.min(A,1),0))/100};function Qs(e,t,n={order:2,multiplier:25,min:.2,max:.8}){let o=P5(e,t,n);return Mn(o,n.order||2,n.min||0,n.max||1)}function _s(e,t,n={order:2,multiplier:25,threshold:0,min:.2,max:.8}){if(!Array.isArray(e)||!Array.isArray(t)||e.length<64||t.length===0)return{index:-1,distance:Number.POSITIVE_INFINITY,similarity:0};let o=Number.MAX_SAFE_INTEGER,s=-1;for(let a=0;a<t.length;a++){let i=t[a].length===e.length?P5(e,t[a],n):Number.MAX_SAFE_INTEGER;if(i<o&&(o=i,s=a),o<(n.threshold||0))break}let A=Mn(o,n.order||2,n.min||0,n.max||1);return{index:s,distance:o,similarity:A}}var Yn={};re(Yn,{Models:()=>i2,validateModel:()=>ot});var Pn=.005,N0={keypoints:[],padding:[[0,0],[0,0],[0,0],[0,0]]};function w5(e){for(let t of g5){let n=e.keypoints.findIndex(s=>s.part===t[0]),o=e.keypoints.findIndex(s=>s.part===t[1]);if(e.keypoints[n]&&e.keypoints[o]&&e.keypoints[n].position[0]<e.keypoints[o].position[0]){let s=e.keypoints[n];e.keypoints[n]=e.keypoints[o],e.keypoints[o]=s}}for(let t of T5){let n=e.keypoints.findIndex(s=>s&&s.part===t[0]),o=e.keypoints.findIndex(s=>s&&s.part===t[1]);e.keypoints[n]&&e.keypoints[o]&&e.keypoints[n].position[1]<e.keypoints[o].position[1]&&e.keypoints.splice(n,1)}for(let[t,n]of v5){let o=e.keypoints.findIndex(d=>d&&d.part===t[0]),s=e.keypoints.findIndex(d=>d&&d.part===t[1]),A=e.keypoints.findIndex(d=>d&&d.part===n[0]),a=e.keypoints.findIndex(d=>d&&d.part===n[1]);if(!e.keypoints[A]||!e.keypoints[a])continue;let i=e.keypoints[o]?[Math.abs(e.keypoints[A].position[0]-e.keypoints[o].position[0]),Math.abs(e.keypoints[a].position[0]-e.keypoints[o].position[0])]:[0,0],c=e.keypoints[s]?[Math.abs(e.keypoints[a].position[0]-e.keypoints[s].position[0]),Math.abs(e.keypoints[A].position[0]-e.keypoints[s].position[0])]:[0,0];if(i[0]>i[1]||c[0]>c[1]){let d=e.keypoints[o];e.keypoints[o]=e.keypoints[s],e.keypoints[s]=d}}}function kn(e){for(let t=0;t<e.length;t++)if(e[t]&&N0.keypoints[t]){let n=[Math.abs(e[t].positionRaw[0]-N0.keypoints[t].positionRaw[0]),Math.abs(e[t].positionRaw[1]-N0.keypoints[t].positionRaw[1])];n[0]<Pn&&n[1]<Pn?e[t]=N0.keypoints[t]:N0.keypoints[t]=e[t]}else N0.keypoints[t]=e[t];return e}function wn(e,t){var s,A;let n={};if(!((s=e==null?void 0:e.shape)!=null&&s[1])||!((A=e==null?void 0:e.shape)!=null&&A[2]))return e;N0.padding=[[0,0],[e.shape[2]>e.shape[1]?Math.trunc((e.shape[2]-e.shape[1])/2):0,e.shape[2]>e.shape[1]?Math.trunc((e.shape[2]-e.shape[1])/2):0],[e.shape[1]>e.shape[2]?Math.trunc((e.shape[1]-e.shape[2])/2):0,e.shape[1]>e.shape[2]?Math.trunc((e.shape[1]-e.shape[2])/2):0],[0,0]],n.pad=r.pad(e,N0.padding),n.resize=r.image.resizeBilinear(n.pad,[t,t]);let o=r.cast(n.resize,"int32");return Object.keys(n).forEach(a=>r.dispose(n[a])),o}function En(e,t){e.keypoints=e.keypoints.filter(o=>o==null?void 0:o.position);for(let o of e.keypoints)o.position=[o.position[0]*(t[0]+N0.padding[2][0]+N0.padding[2][1])/t[0]-N0.padding[2][0],o.position[1]*(t[1]+N0.padding[1][0]+N0.padding[1][1])/t[1]-N0.padding[1][0]],o.positionRaw=[o.position[0]/t[0],o.position[1]/t[1]];let n=ie(e.keypoints.map(o=>o.position),t);return e.box=n.box,e.boxRaw=n.boxRaw,e}var A0,Q2=0,E5=Number.MAX_SAFE_INTEGER,Le={boxes:[],bodies:[],last:0};async function zn(e){var t;return M.initial&&(A0=null),A0?e.debug&&g("cached model:",A0.modelUrl):(h2(["size"],e),A0=await L(e.body.modelPath)),Q2=A0!=null&&A0.executor&&((t=A0==null?void 0:A0.inputs)!=null&&t[0].shape)?A0.inputs[0].shape[2]:0,Q2<64&&(Q2=256),r.env().flagRegistry.WEBGL_USE_SHAPES_UNIFORMS&&r.env().set("WEBGL_USE_SHAPES_UNIFORMS",!1),A0}function eA(e,t,n){let o=e[0][0],s=[],A=0;for(let x=0;x<o.length;x++)if(A=o[x][2],A>t.body.minConfidence){let l=[o[x][1],o[x][0]];s.push({score:Math.round(100*A)/100,part:K2[x],positionRaw:l,position:[Math.round((n.shape[2]||0)*l[0]),Math.round((n.shape[1]||0)*l[1])]})}A=s.reduce((x,l)=>l.score>x?l.score:x,0);let a=[],i=ie(s.map(x=>x.position),[n.shape[2],n.shape[1]]),c={};for(let[x,l]of Object.entries(J2)){let f=[];for(let y=0;y<l.length-1;y++){let p=s.find(b=>b.part===l[y]),m=s.find(b=>b.part===l[y+1]);p&&m&&p.score>(t.body.minConfidence||0)&&m.score>(t.body.minConfidence||0)&&f.push([p.position,m.position])}c[x]=f}let d={id:0,score:A,box:i.box,boxRaw:i.boxRaw,keypoints:s,annotations:c};return w5(d),a.push(d),a}function tA(e,t,n){let o=[];for(let s=0;s<e[0].length;s++){let A=e[0][s],a=Math.round(100*A[55])/100;if(a>t.body.minConfidence){let i=[];for(let f=0;f<17;f++){let y=A[3*f+2];if(y>t.body.minConfidence){let p=[A[3*f+1],A[3*f+0]];i.push({part:K2[f],score:Math.round(100*y)/100,positionRaw:p,position:[Math.round((n.shape[2]||0)*p[0]),Math.round((n.shape[1]||0)*p[1])]})}}let c=[A[52],A[51],A[54]-A[52],A[53]-A[51]],d=[Math.trunc(c[0]*(n.shape[2]||0)),Math.trunc(c[1]*(n.shape[1]||0)),Math.trunc(c[2]*(n.shape[2]||0)),Math.trunc(c[3]*(n.shape[1]||0))],x={};for(let[f,y]of Object.entries(J2)){let p=[];for(let m=0;m<y.length-1;m++){let b=i.find(T=>T.part===y[m]),v=i.find(T=>T.part===y[m+1]);b&&v&&b.score>(t.body.minConfidence||0)&&v.score>(t.body.minConfidence||0)&&p.push([b.position,v.position])}x[f]=p}let l={id:s,score:a,box:d,boxRaw:c,keypoints:[...i],annotations:x};w5(l),o.push(l)}}return o.sort((s,A)=>A.score-s.score),o.length>t.body.maxDetected&&(o.length=t.body.maxDetected),o}async function z5(e,t){var s;if(!(A0!=null&&A0.executor)||!((s=A0==null?void 0:A0.inputs)!=null&&s[0].shape))return[];t.skipAllowed||(Le.boxes.length=0),E5++;let n=(t.body.skipTime||0)>R()-Le.last,o=E5<(t.body.skipFrames||0);return t.skipAllowed&&n&&o?Le.bodies:new Promise(async A=>{let a={};E5=0,a.input=wn(e,Q2),a.res=A0==null?void 0:A0.execute(a.input),Le.last=R();let i=await a.res.array();Le.bodies=a.res.shape[2]===17?eA(i,t,e):tA(i,t,e);for(let c of Le.bodies)En(c,[e.shape[2]||1,e.shape[1]||1]),kn(c.keypoints);Object.keys(a).forEach(c=>r.dispose(a[c])),A(Le.bodies)})}var H0,_2=[],jn=0,S5=Number.MAX_SAFE_INTEGER,et=0,$2=2.5;async function Nn(e){if(!H0||M.initial){H0=await L(e.object.modelPath);let t=H0!=null&&H0.executor?Object.values(H0.modelSignature.inputs):void 0;et=Array.isArray(t)?parseInt(t[0].tensorShape.dim[2].size):416}else e.debug&&g("cached model:",H0.modelUrl);return H0}async function nA(e,t,n){var d,x;let o=0,s=[],A=et;for(let l of[1,2,4]){let f=l*13,y=r.squeeze(e.find(u=>u.shape[1]===f**2&&(u.shape[2]||0)===De.length)),p=await y.array(),m=r.squeeze(e.find(u=>u.shape[1]===f**2&&(u.shape[2]||0)<De.length)),b=r.reshape(m,[-1,4,(((d=m.shape)==null?void 0:d[1])||0)/4]),v=r.argMax(b,2),T=await v.array();for(let u=0;u<y.shape[0];u++)for(let h=0;h<(((x=y.shape)==null?void 0:x[1])||0);h++){let w=p[u][h];if(w>(n.object.minConfidence||0)&&h!==61){let k=(.5+Math.trunc(u%f))/f,I=(.5+Math.trunc(u/f))/f,C=T[u].map(P=>P*(f/l/A)),[V,W]=[k-$2/l*C[0],I-$2/l*C[1]],[G,X]=[k+$2/l*C[2]-V,I+$2/l*C[3]-W],U=[V,W,G,X];U=U.map(P=>Math.max(0,Math.min(P,1)));let Y=[U[0]*t[0],U[1]*t[1],U[2]*t[0],U[3]*t[1]],p0={id:o++,score:Math.round(100*w)/100,class:h+1,label:De[h].label,box:Y.map(P=>Math.trunc(P)),boxRaw:U};s.push(p0)}}r.dispose([y,m,b,v])}let a=s.map(l=>[l.boxRaw[1],l.boxRaw[0],l.boxRaw[3],l.boxRaw[2]]),i=s.map(l=>l.score),c=[];if(a&&a.length>0){let l=await r.image.nonMaxSuppressionAsync(a,i,n.object.maxDetected||0,n.object.iouThreshold,n.object.minConfidence);c=Array.from(await l.data()),r.dispose(l)}return s=s.filter((l,f)=>c.includes(f)).sort((l,f)=>f.score-l.score),s}async function j5(e,t){if(!(H0!=null&&H0.executor))return[];let n=(t.object.skipTime||0)>R()-jn,o=S5<(t.object.skipFrames||0);return t.skipAllowed&&n&&o&&_2.length>0?(S5++,_2):(S5=0,!M.kernels.includes("mod")||!M.kernels.includes("sparsetodense")?_2:new Promise(async s=>{let A=[e.shape[2]||0,e.shape[1]||0],a=r.image.resizeBilinear(e,[et,et],!1),i=r.div(a,O.tf255),c=r.transpose(i,[0,3,1,2]),d;t.object.enabled&&(d=H0.execute(c)),jn=R();let x=await nA(d,A,t);_2=x,r.dispose([a,i,c,...d]),s(x)}))}var a2=["nose","leftEye","rightEye","leftEar","rightEar","leftShoulder","rightShoulder","leftElbow","rightElbow","leftWrist","rightWrist","leftHip","rightHip","leftKnee","rightKnee","leftAnkle","rightAnkle"],oA=a2.length,A2=a2.reduce((e,t,n)=>(e[t]=n,e),{}),rA=[["leftHip","leftShoulder"],["leftElbow","leftShoulder"],["leftElbow","leftWrist"],["leftHip","leftKnee"],["leftKnee","leftAnkle"],["rightHip","rightShoulder"],["rightElbow","rightShoulder"],["rightElbow","rightWrist"],["rightHip","rightKnee"],["rightKnee","rightAnkle"],["leftShoulder","rightShoulder"],["leftHip","rightHip"]],_i=rA.map(([e,t])=>[A2[e],A2[t]]),Ln=[["nose","leftEye"],["leftEye","leftEar"],["nose","rightEye"],["rightEye","rightEar"],["nose","leftShoulder"],["leftShoulder","leftElbow"],["leftElbow","leftWrist"],["leftShoulder","leftHip"],["leftHip","leftKnee"],["leftKnee","leftAnkle"],["nose","rightShoulder"],["rightShoulder","rightElbow"],["rightElbow","rightWrist"],["rightShoulder","rightHip"],["rightHip","rightKnee"],["rightKnee","rightAnkle"]];function On(e){let t=e.reduce(({maxX:n,maxY:o,minX:s,minY:A},{position:{x:a,y:i}})=>({maxX:Math.max(n,a),maxY:Math.max(o,i),minX:Math.min(s,a),minY:Math.min(A,i)}),{maxX:Number.NEGATIVE_INFINITY,maxY:Number.NEGATIVE_INFINITY,minX:Number.POSITIVE_INFINITY,minY:Number.POSITIVE_INFINITY});return[t.minX,t.minY,t.maxX-t.minX,t.maxY-t.minY]}function Cn(e,[t,n],[o,s]){let A=t/o,a=n/s,i=(d,x)=>({id:x,score:d.score,boxRaw:[d.box[0]/s,d.box[1]/o,d.box[2]/s,d.box[3]/o],box:[Math.trunc(d.box[0]*a),Math.trunc(d.box[1]*A),Math.trunc(d.box[2]*a),Math.trunc(d.box[3]*A)],keypoints:d.keypoints.map(({score:l,part:f,position:y})=>({score:l,part:f,position:[Math.trunc(y.x*a),Math.trunc(y.y*A)],positionRaw:[y.x/o,y.y/o]})),annotations:{}});return e.map((d,x)=>i(d,x))}var tt=class{constructor(t,n){z(this,"priorityQueue");z(this,"numberOfElements");z(this,"getElementValue");this.priorityQueue=new Array(t),this.numberOfElements=-1,this.getElementValue=n}enqueue(t){this.priorityQueue[++this.numberOfElements]=t,this.swim(this.numberOfElements)}dequeue(){let t=this.priorityQueue[0];return this.exchange(0,this.numberOfElements--),this.sink(0),this.priorityQueue[this.numberOfElements+1]=null,t}empty(){return this.numberOfElements===-1}size(){return this.numberOfElements+1}all(){return this.priorityQueue.slice(0,this.numberOfElements+1)}max(){return this.priorityQueue[0]}swim(t){for(;t>0&&this.less(Math.floor(t/2),t);)this.exchange(t,Math.floor(t/2)),t=Math.floor(t/2)}sink(t){for(;2*t<=this.numberOfElements;){let n=2*t;if(n<this.numberOfElements&&this.less(n,n+1)&&n++,!this.less(t,n))break;this.exchange(t,n),t=n}}getValueAt(t){return this.getElementValue(this.priorityQueue[t])}less(t,n){return this.getValueAt(t)<this.getValueAt(n)}exchange(t,n){let o=this.priorityQueue[t];this.priorityQueue[t]=this.priorityQueue[n],this.priorityQueue[n]=o}};function N5(e,t,n,o){return{y:o.get(e,t,n),x:o.get(e,t,n+oA)}}function I5(e,t,n){let{heatmapY:o,heatmapX:s,id:A}=e,{y:a,x:i}=N5(o,s,A,n);return{x:e.heatmapX*t+i,y:e.heatmapY*t+a}}function L5(e,t,n){return e<t?t:e>n?n:e}function Wn(e,t,n,o){let s=n-e,A=o-t;return s*s+A*A}function O5(e,t){return{x:e.x+t.x,y:e.y+t.y}}var I0,AA=["MobilenetV1/offset_2/BiasAdd","MobilenetV1/heatmap_2/BiasAdd","MobilenetV1/displacement_fwd_2/BiasAdd","MobilenetV1/displacement_bwd_2/BiasAdd"],nt=1,Ue=16,aA=50**2;function Dn(e,t,n,o,s,A,a=2){let i=v=>({y:A.get(v.y,v.x,e),x:A.get(v.y,v.x,A.shape[2]/2+e)}),c=(v,T,u)=>({y:L5(Math.round(v.y/Ue),0,T-1),x:L5(Math.round(v.x/Ue),0,u-1)}),[d,x]=o.shape,l=c(t.position,d,x),f=i(l),p=O5(t.position,f);for(let v=0;v<a;v++){let T=c(p,d,x),u=N5(T.y,T.x,n,s);p=O5({x:T.x*Ue,y:T.y*Ue},{x:u.x,y:u.y})}let m=c(p,d,x),b=o.get(m.y,m.x,n);return{position:p,part:a2[n],score:b}}function iA(e,t,n,o,s){let A=Ln.map(([f,y])=>[A2[f],A2[y]]),a=A.map(([,f])=>f),i=A.map(([f])=>f),c=t.shape[2],d=a.length,x=new Array(c),l=I5(e.part,Ue,n);x[e.part.id]={score:e.score,part:a2[e.part.id],position:l};for(let f=d-1;f>=0;--f){let y=a[f],p=i[f];x[y]&&!x[p]&&(x[p]=Dn(f,x[y],p,t,n,s))}for(let f=0;f<d;++f){let y=i[f],p=a[f];x[y]&&!x[p]&&(x[p]=Dn(f,x[y],p,t,n,o))}return x}function lA(e,t,n,o,s){let[A,a]=s.shape,i=!0,c=Math.max(n-nt,0),d=Math.min(n+nt+1,A);for(let x=c;x<d;++x){let l=Math.max(o-nt,0),f=Math.min(o+nt+1,a);for(let y=l;y<f;++y)if(s.get(x,y,e)>t){i=!1;break}if(!i)break}return i}function cA(e,t){let[n,o,s]=t.shape,A=new tt(n*o*s,({score:a})=>a);for(let a=0;a<n;++a)for(let i=0;i<o;++i)for(let c=0;c<s;++c){let d=t.get(a,i,c);d<e||lA(c,d,a,i,t)&&A.enqueue({score:d,part:{heatmapY:a,heatmapX:i,id:c}})}return A}function Fn(e,{x:t,y:n},o){return e.some(({keypoints:s})=>{var a;let A=(a=s[o])==null?void 0:a.position;return A?Wn(n,t,A.y,A.x)<=aA:!1})}function dA(e,t){return t.reduce((o,{position:s,score:A},a)=>(Fn(e,s,a)||(o+=A),o),0)/t.length}function xA(e,t,n,o,s,A){let a=[],i=cA(A,t);for(;a.length<s&&!i.empty();){let c=i.dequeue(),d=I5(c.part,Ue,e);if(Fn(a,d,c.part.id))continue;let x=iA(c,t,e,n,o);x=x.filter(y=>y.score>A);let l=dA(a,x),f=On(x);l>A&&a.push({keypoints:x,box:f,score:Math.round(100*l)/100})}return a}async function C5(e,t){if(!(I0!=null&&I0.executor))return[];let n=r.tidy(()=>{if(!I0.inputs[0].shape)return[];let a=r.image.resizeBilinear(e,[I0.inputs[0].shape[2],I0.inputs[0].shape[1]]),i=r.sub(r.div(r.cast(a,"float32"),127.5),1),d=I0.execute(i,AA).map(x=>r.squeeze(x,[0]));return d[1]=r.sigmoid(d[1]),d}),o=await Promise.all(n.map(a=>a.buffer()));for(let a of n)r.dispose(a);let s=xA(o[0],o[1],o[2],o[3],t.body.maxDetected,t.body.minConfidence);return I0.inputs[0].shape?Cn(s,[e.shape[1],e.shape[2]],[I0.inputs[0].shape[2],I0.inputs[0].shape[1]]):[]}async function Bn(e){return!I0||M.initial?I0=await L(e.body.modelPath):e.debug&&g("cached model:",I0.modelUrl),I0}var Q0,yA=["fgr","pha","r1o","r2o","r3o","r4o"],a0={},D5=0;function Vn(e){r.dispose([a0.r1i,a0.r2i,a0.r3i,a0.r4i,a0.downsample_ratio]),a0.r1i=r.tensor(0),a0.r2i=r.tensor(0),a0.r3i=r.tensor(0),a0.r4i=r.tensor(0),D5=e.segmentation.ratio||.5,a0.downsample_ratio=r.tensor(D5)}async function F5(e){return!Q0||M.initial?Q0=await L(e.segmentation.modelPath):e.debug&&g("cached model:",Q0.modelUrl),Vn(e),Q0}var Gn=e=>r.tidy(()=>{let t=r.squeeze(e,[0]),n=r.mul(t,O.tf255);return r.cast(n,"int32")});function W5(e,t){let n=e?Gn(e):r.fill([t.shape[1]||0,t.shape[2]||0,3],255,"int32"),o=t?Gn(t):r.fill([e.shape[1]||0,e.shape[2]||0,1],255,"int32"),s=r.concat([n,o],-1);return r.dispose([n,o]),s}function fA(e){return r.tidy(()=>{let t={};return t.unstack=r.unstack(e,-1),t.concat=r.concat(t.unstack,1),t.split=r.split(t.concat,4,1),t.stack=r.concat(t.split,2),t.squeeze=r.squeeze(t.stack,[0]),t.expand=r.expandDims(t.squeeze,-1),t.add=r.add(t.expand,1),t.mul=r.mul(t.add,127.5),t.cast=r.cast(t.mul,"int32"),t.tile=r.tile(t.cast,[1,1,3]),t.alpha=r.fill([t.tile.shape[0]||0,t.tile.shape[1]||0,1],255,"int32"),r.concat([t.tile,t.alpha],-1)})}async function Zn(e,t){if(Q0||(Q0=await F5(t)),!(Q0!=null&&Q0.executor))return null;a0.src=r.div(e,255),D5!==t.segmentation.ratio&&Vn(t);let[n,o,s,A,a,i]=await Q0.executeAsync(a0,yA),c;switch(t.segmentation.mode||"default"){case"default":c=W5(n,o);break;case"alpha":c=W5(null,o);break;case"foreground":c=W5(n,null);break;case"state":c=fA(s);break;default:c=r.tensor(0)}return r.dispose([a0.src,n,o,a0.r1i,a0.r2i,a0.r3i,a0.r4i]),[a0.r1i,a0.r2i,a0.r3i,a0.r4i]=[s,A,a,i],c}var b0;async function B5(e){return!b0||M.initial?b0=await L(e.segmentation.modelPath):e.debug&&g("cached model:",b0.modelUrl),b0}async function qn(e,t){var s;if(b0||(b0=await B5(t)),!(b0!=null&&b0.executor)||!((s=b0==null?void 0:b0.inputs)!=null&&s[0].shape))return null;let n={};n.resize=r.image.resizeBilinear(e,[b0.inputs[0].shape?b0.inputs[0].shape[1]:0,b0.inputs[0].shape?b0.inputs[0].shape[2]:0],!1),n.norm=r.div(n.resize,O.tf255),n.res=b0.execute(n.norm),n.squeeze=r.squeeze(n.res,[0]),n.alpha=r.image.resizeBilinear(n.squeeze,[e.shape[1]||0,e.shape[2]||0]),n.mul=r.mul(n.alpha,O.tf255);let o;switch(t.segmentation.mode||"default"){case"default":n.input=r.squeeze(e),n.concat=r.concat([n.input,n.mul],-1),o=r.cast(n.concat,"int32");break;case"alpha":o=r.cast(n.mul,"int32");break;default:o=r.tensor(0)}return Object.keys(n).forEach(A=>r.dispose(n[A])),o}function ot(e,t,n){var d,x;if(!t||!((d=e==null?void 0:e.config)!=null&&d.validateModels))return null;let o=["const","placeholder","noop","pad","squeeze","add","sub","mul","div"],s=["biasadd","fusedbatchnormv3","matmul","switch","shape","merge","split","broadcastto"],A=[],a=[],i=t.modelUrl,c=t.executor;if((x=c==null?void 0:c.graph)!=null&&x.nodes)for(let l of Object.values(c.graph.nodes)){let f=l.op.toLowerCase();A.includes(f)||A.push(f)}else!c&&e.config.debug&&g("model not loaded",n);for(let l of A)!o.includes(l)&&!s.includes(l)&&!e.env.kernels.includes(l)&&!e.env.kernels.includes(l.replace("_",""))&&!e.env.kernels.includes(l.replace("native",""))&&!e.env.kernels.includes(l.replace("v2",""))&&a.push(l);return e.config.debug&&a.length>0&&g("model validation failed:",n,a),a.length>0?{name:n,missing:a,ops:A,url:i}:null}var i2=class{constructor(t){z(this,"instance");z(this,"models",{});this.models={},this.instance=t}stats(){let t=0,n=0,o=0;for(let A of Object.values(l0))t+=Number.isNaN(+A.sizeFromManifest)?0:A.sizeFromManifest,n+=Number.isNaN(+A.sizeLoadedWeights)?0:A.sizeLoadedWeights,o+=Number.isNaN(+A.sizeDesired)?0:A.sizeDesired;let s=o>0?n/o:0;return{numLoadedModels:Object.values(l0).filter(A=>A==null?void 0:A.loaded).length,numDefinedModels:Object.keys(this.models).length,percentageLoaded:s,totalSizeFromManifest:t,totalSizeWeights:n,totalSizeLoading:o,modelStats:Object.values(l0)}}reset(){for(let t of Object.keys(this.models))this.models[t]=null}async load(t){var o,s,A,a,i,c,d,x,l,f,y,p,m,b,v,T,u,h,w,k,I,C,V,W,G,X,U;M.initial&&this.reset(),t&&(this.instance=t);let n={};n.blazeface=this.instance.config.face.enabled&&!this.models.blazeface?B1(this.instance.config):null,n.antispoof=this.instance.config.face.enabled&&((o=this.instance.config.face.antispoof)!=null&&o.enabled)&&!this.models.antispoof?d3(this.instance.config):null,n.liveness=this.instance.config.face.enabled&&((s=this.instance.config.face.liveness)!=null&&s.enabled)&&!this.models.liveness?m3(this.instance.config):null,n.faceres=this.instance.config.face.enabled&&((A=this.instance.config.face.description)!=null&&A.enabled)&&!this.models.faceres?A3(this.instance.config):null,n.emotion=this.instance.config.face.enabled&&((a=this.instance.config.face.emotion)!=null&&a.enabled)&&!this.models.emotion?n3(this.instance.config):null,n.iris=this.instance.config.face.enabled&&((i=this.instance.config.face.iris)!=null&&i.enabled)&&!((c=this.instance.config.face.attention)!=null&&c.enabled)&&!this.models.iris?q1(this.instance.config):null,n.facemesh=this.instance.config.face.enabled&&((d=this.instance.config.face.mesh)!=null&&d.enabled)&&!this.models.facemesh?Q1(this.instance.config):null,n.gear=this.instance.config.face.enabled&&((x=this.instance.config.face.gear)!=null&&x.enabled)&&!this.models.gear?b3(this.instance.config):null,n.ssrnetage=this.instance.config.face.enabled&&((l=this.instance.config.face.ssrnet)!=null&&l.enabled)&&!this.models.ssrnetage?R3(this.instance.config):null,n.ssrnetgender=this.instance.config.face.enabled&&((f=this.instance.config.face.ssrnet)!=null&&f.enabled)&&!this.models.ssrnetgender?w3(this.instance.config):null,n.mobilefacenet=this.instance.config.face.enabled&&((y=this.instance.config.face.mobilefacenet)!=null&&y.enabled)&&!this.models.mobilefacenet?N3(this.instance.config):null,n.insightface=this.instance.config.face.enabled&&((p=this.instance.config.face.insightface)!=null&&p.enabled)&&!this.models.insightface?W3(this.instance.config):null,n.blazepose=this.instance.config.body.enabled&&!this.models.blazepose&&((m=this.instance.config.body.modelPath)!=null&&m.includes("blazepose"))?T1(this.instance.config):null,n.blazeposedetect=this.instance.config.body.enabled&&!this.models.blazeposedetect&&this.instance.config.body.detector&&this.instance.config.body.detector.modelPath?g1(this.instance.config):null,n.efficientpose=this.instance.config.body.enabled&&!this.models.efficientpose&&((b=this.instance.config.body.modelPath)!=null&&b.includes("efficientpose"))?w1(this.instance.config):null,n.movenet=this.instance.config.body.enabled&&!this.models.movenet&&((v=this.instance.config.body.modelPath)!=null&&v.includes("movenet"))?zn(this.instance.config):null,n.posenet=this.instance.config.body.enabled&&!this.models.posenet&&((T=this.instance.config.body.modelPath)!=null&&T.includes("posenet"))?Bn(this.instance.config):null,n.handtrack=this.instance.config.hand.enabled&&!this.models.handtrack&&((h=(u=this.instance.config.hand.detector)==null?void 0:u.modelPath)!=null&&h.includes("handtrack"))?hn(this.instance.config):null,n.handskeleton=this.instance.config.hand.enabled&&this.instance.config.hand.landmarks&&!this.models.handskeleton&&((k=(w=this.instance.config.hand.detector)==null?void 0:w.modelPath)!=null&&k.includes("handtrack"))?bn(this.instance.config):null,this.instance.config.hand.enabled&&!this.models.handdetect&&((C=(I=this.instance.config.hand.detector)==null?void 0:I.modelPath)!=null&&C.includes("handdetect"))&&(n.handdetect=xn(this.instance.config),n.handskeleton=yn(this.instance.config)),n.centernet=this.instance.config.object.enabled&&!this.models.centernet&&((V=this.instance.config.object.modelPath)!=null&&V.includes("centernet"))?M1(this.instance.config):null,n.nanodet=this.instance.config.object.enabled&&!this.models.nanodet&&((W=this.instance.config.object.modelPath)!=null&&W.includes("nanodet"))?Nn(this.instance.config):null,n.selfie=this.instance.config.segmentation.enabled&&!this.models.selfie&&((G=this.instance.config.segmentation.modelPath)!=null&&G.includes("selfie"))?B5(this.instance.config):null,n.meet=this.instance.config.segmentation.enabled&&!this.models.meet&&((X=this.instance.config.segmentation.modelPath)!=null&&X.includes("meet"))?M5(this.instance.config):null,n.rvm=this.instance.config.segmentation.enabled&&!this.models.rvm&&((U=this.instance.config.segmentation.modelPath)!=null&&U.includes("rvm"))?F5(this.instance.config):null;for(let[Y,p0]of Object.entries(n))p0!=null&&p0.then&&p0.then(P=>this.models[Y]=P);await Promise.all(Object.values(n))}list(){let t=Object.keys(this.models).map(n=>{var o;return{name:n,loaded:this.models[n]!==null,size:0,url:this.models[n]?(o=this.models[n])==null?void 0:o.modelUrl:null}});for(let n of t){let o=Object.keys(l0).find(s=>s.startsWith(n.name));o&&(n.size=l0[o].sizeLoadedWeights,n.url=l0[o].url)}return t}loaded(){return this.list().filter(o=>o.loaded).map(o=>o.name)}validate(){let t=[];for(let n of Object.keys(this.models)){let o=this.models[n];if(!o)continue;let s=ot(this.instance,o,n);s&&t.push(s)}return t}};function Kn(e,t,n,o,s){var i,c,d,x,l,f;let A=0,a=[];for(let y of e){let p={id:A++,face:y,body:null,hands:{left:null,right:null},gestures:[],box:[0,0,0,0]};for(let h of t)y.box[0]>h.box[0]&&y.box[0]<h.box[0]+h.box[2]&&y.box[1]+y.box[3]>h.box[1]&&y.box[1]+y.box[3]<h.box[1]+h.box[3]&&(p.body=h);if(p.body)for(let h of n)h.box[0]+h.box[2]>p.body.box[0]&&h.box[0]+h.box[2]<p.body.box[0]+p.body.box[2]&&h.box[1]+h.box[3]>p.body.box[1]&&h.box[1]+h.box[3]<p.body.box[1]+p.body.box[3]&&p.hands&&(p.hands.left=h),h.box[0]<p.body.box[0]+p.body.box[2]&&h.box[0]>p.body.box[0]&&h.box[1]+h.box[3]>p.body.box[1]&&h.box[1]+h.box[3]<p.body.box[1]+p.body.box[3]&&p.hands&&(p.hands.right=h);for(let h of o)(h.face!==void 0&&h.face===y.id||h.iris!==void 0&&h.iris===y.id||h.body!==void 0&&h.body===((i=p.body)==null?void 0:i.id)||h.hand!==void 0&&h.hand===((c=p.hands.left)==null?void 0:c.id)||h.hand!==void 0&&h.hand===((d=p.hands.right)==null?void 0:d.id))&&p.gestures.push(h);let m=[],b=[],v=h=>{h&&h.length===4&&(m.push(h[0],h[0]+h[2]),b.push(h[1],h[1]+h[3]))};v(p.face.box),v((x=p.body)==null?void 0:x.box),v((l=p.hands.left)==null?void 0:l.box),v((f=p.hands.right)==null?void 0:f.box);let T=Math.min(...m),u=Math.min(...b);p.box=[T,u,Math.max(...m)-T,Math.max(...b)-u],s!=null&&s[1]&&(s!=null&&s[2])&&(p.boxRaw=[p.box[0]/s[2],p.box[1]/s[1],p.box[2]/s[2],p.box[3]/s[1]]),a.push(p)}return a}var rt=`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==`,st=`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`;async function uA(e){let t=(s,A="application/octet-stream")=>fetch(`data:${A};base64,${s}`).then(a=>a.blob()),n,o;switch(e.config.warmup){case"face":n=await t(rt);break;case"body":case"full":n=await t(st);break;default:n=null}if(n){let s=await createImageBitmap(n);o=await e.detect(s,e.config),s.close()}return o}async function hA(e){return new Promise(t=>{let n;switch(e.config.warmup){case"face":n="data:image/jpeg;base64,"+rt;break;case"full":case"body":n="data:image/jpeg;base64,"+st;break;default:n=""}let o;if(typeof Image!="undefined")o=new Image;else if(M.Image)o=new M.Image;else{t(void 0);return}o.onload=async()=>{let s=D0(o.naturalWidth,o.naturalHeight);if(!s)g("Warmup: Canvas not found"),t(void 0);else{let A=s.getContext("2d");A&&A.drawImage(o,0,0);let a=await e.image(s,!0),i=a.tensor?await e.detect(a.tensor,e.config):void 0;t(i)}},n?o.src=n:t(void 0)})}async function bA(e){let t=s=>Buffer.from(s,"base64"),n;e.config.warmup==="face"?n=t(rt):n=t(st);let o;if("node"in r&&r.getBackend()==="tensorflow"){let s=it.decodeJpeg(n),A=r.expandDims(s,0);e.tf.dispose(s),o=await e.detect(A,e.config),e.tf.dispose(A)}else e.config.debug&&g("Warmup tfjs-node not loaded");return o}async function gA(e){let t;return typeof createImageBitmap=="function"?t=await uA(e):typeof Image!="undefined"||M.Canvas!==void 0?t=await hA(e):t=await bA(e),t}async function TA(e){var i,c,d,x;if(!r.env().flagRegistry.ENGINE_COMPILE_ONLY)return;let t=r.getBackend(),n=r.backend();if(t!=="webgl"&&t!=="humangl"||!(n!=null&&n.checkCompileCompletion))return;r.env().set("ENGINE_COMPILE_ONLY",!0);let o=r.engine().state.numTensors,s=[];for(let[l,f]of Object.entries(e.models.models)){if(!f)continue;let y=f!=null&&f.modelSignature&&((c=(i=f==null?void 0:f.inputs)==null?void 0:i[0])!=null&&c.shape)?[...f.inputs[0].shape]:[1,64,64,3],p=f!=null&&f.modelSignature&&((x=(d=f==null?void 0:f.inputs)==null?void 0:d[0])!=null&&x.dtype)?f.inputs[0].dtype:"float32";for(let b=0;b<y.length;b++)y[b]===-1&&(y[b]=b===0?1:64);let m=r.zeros(y,p);try{let b=f.execute(m);s.push(l),Array.isArray(b)?b.forEach(v=>r.dispose(v)):r.dispose(b)}catch(b){e.config.debug&&g("compile fail model:",l)}r.dispose(m)}let A=await n.checkCompileCompletionAsync();n.getUniformLocations(),e.config.debug&&g("compile pass:",{models:s,kernels:A.length}),r.env().set("ENGINE_COMPILE_ONLY",!1);let a=r.engine().state.numTensors;a-o>0&&g("tensor leak:",a-o)}async function Jn(e,t){await e2(e,!1);let n=R();return e.state="warmup",t&&(e.config=Q(e.config,t)),!e.config.warmup||e.config.warmup.length===0||e.config.warmup==="none"?ne():new Promise(async o=>{await e.models.load(),await r.ready(),await TA(e);let s=await gA(e),A=R();e.config.debug&&g("warmup",e.config.warmup,Math.round(A-n),"ms"),e.emit("warmup"),o(s)})}var Ye,l2,c2,At,be,Qn=class{constructor(t){z(this,"version");z(this,"config");z(this,"result");z(this,"state");z(this,"process");z(this,"tf");z(this,"env",M);z(this,"draw",vt);z(this,"match",k5);z(this,"models");z(this,"events");z(this,"faceTriangulation");z(this,"faceUVMap");z(this,"performance");q0(this,Ye);q0(this,l2);q0(this,c2);z(this,"analyze",(...t)=>{if(!E0(this,l2))return;let n=this.tf.engine().state.numTensors,o=E0(this,Ye);_0(this,Ye,n);let s=n-o;s!==0&&g(...t,s)});q0(this,At,t=>{if(!E0(this,c2))return null;if(!t)return"input is not defined";if(this.env.node&&!(t instanceof r.Tensor))return"input must be a tensor";try{this.tf.getBackend()}catch(n){return"backend not loaded"}return null});z(this,"webcam",new u2);z(this,"emit",t=>{var n;(n=this.events)!=null&&n.dispatchEvent&&this.events.dispatchEvent(new Event(t))});q0(this,be,{});let n=(Je.tfjs||r.version_core).replace(/-(.*)/,"");Ce.wasmPath=`https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-wasm@${n}/dist/`,Ce.modelBasePath=M.browser?"../models/":"file://models/",this.version=ft,Object.defineProperty(this,"version",{value:ft}),this.config=JSON.parse(JSON.stringify(Ce)),Object.seal(this.config),this.config.cacheModels=typeof indexedDB!="undefined",t&&(this.config=Q(this.config,t)),s1(this.config),this.tf=r,this.state="idle",_0(this,Ye,0),_0(this,l2,!1),_0(this,c2,!1),this.performance={},this.events=typeof EventTarget!="undefined"?new EventTarget:void 0,this.models=new i2(this),Tt(),this.result=ne(),this.process={tensor:null,canvas:null},this.faceTriangulation=_1,this.faceUVMap=$1,ot(this,null,""),this.emit("create"),(this.config.debug||this.env.browser)&&g(`version: ${this.version}`),this.config.debug&&g(`tfjs version: ${this.tf.version["tfjs-core"]}`);let o=JSON.parse(JSON.stringify(this.env));delete o.kernels,delete o.initial,delete o.perfadd,this.config.debug&&g("environment:",o)}reset(){let t=this.config.backend;this.config=JSON.parse(JSON.stringify(Ce)),this.config.backend=t,xt(),M.initial=!0}validate(t){let n=lt(Ce,t||this.config);return n.length===0&&(this.config=Q(this.config,t)),n}now(){return R()}image(t,n=!1){return f2(t,this.config,n)}async segmentation(t,n){var A,a,i;if(n&&(this.config=Q(this.config,n)),!this.config.segmentation.enabled)return null;let o=await f2(t,this.config);if(!o.tensor)return null;let s=null;return(A=this.config.segmentation.modelPath)!=null&&A.includes("rvm")&&(s=await Zn(o.tensor,this.config)),(a=this.config.segmentation.modelPath)!=null&&a.includes("meet")&&(s=await vn(o.tensor,this.config)),(i=this.config.segmentation.modelPath)!=null&&i.includes("selfie")&&(s=await qn(o.tensor,this.config)),r.dispose(o.tensor),s}compare(t,n){return r1(this.config,t,n)}async init(){await e2(this,!0),await this.tf.ready(),xt()}async load(t){this.state="load";let n=R(),o=Object.values(this.models.models).filter(a=>a).length;t&&(this.config=Q(this.config,t)),this.env.initial&&(await e2(this,!1)||g("error: backend check failed"),await r.ready(),this.env.browser&&(this.config.debug&&g("configuration:",this.config),this.config.debug&&g("tf flags:",this.tf.ENV.flags))),await this.models.load(this),this.env.initial&&this.config.debug&&g("tf engine state:",this.tf.engine().state.numBytes,"bytes",this.tf.engine().state.numTensors,"tensors"),this.env.initial=!1,Object.values(this.models.models).filter(a=>a).length!==o&&(this.models.validate(),this.emit("load"));let A=Math.trunc(R()-n);A>(this.performance.loadModels||0)&&(this.performance.loadModels=this.env.perfadd?(this.performance.loadModels||0)+A:A)}next(t=this.result){return Tn(t,this.config)}async warmup(t){let n=R(),o=await Jn(this,t),s=R();return this.performance.warmup=Math.trunc(s-n),o}async profile(t,n){let o=await this.tf.profile(()=>this.detect(t,n)),s={},A=0;for(let i of o.kernels){let c=Number(i.kernelTimeMs)||0;s[i.name]?s[i.name]+=c:s[i.name]=c,A+=c}let a=[];Object.entries(s).forEach(i=>a.push({kernel:i[0],time:i[1],perc:0}));for(let i of a)i.perc=Math.round(1e3*i.time/A)/1e3,i.time=Math.round(1e3*i.time)/1e3;return a.sort((i,c)=>c.time-i.time),a.length=20,a}async detect(t,n){return this.state="detect",new Promise(async o=>{var b,v,T,u,h,w,k,I,C,V,W,G,X,U,Y,p0,P,i0,g0,e0,Z;this.state="config";let s;this.config=Q(this.config,n),this.state="check";let A=E0(this,At).call(this,t);A&&(g(A,t),this.emit("error"),o(ne(A)));let a=R();await this.load(),s=R(),this.state="image";let i=await f2(t,this.config);if(this.process=i,this.performance.inputProcess=this.env.perfadd?(this.performance.inputProcess||0)+Math.trunc(R()-s):Math.trunc(R()-s),this.analyze("Get Image:"),!i.tensor){this.config.debug&&g("could not convert input to tensor"),this.emit("error"),o(ne("could not convert input to tensor"));return}this.emit("image"),s=R(),this.config.skipAllowed=await o1(this.config,i.tensor),this.config.filter.autoBrightness=(this.config.filter.autoBrightness||!1)&&this.config.skipAllowed,this.performance.totalFrames||(this.performance.totalFrames=0),this.performance.cachedFrames||(this.performance.cachedFrames=0),this.performance.totalFrames++,this.config.skipAllowed&&this.performance.cachedFrames++,this.performance.cacheCheck=this.env.perfadd?(this.performance.cacheCheck||0)+Math.trunc(R()-s):Math.trunc(R()-s),this.analyze("Check Changed:");let c=[],d=[],x=[],l=[];this.state="detect:face",this.config.async?(c=this.config.face.enabled?c5(this,i.tensor):[],this.performance.face&&delete this.performance.face):(s=R(),c=this.config.face.enabled?await c5(this,i.tensor):[],this.performance.face=this.env.perfadd?(this.performance.face||0)+Math.trunc(R()-s):Math.trunc(R()-s)),this.config.async&&(this.config.body.maxDetected===-1||this.config.hand.maxDetected===-1)&&(c=await c),this.analyze("Start Body:"),this.state="detect:body";let f=this.config.body.maxDetected===-1?Q(this.config,{body:{maxDetected:this.config.face.enabled?1*c.length:1}}):this.config;this.config.async?((b=this.config.body.modelPath)!=null&&b.includes("posenet")?d=this.config.body.enabled?C5(i.tensor,f):[]:(v=this.config.body.modelPath)!=null&&v.includes("blazepose")?d=this.config.body.enabled?wt(i.tensor,f):[]:(T=this.config.body.modelPath)!=null&&T.includes("efficientpose")?d=this.config.body.enabled?Lt(i.tensor,f):[]:(u=this.config.body.modelPath)!=null&&u.includes("movenet")&&(d=this.config.body.enabled?z5(i.tensor,f):[]),this.performance.body&&delete this.performance.body):(s=R(),(h=this.config.body.modelPath)!=null&&h.includes("posenet")?d=this.config.body.enabled?await C5(i.tensor,f):[]:(w=this.config.body.modelPath)!=null&&w.includes("blazepose")?d=this.config.body.enabled?await wt(i.tensor,f):[]:(k=this.config.body.modelPath)!=null&&k.includes("efficientpose")?d=this.config.body.enabled?await Lt(i.tensor,f):[]:(I=this.config.body.modelPath)!=null&&I.includes("movenet")&&(d=this.config.body.enabled?await z5(i.tensor,f):[]),this.performance.body=this.env.perfadd?(this.performance.body||0)+Math.trunc(R()-s):Math.trunc(R()-s)),this.analyze("End Body:"),this.analyze("Start Hand:"),this.state="detect:hand";let y=this.config.hand.maxDetected===-1?Q(this.config,{hand:{maxDetected:this.config.face.enabled?2*c.length:1}}):this.config;this.config.async?((V=(C=this.config.hand.detector)==null?void 0:C.modelPath)!=null&&V.includes("handdetect")?x=this.config.hand.enabled?p5(i.tensor,y):[]:(G=(W=this.config.hand.detector)==null?void 0:W.modelPath)!=null&&G.includes("handtrack")&&(x=this.config.hand.enabled?b5(i.tensor,y):[]),this.performance.hand&&delete this.performance.hand):(s=R(),(U=(X=this.config.hand.detector)==null?void 0:X.modelPath)!=null&&U.includes("handdetect")?x=this.config.hand.enabled?await p5(i.tensor,y):[]:(p0=(Y=this.config.hand.detector)==null?void 0:Y.modelPath)!=null&&p0.includes("handtrack")&&(x=this.config.hand.enabled?await b5(i.tensor,y):[]),this.performance.hand=this.env.perfadd?(this.performance.hand||0)+Math.trunc(R()-s):Math.trunc(R()-s)),this.analyze("End Hand:"),this.analyze("Start Object:"),this.state="detect:object",this.config.async?((P=this.config.object.modelPath)!=null&&P.includes("nanodet")?l=this.config.object.enabled?j5(i.tensor,this.config):[]:(i0=this.config.object.modelPath)!=null&&i0.includes("centernet")&&(l=this.config.object.enabled?St(i.tensor,this.config):[]),this.performance.object&&delete this.performance.object):(s=R(),(g0=this.config.object.modelPath)!=null&&g0.includes("nanodet")?l=this.config.object.enabled?await j5(i.tensor,this.config):[]:(e0=this.config.object.modelPath)!=null&&e0.includes("centernet")&&(l=this.config.object.enabled?await St(i.tensor,this.config):[]),this.performance.object=this.env.perfadd?(this.performance.object||0)+Math.trunc(R()-s):Math.trunc(R()-s)),this.analyze("End Object:"),this.state="detect:await",this.config.async&&([c,d,x,l]=await Promise.all([c,d,x,l])),this.state="detect:gesture";let p=[];this.config.gesture.enabled&&(s=R(),p=[...J3(c),...K3(d),..._3(x),...Q3(c)],this.config.async?this.performance.gesture&&delete this.performance.gesture:this.performance.gesture=this.env.perfadd?(this.performance.gesture||0)+Math.trunc(R()-s):Math.trunc(R()-s)),this.performance.total=this.env.perfadd?(this.performance.total||0)+Math.trunc(R()-a):Math.trunc(R()-a);let m=((Z=this.process.tensor)==null?void 0:Z.shape)||[0,0,0,0];this.result={face:c,body:d,hand:x,gesture:p,object:l,performance:this.performance,canvas:this.process.canvas,timestamp:Date.now(),error:null,width:m[2],height:m[1],get persons(){return Kn(c,d,x,p,m)}},r.dispose(i.tensor),this.emit("detect"),this.state="idle",o(this.result)})}async sleep(t){return new Promise(n=>{setTimeout(n,t)})}async video(t,n=!0,o=0){n?(E0(this,be)[t.id]||(this.config.debug&&g("video start",t.id),E0(this,be)[t.id]=!0),!t.paused&&E0(this,be)[t.id]&&t.readyState>=2&&await this.detect(t),o>0&&await this.sleep(o),E0(this,be)[t.id]&&requestAnimationFrame(()=>this.video(t,n,o))):(this.config.debug&&g("video stop",t.id),E0(this,be)[t.id]=!1)}};Ye=new WeakMap,l2=new WeakMap,c2=new WeakMap,At=new WeakMap,be=new WeakMap;export{p2 as Env,Qn as Human,Qn as default,Ce as defaults,vt as draw,ne as empty,M as env,k5 as match,Yn as models};
