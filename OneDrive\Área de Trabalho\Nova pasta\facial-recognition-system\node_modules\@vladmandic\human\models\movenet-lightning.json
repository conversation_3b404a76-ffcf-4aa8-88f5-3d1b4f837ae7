{"format": "graph-model", "generatedBy": "https://tfhub.dev/google/movenet/singlepose/lightning/4", "convertedBy": "https://github.com/vladmandic", "signature": {"inputs": {"input:0": {"name": "input:0", "dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}, {"size": "192"}, {"size": "192"}, {"size": "3"}]}}}, "outputs": {"Identity:0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "17"}, {"size": "3"}]}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/ExpandDims_2/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/separable_conv2d/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/separable_conv2d/ReadVariableOp_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/kpt_offset_0/conv2d_7/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "34"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_offset_0/conv2d_7/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "34"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/Reshape_11/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "StatefulPartitionedCall/stack_4", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "17"}, {"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/ExpandDims_3/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/ExpandDims_4/dim", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/Reshape_13", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1"}, {"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/Reshape_8", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "17"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/Reshape_9/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/separable_conv2d/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/separable_conv2d/ReadVariableOp_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/conv2d_5/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "17"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/conv2d_5/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "17"}]}}}}}, {"name": "StatefulPartitionedCall/strided_slice_3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}, {"size": "48"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/strided_slice_4", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}, {"size": "48"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/separable_conv2d/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/separable_conv2d/ReadVariableOp_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_regress_0/conv2d_6/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "34"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_regress_0/conv2d_6/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "34"}]}}}}}, {"name": "StatefulPartitionedCall/Reshape_3", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/Reshape_4/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1280"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_1/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_2/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_2/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_2/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/ResizeImage/resize/ExpandDims/dim", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/Reshape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1"}, {"size": "3"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/truediv", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1"}, {"size": "3"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sub_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_3/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_3/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_0/separable_conv2d_3/separable_conv2d/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_0/separable_conv2d_3/separable_conv2d/ReadVariableOp_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/center_0/separable_conv2d_3/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/center_0/conv2d_4/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_0/conv2d_4/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "ConstantFolding/StatefulPartitionedCall/truediv_2_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}, {"size": "48"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/Reshape_2/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/ArgMax/dimension", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/floordiv/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/mul/y", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/Reshape_5/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/Reshape_6/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/add_5/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/Reshape_7/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "StatefulPartitionedCall/ArgMax_1/dimension", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/floordiv_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/Reshape_10/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/ExpandDims_5", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "17"}, {"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/ExpandDims_6/dim", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/ExpandDims_7/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/ExpandDims_8/dim", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/concat_1/axis", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "input", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "1"}, {"size": "192"}, {"size": "192"}, {"size": "3"}]}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv1/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv1/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "144"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "144"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "144"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "144"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "144"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "144"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "192"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "192"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "192"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "192"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "192"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "384"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "384"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "384"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "384"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "576"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "576"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "576"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "576"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "576"}, {"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "576"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "576"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "576"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "576"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "576"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "576"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "576"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "576"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "576"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "576"}, {"size": "160"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_project/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "160"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_expand/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "160"}, {"size": "960"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "960"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_depthwise/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "960"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "960"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_project/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "960"}, {"size": "160"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "160"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "160"}, {"size": "960"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_expand/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "960"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "960"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "960"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "960"}, {"size": "160"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "160"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_expand/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "160"}, {"size": "960"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_expand/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "960"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_depthwise/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "960"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_depthwise/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "960"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_project/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "960"}, {"size": "320"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_project/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "320"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv_1/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "320"}, {"size": "1280"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv_1/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1280"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/Cast", "op": "Cast", "input": ["input"], "attr": {"DstT": {"type": "DT_FLOAT"}, "SrcT": {"type": "DT_INT32"}, "Truncate": {"b": false}}}, {"name": "StatefulPartitionedCall/unstack", "op": "Unpack", "input": ["StatefulPartitionedCall/Cast"], "attr": {"T": {"type": "DT_FLOAT"}, "axis": {"i": "0"}, "num": {"i": "1"}}}, {"name": "StatefulPartitionedCall/ResizeImage/resize/ExpandDims", "op": "ExpandDims", "input": ["StatefulPartitionedCall/unstack", "StatefulPartitionedCall/ResizeImage/resize/ExpandDims/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/ResizeImage/resize/Squeeze", "op": "Squeeze", "input": ["StatefulPartitionedCall/ResizeImage/resize/ExpandDims"], "attr": {"T": {"type": "DT_FLOAT"}, "squeeze_dims": {"list": {"i": ["0"]}}}}, {"name": "ConstantFolding/StatefulPartitionedCall/stack_const_axis", "op": "Const", "input": ["^StatefulPartitionedCall/ResizeImage/resize/Squeeze"], "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/stack", "op": "ExpandDims", "input": ["StatefulPartitionedCall/ResizeImage/resize/Squeeze", "ConstantFolding/StatefulPartitionedCall/stack_const_axis"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sub", "op": "Sub", "input": ["StatefulPartitionedCall/stack", "StatefulPartitionedCall/Reshape"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/truediv_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/sub", "StatefulPartitionedCall/truediv"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sub_1", "op": "Sub", "input": ["StatefulPartitionedCall/truediv_1", "StatefulPartitionedCall/sub_1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv1_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/sub_1", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv1/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv1/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv1_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_depthwise/depthwise_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_depthwise/depthwise_bn_offset"], "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_depthwise/depthwise_bn_offset"], "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_3/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_3/Conv2D/ReadVariableOp", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_3/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_depthwise/depthwise_bn_offset"], "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_depthwise/depthwise_bn_offset"], "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_depthwise/depthwise_bn_offset"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_2/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_2/Conv2D/ReadVariableOp", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_2/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_depthwise/depthwise_bn_offset"], "attr": {"data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_depthwise/depthwise_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_depthwise/depthwise_bn_offset"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_depthwise/depthwise_bn_offset"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_1/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_1/Conv2D/ReadVariableOp", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_depthwise/depthwise_bn_offset"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_depthwise/depthwise_bn_offset"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_depthwise/depthwise_bn_offset"], "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_depthwise/depthwise_bn_offset"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_depthwise/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_project_BN/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_expand_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_add/add", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_expand/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_expand/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_depthwise/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_expand_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_depthwise/depthwise_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_depthwise/depthwise_bn_offset"], "attr": {"num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_project_BN/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_depthwise/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_project/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_project/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/out_relu/Relu6", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_project_BN/FusedBatchNormV3", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv_1/Conv2D_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv_1/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdTY="]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/out_relu/Relu6", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d/Conv2D/ReadVariableOp", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d/resize/ResizeBilinear", "op": "ResizeBilinear", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d/BiasAdd", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d/mul"], "attr": {"half_pixel_centers": {"b": true}, "T": {"type": "DT_FLOAT"}, "align_corners": {"b": false}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/tf.__operators__.add/AddV2", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d/resize/ResizeBilinear", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/tf.__operators__.add/AddV2", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d/ReadVariableOp"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/re_lu/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_1/resize/ResizeBilinear", "op": "ResizeBilinear", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/re_lu/Relu", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}, "half_pixel_centers": {"b": true}, "align_corners": {"b": false}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/tf.__operators__.add_1/AddV2", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_1/resize/ResizeBilinear", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_2/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/tf.__operators__.add_1/AddV2", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d/ReadVariableOp"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/re_lu_1/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_2/resize/ResizeBilinear", "op": "ResizeBilinear", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/re_lu_1/Relu", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_2/mul"], "attr": {"align_corners": {"b": false}, "half_pixel_centers": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/tf.__operators__.add_2/AddV2", "op": "AddV2", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_2/resize/ResizeBilinear", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_3/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/tf.__operators__.add_2/AddV2", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d/ReadVariableOp"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/re_lu_2/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d/depthwise", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d_weights", "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/re_lu_2/Relu", "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/separable_conv2d/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/re_lu_2/Relu", "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/separable_conv2d/ReadVariableOp"], "attr": {"padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/center_0/separable_conv2d_3/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/re_lu_2/Relu", "StatefulPartitionedCall/center_0/separable_conv2d_3/separable_conv2d/ReadVariableOp"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/re_lu_2/Relu", "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/separable_conv2d/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/re_lu_4/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/separable_conv2d/depthwise", "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/separable_conv2d/ReadVariableOp_1", "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "StatefulPartitionedCall/kpt_regress_0/re_lu_5/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/separable_conv2d/depthwise", "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/separable_conv2d/ReadVariableOp_1", "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/center_0/re_lu_3/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_0/separable_conv2d_3/separable_conv2d/depthwise", "StatefulPartitionedCall/center_0/separable_conv2d_3/separable_conv2d/ReadVariableOp_1", "StatefulPartitionedCall/center_0/separable_conv2d_3/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/kpt_offset_0/re_lu_6/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/separable_conv2d/depthwise", "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/separable_conv2d/ReadVariableOp_1", "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/conv2d_5/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/kpt_heatmap_0/re_lu_4/Relu", "StatefulPartitionedCall/kpt_heatmap_0/conv2d_5/Conv2D/ReadVariableOp", "StatefulPartitionedCall/kpt_heatmap_0/conv2d_5/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/kpt_regress_0/conv2d_6/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/kpt_regress_0/re_lu_5/Relu", "StatefulPartitionedCall/kpt_regress_0/conv2d_6/Conv2D/ReadVariableOp", "StatefulPartitionedCall/kpt_regress_0/conv2d_6/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/center_0/conv2d_4/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/center_0/re_lu_3/Relu", "StatefulPartitionedCall/center_0/conv2d_4/Conv2D/ReadVariableOp", "StatefulPartitionedCall/center_0/conv2d_4/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/kpt_offset_0/conv2d_7/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/kpt_offset_0/re_lu_6/Relu", "StatefulPartitionedCall/kpt_offset_0/conv2d_7/Conv2D/ReadVariableOp", "StatefulPartitionedCall/kpt_offset_0/conv2d_7/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/kpt_heatmap_0/conv2d_5/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/Sigmoid_1", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/center_0/conv2d_4/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/truediv_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/Sigmoid_1", "ConstantFolding/StatefulPartitionedCall/truediv_2_recip"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/Reshape_2", "op": "Reshape", "input": ["StatefulPartitionedCall/truediv_2", "StatefulPartitionedCall/Reshape_2/shape"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/ArgMax", "op": "ArgMax", "input": ["StatefulPartitionedCall/Reshape_2", "StatefulPartitionedCall/ArgMax/dimension"], "attr": {"T": {"type": "DT_FLOAT"}, "output_type": {"type": "DT_INT32"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/floordiv", "op": "FloorDiv", "input": ["StatefulPartitionedCall/ArgMax", "StatefulPartitionedCall/floordiv/y"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/Cast_4", "op": "Cast", "input": ["StatefulPartitionedCall/floordiv"], "attr": {"SrcT": {"type": "DT_INT32"}, "Truncate": {"b": false}, "DstT": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/Reshape_4", "op": "Reshape", "input": ["StatefulPartitionedCall/floordiv", "StatefulPartitionedCall/Reshape_4/shape"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/floordiv", "StatefulPartitionedCall/mul/y"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sub_4", "op": "Sub", "input": ["StatefulPartitionedCall/ArgMax", "StatefulPartitionedCall/mul"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/Cast_5", "op": "Cast", "input": ["StatefulPartitionedCall/sub_4"], "attr": {"DstT": {"type": "DT_FLOAT"}, "SrcT": {"type": "DT_INT32"}, "Truncate": {"b": false}}}, {"name": "StatefulPartitionedCall/Reshape_5", "op": "Reshape", "input": ["StatefulPartitionedCall/sub_4", "StatefulPartitionedCall/Reshape_5/shape"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/stack_2", "op": "Pack", "input": ["StatefulPartitionedCall/Reshape_3", "StatefulPartitionedCall/Reshape_4", "StatefulPartitionedCall/Reshape_5"], "attr": {"N": {"i": "3"}, "T": {"type": "DT_INT32"}, "axis": {"i": "1"}}}, {"name": "StatefulPartitionedCall/GatherNd", "op": "GatherNd", "input": ["StatefulPartitionedCall/kpt_regress_0/conv2d_6/BiasAdd", "StatefulPartitionedCall/stack_2"], "attr": {"Tparams": {"type": "DT_FLOAT"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/Reshape_6", "op": "Reshape", "input": ["StatefulPartitionedCall/GatherNd", "StatefulPartitionedCall/Reshape_6/shape"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/unstack_1", "op": "Unpack", "input": ["StatefulPartitionedCall/Reshape_6"], "attr": {"num": {"i": "2"}, "T": {"type": "DT_FLOAT"}, "axis": {"i": "1"}}}, {"name": "StatefulPartitionedCall/add_2", "op": "AddV2", "input": ["StatefulPartitionedCall/Cast_4", "StatefulPartitionedCall/unstack_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/add_3", "op": "AddV2", "input": ["StatefulPartitionedCall/Cast_5", "StatefulPartitionedCall/unstack_1:1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sub_5", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/strided_slice_3", "StatefulPartitionedCall/add_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sub_6", "op": "SquaredDifference", "input": ["StatefulPartitionedCall/strided_slice_4", "StatefulPartitionedCall/add_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/add_4", "op": "AddV2", "input": ["StatefulPartitionedCall/sub_5", "StatefulPartitionedCall/sub_6"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/Sqrt_1", "op": "Sqrt", "input": ["StatefulPartitionedCall/add_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/add_5", "op": "AddV2", "input": ["StatefulPartitionedCall/Sqrt_1", "StatefulPartitionedCall/add_5/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/truediv_3", "op": "RealDiv", "input": ["StatefulPartitionedCall/Sigmoid", "StatefulPartitionedCall/add_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/Reshape_7", "op": "Reshape", "input": ["StatefulPartitionedCall/truediv_3", "StatefulPartitionedCall/Reshape_7/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/ArgMax_1", "op": "ArgMax", "input": ["StatefulPartitionedCall/Reshape_7", "StatefulPartitionedCall/ArgMax_1/dimension"], "attr": {"Tidx": {"type": "DT_INT32"}, "output_type": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/floordiv_1", "op": "FloorDiv", "input": ["StatefulPartitionedCall/ArgMax_1", "StatefulPartitionedCall/floordiv_1/y"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/Cast_6", "op": "Cast", "input": ["StatefulPartitionedCall/floordiv_1"], "attr": {"DstT": {"type": "DT_FLOAT"}, "SrcT": {"type": "DT_INT32"}, "Truncate": {"b": false}}}, {"name": "StatefulPartitionedCall/Reshape_9", "op": "Reshape", "input": ["StatefulPartitionedCall/floordiv_1", "StatefulPartitionedCall/Reshape_9/shape"], "attr": {"T": {"type": "DT_INT32"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/floordiv_1", "StatefulPartitionedCall/mul_1/y"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sub_7", "op": "Sub", "input": ["StatefulPartitionedCall/ArgMax_1", "StatefulPartitionedCall/mul_1"], "attr": {"T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/Cast_7", "op": "Cast", "input": ["StatefulPartitionedCall/sub_7"], "attr": {"Truncate": {"b": false}, "SrcT": {"type": "DT_INT32"}, "DstT": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/Reshape_10", "op": "Reshape", "input": ["StatefulPartitionedCall/sub_7", "StatefulPartitionedCall/Reshape_10/shape"], "attr": {"T": {"type": "DT_INT32"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/stack_3", "op": "Pack", "input": ["StatefulPartitionedCall/Reshape_8", "StatefulPartitionedCall/Reshape_9", "StatefulPartitionedCall/Reshape_10"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "3"}, "axis": {"i": "1"}}}, {"name": "StatefulPartitionedCall/concat", "op": "ConcatV2", "input": ["StatefulPartitionedCall/stack_3", "StatefulPartitionedCall/ExpandDims_5", "StatefulPartitionedCall/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "N": {"i": "2"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/GatherNd_1", "op": "GatherNd", "input": ["StatefulPartitionedCall/kpt_offset_0/conv2d_7/BiasAdd", "StatefulPartitionedCall/stack_3"], "attr": {"Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/GatherNd_3", "op": "GatherNd", "input": ["StatefulPartitionedCall/Sigmoid", "StatefulPartitionedCall/concat"], "attr": {"Tparams": {"type": "DT_FLOAT"}, "Tindices": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/Reshape_11", "op": "Reshape", "input": ["StatefulPartitionedCall/GatherNd_1", "StatefulPartitionedCall/Reshape_11/shape"], "attr": {"Tshape": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/ExpandDims_6", "op": "ExpandDims", "input": ["StatefulPartitionedCall/GatherNd_3", "StatefulPartitionedCall/ExpandDims_6/dim"], "attr": {"T": {"type": "DT_FLOAT"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/GatherNd_2", "op": "GatherNd", "input": ["StatefulPartitionedCall/Reshape_11", "StatefulPartitionedCall/stack_4"], "attr": {"Tindices": {"type": "DT_INT32"}, "Tparams": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/ExpandDims_7", "op": "ExpandDims", "input": ["StatefulPartitionedCall/ExpandDims_6", "StatefulPartitionedCall/ExpandDims_7/dim"], "attr": {"T": {"type": "DT_FLOAT"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/unstack_2", "op": "Unpack", "input": ["StatefulPartitionedCall/GatherNd_2"], "attr": {"T": {"type": "DT_FLOAT"}, "axis": {"i": "1"}, "num": {"i": "2"}}}, {"name": "StatefulPartitionedCall/ExpandDims_8", "op": "ExpandDims", "input": ["StatefulPartitionedCall/ExpandDims_7", "StatefulPartitionedCall/ExpandDims_8/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/ExpandDims_2", "op": "ExpandDims", "input": ["StatefulPartitionedCall/unstack_2", "StatefulPartitionedCall/ExpandDims_2/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/ExpandDims_3", "op": "ExpandDims", "input": ["StatefulPartitionedCall/unstack_2:1", "StatefulPartitionedCall/ExpandDims_3/dim"], "attr": {"T": {"type": "DT_FLOAT"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/add_6", "op": "AddV2", "input": ["StatefulPartitionedCall/Cast_6", "StatefulPartitionedCall/ExpandDims_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/add_7", "op": "AddV2", "input": ["StatefulPartitionedCall/Cast_7", "StatefulPartitionedCall/ExpandDims_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/stack_5", "op": "Pack", "input": ["StatefulPartitionedCall/add_6", "StatefulPartitionedCall/add_7"], "attr": {"axis": {"i": "2"}, "N": {"i": "2"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/ExpandDims_4", "op": "ExpandDims", "input": ["StatefulPartitionedCall/stack_5", "StatefulPartitionedCall/ExpandDims_4/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/mul_2", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/ExpandDims_4", "StatefulPartitionedCall/Reshape_13"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/concat_1", "op": "ConcatV2", "input": ["StatefulPartitionedCall/mul_2", "StatefulPartitionedCall/ExpandDims_8", "StatefulPartitionedCall/concat_1/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "N": {"i": "2"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/concat_1"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {}}, "weightsManifest": [{"paths": ["movenet-lightning.bin"], "weights": [{"name": "StatefulPartitionedCall/ExpandDims_2/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/separable_conv2d/ReadVariableOp", "shape": [3, 3, 24, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/separable_conv2d/ReadVariableOp_1", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_offset_0/separable_conv2d_6/BiasAdd/ReadVariableOp", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_offset_0/conv2d_7/Conv2D/ReadVariableOp", "shape": [1, 1, 96, 34], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_offset_0/conv2d_7/BiasAdd/ReadVariableOp", "shape": [34], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/Reshape_11/shape", "shape": [3], "dtype": "int32"}, {"name": "StatefulPartitionedCall/stack_4", "shape": [17, 2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/ExpandDims_3/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/ExpandDims_4/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/Reshape_13", "shape": [1, 1, 1, 2], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/Reshape_8", "shape": [17], "dtype": "int32"}, {"name": "StatefulPartitionedCall/Reshape_9/shape", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/separable_conv2d/ReadVariableOp", "shape": [3, 3, 24, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/separable_conv2d/ReadVariableOp_1", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/separable_conv2d_4/BiasAdd/ReadVariableOp", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/conv2d_5/Conv2D/ReadVariableOp", "shape": [1, 1, 96, 17], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_heatmap_0/conv2d_5/BiasAdd/ReadVariableOp", "shape": [17], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/strided_slice_3", "shape": [48, 48, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/strided_slice_4", "shape": [48, 48, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/separable_conv2d/ReadVariableOp", "shape": [3, 3, 24, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/separable_conv2d/ReadVariableOp_1", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_regress_0/separable_conv2d_5/BiasAdd/ReadVariableOp", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_regress_0/conv2d_6/Conv2D/ReadVariableOp", "shape": [1, 1, 96, 34], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/kpt_regress_0/conv2d_6/BiasAdd/ReadVariableOp", "shape": [34], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/Reshape_3", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/Reshape_4/shape", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d/Conv2D/ReadVariableOp", "shape": [1, 1, 1280, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d/BiasAdd/ReadVariableOp", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d/mul", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_1/Conv2D/ReadVariableOp", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_1/BiasAdd/ReadVariableOp", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d/ReadVariableOp", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_1/mul", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_2/Conv2D/ReadVariableOp", "shape": [1, 1, 32, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_2/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d/ReadVariableOp", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/up_sampling2d_2/mul", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/ResizeImage/resize/ExpandDims/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/Reshape", "shape": [1, 1, 1, 3], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/truediv", "shape": [1, 1, 1, 3], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/sub_1/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_3/Conv2D/ReadVariableOp", "shape": [1, 1, 24, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/conv2d_3/BiasAdd/ReadVariableOp", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d/ReadVariableOp", "shape": [3, 3, 24, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_0/separable_conv2d_3/separable_conv2d/ReadVariableOp", "shape": [3, 3, 24, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_0/separable_conv2d_3/separable_conv2d/ReadVariableOp_1", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_0/separable_conv2d_3/BiasAdd/ReadVariableOp", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_0/conv2d_4/Conv2D/ReadVariableOp", "shape": [1, 1, 96, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_0/conv2d_4/BiasAdd/ReadVariableOp", "shape": [1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/truediv_2_recip", "shape": [48, 48, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/Reshape_2/shape", "shape": [3], "dtype": "int32"}, {"name": "StatefulPartitionedCall/ArgMax/dimension", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/floordiv/y", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/mul/y", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/Reshape_5/shape", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/Reshape_6/shape", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/add_5/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/Reshape_7/shape", "shape": [3], "dtype": "int32"}, {"name": "StatefulPartitionedCall/ArgMax_1/dimension", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/floordiv_1/y", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/mul_1/y", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/Reshape_10/shape", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/ExpandDims_5", "shape": [17, 1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/concat/axis", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/ExpandDims_6/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/ExpandDims_7/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/ExpandDims_8/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/concat_1/axis", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv1/Conv2D_weights", "shape": [3, 3, 3, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv1/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_depthwise/depthwise_weights", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_depthwise/depthwise_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_project/Conv2D_weights", "shape": [1, 1, 32, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/expanded_conv_project/Conv2D_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_expand/Conv2D_weights", "shape": [1, 1, 16, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_expand/Conv2D_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_depthwise/depthwise_weights", "shape": [3, 3, 96, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_depthwise/depthwise_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_project/Conv2D_weights", "shape": [1, 1, 96, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_1_project/Conv2D_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_expand/Conv2D_weights", "shape": [1, 1, 24, 144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_expand/Conv2D_bn_offset", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_depthwise/depthwise_weights", "shape": [3, 3, 144, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_depthwise/depthwise_bn_offset", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_project/Conv2D_weights", "shape": [1, 1, 144, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_2_project/Conv2D_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_expand/Conv2D_weights", "shape": [1, 1, 24, 144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_expand/Conv2D_bn_offset", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_depthwise/depthwise_weights", "shape": [3, 3, 144, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_depthwise/depthwise_bn_offset", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_project/Conv2D_weights", "shape": [1, 1, 144, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_3_project/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_expand/Conv2D_weights", "shape": [1, 1, 32, 192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_expand/Conv2D_bn_offset", "shape": [192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_depthwise/depthwise_weights", "shape": [3, 3, 192, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_depthwise/depthwise_bn_offset", "shape": [192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_project/Conv2D_weights", "shape": [1, 1, 192, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_4_project/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_expand/Conv2D_weights", "shape": [1, 1, 32, 192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_expand/Conv2D_bn_offset", "shape": [192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_depthwise/depthwise_weights", "shape": [3, 3, 192, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_depthwise/depthwise_bn_offset", "shape": [192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_project/Conv2D_weights", "shape": [1, 1, 192, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_5_project/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_expand/Conv2D_weights", "shape": [1, 1, 32, 192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_expand/Conv2D_bn_offset", "shape": [192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_depthwise/depthwise_weights", "shape": [3, 3, 192, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_depthwise/depthwise_bn_offset", "shape": [192], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_project/Conv2D_weights", "shape": [1, 1, 192, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_6_project/Conv2D_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_expand/Conv2D_weights", "shape": [1, 1, 64, 384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_expand/Conv2D_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_depthwise/depthwise_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_project/Conv2D_weights", "shape": [1, 1, 384, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_7_project/Conv2D_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_expand/Conv2D_weights", "shape": [1, 1, 64, 384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_expand/Conv2D_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_depthwise/depthwise_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_project/Conv2D_weights", "shape": [1, 1, 384, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_8_project/Conv2D_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_expand/Conv2D_weights", "shape": [1, 1, 64, 384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_expand/Conv2D_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_depthwise/depthwise_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_project/Conv2D_weights", "shape": [1, 1, 384, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_9_project/Conv2D_bn_offset", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_expand/Conv2D_weights", "shape": [1, 1, 64, 384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_expand/Conv2D_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_depthwise/depthwise_weights", "shape": [3, 3, 384, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_depthwise/depthwise_bn_offset", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_project/Conv2D_weights", "shape": [1, 1, 384, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_10_project/Conv2D_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_expand/Conv2D_weights", "shape": [1, 1, 96, 576], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_expand/Conv2D_bn_offset", "shape": [576], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_depthwise/depthwise_weights", "shape": [3, 3, 576, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_depthwise/depthwise_bn_offset", "shape": [576], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_project/Conv2D_weights", "shape": [1, 1, 576, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_11_project/Conv2D_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_expand/Conv2D_weights", "shape": [1, 1, 96, 576], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_expand/Conv2D_bn_offset", "shape": [576], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_depthwise/depthwise_weights", "shape": [3, 3, 576, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_depthwise/depthwise_bn_offset", "shape": [576], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_project/Conv2D_weights", "shape": [1, 1, 576, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_12_project/Conv2D_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_expand/Conv2D_weights", "shape": [1, 1, 96, 576], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_expand/Conv2D_bn_offset", "shape": [576], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_depthwise/depthwise_weights", "shape": [3, 3, 576, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_depthwise/depthwise_bn_offset", "shape": [576], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_project/Conv2D_weights", "shape": [1, 1, 576, 160], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_13_project/Conv2D_bn_offset", "shape": [160], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_expand/Conv2D_weights", "shape": [1, 1, 160, 960], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_expand/Conv2D_bn_offset", "shape": [960], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_depthwise/depthwise_weights", "shape": [3, 3, 960, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_depthwise/depthwise_bn_offset", "shape": [960], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_project/Conv2D_weights", "shape": [1, 1, 960, 160], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_14_project/Conv2D_bn_offset", "shape": [160], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_expand/Conv2D_weights", "shape": [1, 1, 160, 960], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_expand/Conv2D_bn_offset", "shape": [960], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_depthwise/depthwise_weights", "shape": [3, 3, 960, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_depthwise/depthwise_bn_offset", "shape": [960], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_project/Conv2D_weights", "shape": [1, 1, 960, 160], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_15_project/Conv2D_bn_offset", "shape": [160], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_expand/Conv2D_weights", "shape": [1, 1, 160, 960], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_expand/Conv2D_bn_offset", "shape": [960], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_depthwise/depthwise_weights", "shape": [3, 3, 960, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_depthwise/depthwise_bn_offset", "shape": [960], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_project/Conv2D_weights", "shape": [1, 1, 960, 320], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/block_16_project/Conv2D_bn_offset", "shape": [320], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv_1/Conv2D_weights", "shape": [1, 1, 320, 1280], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/model/Conv_1/Conv2D_bn_offset", "shape": [1280], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d_weights", "shape": [1, 1, 64, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d/separable_conv2d_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d_weights", "shape": [1, 1, 32, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_1/separable_conv2d_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d_weights", "shape": [1, 1, 24, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/center_net_mobile_net_v2fpn_feature_extractor/model_1/separable_conv2d_2/separable_conv2d_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/stack_const_axis", "shape": [], "dtype": "int32"}]}]}