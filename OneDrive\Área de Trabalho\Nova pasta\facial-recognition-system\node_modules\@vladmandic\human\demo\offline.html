<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <title>Human: Offline</title>
    <meta name="viewport" content="width=device-width, shrink-to-fit=yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="Human">
    <meta name="keywords" content="Human">
    <meta name="description" content="Human; Author: <PERSON> <<EMAIL>>">
    <meta name="msapplication-tooltip" content="Human; Author: <PERSON> <<EMAIL>>">
    <meta name="theme-color" content="#000000">
    <link rel="manifest" href="manifest.webmanifest">
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon">
    <link rel="icon" sizes="256x256" href="../assets/icon.png">
    <link rel="apple-touch-icon" href="../assets/icon.png">
    <link rel="apple-touch-startup-image" href="../assets/icon.png">
    <style>
      @font-face { font-family: 'Lato'; font-display: swap; font-style: normal; font-weight: 100; src: local('Lato'), url('../assets/lato-light.woff2') }
      body { font-family: 'Lato', 'Segoe UI'; font-size: 16px; font-variant: small-caps; background: black; color: #ebebeb; }
      h1 { font-size: 2rem; margin-top: 1.2rem; font-weight: bold; }
      a { color: white; }
      a:link { color: lightblue; text-decoration: none; }
      a:hover { color: lightskyblue; text-decoration: none; }
      .row { width: 90vw; margin: auto; margin-top: 100px; text-align: center; }
    </style>
  </head>
  <body>
    <div class="row text-center">
      <h1>
        <a href="/">Human: Offline</a><br>
        <img alt="icon" src="../assets/icon.png">
      </h1>
    </div>
  </body>
</html>
