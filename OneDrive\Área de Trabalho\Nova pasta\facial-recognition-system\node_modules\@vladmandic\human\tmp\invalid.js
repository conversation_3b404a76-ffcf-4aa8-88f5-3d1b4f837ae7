const fs = require('fs');
// const tf = require('@tensorflow/tfjs-node');
const Human = require('../dist/human.node').default;

const configuration = {
  modelBasePath: 'file://models/',
  backend: 'tensorflow',
  face: {
    enabled: true,
    detector: { maxDetected: 10 },
  },
  body: { enabled: false },
  hand: { enabled: false },
  gesture: { enabled: false },
  filter: { enabled: true, width: 2048 },
  cacheSensitivity: 0,
};
const human = new Human(configuration);

async function main() {
  await human.tf.ready();
  await human.load();
  const buffer = fs.readFileSync('tmp/image.gif'); // read file data into buffer
  const tensor = human.tf.node.decodeImage(buffer); // decode jpg data
  console.log(tensor);
  const res = await human.detect(tensor, configuration);
  console.log(res);
}

main();
