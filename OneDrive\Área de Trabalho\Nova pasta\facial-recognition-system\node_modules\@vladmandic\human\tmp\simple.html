<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Human</title>
  </head>
  <body>
    <script type="module">
      import { Human } from '../dist/human.esm.js';
      const config = {
        modelBasePath: '../models/',
        debug: true,
        cacheModels: false,
        face: { detector: { modelPath: 'wrong-path.json' } },
      };
      console.log(config);
      const human = new Human(config)
      await human.load();
      console.log(human.models.stats());
    </script>
  </body>
</html>
