# Sistema de Reconhecimento Facial - Human AI

Um sistema web completo de reconhecimento facial usando a biblioteca Human de vladimandic.

## 🚀 Características

- **Reconhecimento Facial em Tempo Real**: Detecção de múltiplas faces simultaneamente
- **Análise de Emoções**: Identifica 7 emoções básicas (feliz, triste, raiva, medo, surpresa, nojo, neutro)
- **Estimativa de Idade e Gênero**: Análise automática de características demográficas
- **Malha Facial 3D**: Visualização detalhada de pontos faciais
- **Captura de Fotos**: Salve momentos com as detecções sobrepostas
- **Interface Moderna**: Design responsivo e intuitivo
- **Configurações Personalizáveis**: Ajuste confiança, número máximo de faces e recursos
- **Logs em Tempo Real**: Monitoramento completo do sistema
- **Estatísticas de Performance**: FPS, tempo de processamento e contadores

## 🛠️ Tecnologias Utilizadas

- **Human AI** (vladimandic) - Biblioteca de IA para reconhecimento facial
- **WebGL** - Aceleração por GPU
- **MediaPipe** - Detector facial de alta precisão
- **Canvas API** - Renderização de sobreposições
- **WebRTC** - Acesso à câmera do usuário
- **ES6 Modules** - JavaScript moderno

## 📋 Pré-requisitos

- Node.js (versão 14 ou superior)
- Navegador moderno com suporte a WebRTC
- Câmera web funcional
- Conexão com internet (para carregar fontes e ícones)

## 🔧 Instalação

1. **Clone ou baixe o projeto**
   ```bash
   git clone [url-do-repositorio]
   cd facial-recognition-system
   ```

2. **Instale as dependências**
   ```bash
   npm install
   ```

3. **Inicie o servidor local**
   ```bash
   npm run dev
   ```

4. **Acesse no navegador**
   ```
   http://localhost:8080
   ```

## 📖 Como Usar

### Iniciando o Sistema

1. **Permissões**: Ao clicar em "Iniciar Câmera", permita o acesso à webcam
2. **Detecção Automática**: O sistema começará a detectar faces automaticamente
3. **Visualização**: Veja as detecções em tempo real com informações sobrepostas

### Controles Principais

- **🎥 Iniciar Câmera**: Ativa a webcam e inicia a detecção
- **⏹️ Parar**: Para a câmera e limpa todas as detecções
- **📷 Capturar Foto**: Salva uma imagem com as detecções atuais
- **⏸️ Pausar Detecção**: Pausa/retoma o processamento (mantém a câmera ativa)

### Configurações Avançadas

#### Confiança Mínima (0.1 - 1.0)
- Controla a sensibilidade da detecção
- Valores menores = mais detecções (menos precisas)
- Valores maiores = menos detecções (mais precisas)

#### Máximo de Faces (1 - 10)
- Define quantas faces podem ser detectadas simultaneamente
- Afeta a performance do sistema

#### Opções de Visualização
- **Mostrar Idade**: Exibe estimativa de idade
- **Mostrar Gênero**: Exibe classificação de gênero
- **Mostrar Emoção**: Exibe emoção dominante com percentual
- **Mostrar Malha Facial**: Exibe pontos de referência faciais

### Interpretando os Resultados

#### Painel de Status
- **Status**: Estado atual do sistema
- **FPS**: Quadros processados por segundo
- **Faces Detectadas**: Número atual de faces
- **Tempo de Processamento**: Latência em milissegundos

#### Resultados da Detecção
Para cada face detectada, você verá:
- **Confiança**: Certeza da detecção (0-100%)
- **Posição**: Coordenadas X,Y da face
- **Idade**: Estimativa em anos
- **Gênero**: Masculino/Feminino
- **Emoção**: Emoção dominante com percentual

## 🎯 Recursos Avançados

### Captura de Fotos
- Clique em "Capturar Foto" para salvar o frame atual
- As fotos incluem todas as detecções sobrepostas
- Timestamp automático para organização

### Sistema de Logs
- Monitore todas as ações do sistema
- Diferentes tipos de log (info, warning, error)
- Função de limpeza de logs

### Performance
- Otimizado para WebGL quando disponível
- Fallback automático para CPU se necessário
- Configurações ajustáveis para diferentes hardwares

## 🔧 Configuração Avançada

### Arquivo human-config.json
Personalize o comportamento do Human AI editando este arquivo:

```json
{
  "face": {
    "minConfidence": 0.5,
    "maxDetected": 5,
    "emotion": true,
    "age": true,
    "gender": true
  }
}
```

### Parâmetros Importantes
- `minConfidence`: Confiança mínima para detecção
- `maxDetected`: Número máximo de faces
- `emotion`: Ativar/desativar análise de emoções
- `age`: Ativar/desativar estimativa de idade
- `gender`: Ativar/desativar classificação de gênero

## 🚨 Solução de Problemas

### Câmera não funciona
- Verifique permissões do navegador
- Teste em HTTPS (necessário para algumas funcionalidades)
- Verifique se a câmera não está sendo usada por outro aplicativo

### Performance baixa
- Reduza o número máximo de faces
- Aumente a confiança mínima
- Desative recursos não necessários (malha facial, emoções)
- Verifique se WebGL está disponível

### Erros de carregamento
- Verifique conexão com internet
- Limpe cache do navegador
- Reinstale dependências: `npm install`

## 📱 Compatibilidade

### Navegadores Suportados
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### Dispositivos
- 💻 Desktop (Windows, macOS, Linux)
- 📱 Mobile (Android, iOS) - funcionalidade limitada
- 📹 Webcam integrada ou externa

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor:

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo LICENSE para detalhes.

## 🙏 Agradecimentos

- **vladimandic** pela excelente biblioteca Human AI
- **Google** pelo MediaPipe
- **Comunidade Open Source** pelas ferramentas utilizadas

## 📞 Suporte

Para dúvidas ou problemas:
- Abra uma issue no GitHub
- Consulte a documentação do Human AI
- Verifique os logs do sistema na interface

---

**Desenvolvido com ❤️ usando Human AI**
