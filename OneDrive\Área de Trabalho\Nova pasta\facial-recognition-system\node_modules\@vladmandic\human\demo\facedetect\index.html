<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Human</title>
    <!-- <meta http-equiv="content-type" content="text/html; charset=utf-8"> -->
    <meta name="viewport" content="width=device-width, shrink-to-fit=yes">
    <meta name="keywords" content="Human">
    <meta name="application-name" content="Human">
    <meta name="description" content="Human: 3D Face Detection, Body Pose, Hand & Finger Tracking, Iris Tracking, Age & Gender Prediction, Emotion Prediction & Gesture Recognition; Author: <PERSON> <https://github.com/vladmandic>">
    <meta name="msapplication-tooltip" content="Human: 3D Face Detection, Body Pose, Hand & Finger Tracking, Iris Tracking, Age & Gender Prediction, Emotion Prediction & Gesture Recognition; Author: <PERSON> <https://github.com/vladmandic>">
    <meta name="theme-color" content="#000000">
    <link rel="manifest" href="../manifest.webmanifest">
    <link rel="shortcut icon" href="../../favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="../../assets/icon.png">
    <script src="./facedetect.js" type="module"></script>
    <style>
      img { object-fit: contain; }
      img:hover { filter: grayscale(1); transform: scale(1.08); transition : all 0.3s ease; }   
      @font-face { font-family: 'Lato'; font-display: swap; font-style: normal; font-weight: 100; src: local('Lato'), url('../../assets/lato-light.woff2') }
      html { font-family: 'Lato', 'Segoe UI'; font-size: 24px; font-variant: small-caps; }
      body { margin: 24px; background: black; color: white; overflow: hidden; text-align: -webkit-center; width: 100vw; height: 100vh; }
      ::-webkit-scrollbar { height: 8px; border: 0; border-radius: 0; }
      ::-webkit-scrollbar-thumb { background: grey }
      ::-webkit-scrollbar-track { margin: 3px; }
      canvas { width: 192px; height: 192px; margin: 2px; padding: 2px; cursor: grab; transform: scale(1.00); transition : all 0.3s ease; }
      canvas:hover { filter: grayscale(1); transform: scale(1.08); transition : all 0.3s ease; }   
    </style>
  </head>
  <body>
    <component-loader></component-loader>
    <div style="display: flex">
      <div>
        <div style="margin: 24px">select image to show detected faces<br>drag & drop to add your images</div>
        <div id="images" style="display: flex; width: 98vw; overflow-x: auto; overflow-y: hidden; scroll-behavior: smooth"></div>
      </div>
    </div>
    <div id="list" style="height: 10px"></div>
    <div style="margin: 24px">hover or click on face to show details</div>
    <div id="faces" style="overflow-y: auto"></div>
    <div id="description" style="white-space: pre;"></div>
  </body>
</html>
