{"async": true, "warmup": "none", "filter": {"enabled": true, "equalization": true, "flip": false, "brightness": 0, "contrast": 0, "sharpness": 0, "blur": 0, "saturation": 0, "hue": 0, "negative": false, "sepia": false, "vintage": false, "kodachrome": false, "technicolor": false, "polaroid": false, "pixelate": 0}, "backend": "webgl", "modelBasePath": "./node_modules/@vladmandic/human/models/", "wasmPath": "./node_modules/@vladmandic/human/dist/", "face": {"enabled": true, "detector": "mediapipe", "mesh": true, "iris": true, "description": true, "emotion": true, "age": true, "gender": true, "liveness": false, "minConfidence": 0.5, "maxDetected": 5, "skipFrames": 0, "skipTime": 0, "iouThreshold": 0.4, "minSize": 64, "maxSize": 1024, "return": true}, "body": {"enabled": false, "modelPath": "movenet-multipose.json", "maxDetected": 1, "minConfidence": 0.3, "skipFrames": 0, "skipTime": 0}, "hand": {"enabled": false, "rotation": true, "skipFrames": 0, "skipTime": 0, "minConfidence": 0.5, "iouThreshold": 0.1, "maxDetected": 2, "landmarks": true, "detector": "full"}, "gesture": {"enabled": false, "minConfidence": 0.1}, "object": {"enabled": false, "modelPath": "nanodet.json", "minConfidence": 0.2, "iouThreshold": 0.4, "maxDetected": 10, "skipFrames": 0, "skipTime": 0}, "segmentation": {"enabled": false, "modelPath": "selfie.json", "mode": "default"}, "debug": false, "deallocate": true, "cacheSensitivity": 0.7, "videoOptimized": true, "scoped": false, "softwareKernels": false}